<?php

namespace Theme25\Backend\Controller\Setting\Setting;

/**
 * Sub-контролер за настройки за плащания
 *
 * Този контролер управлява логиката за показване и обработка на настройките за плащания,
 * включително payment методи, валути, комисионни и други настройки свързани с плащанията.
 *
 * @package Theme25\Backend\Controller\Setting\Setting
 * <AUTHOR> Development Team
 * @version 1.0.0
 */
class Payment extends \Theme25\ControllerSubMethods {

    /**
     * Подготвя данните за настройките за плащания
     */
    public function prepareData() {
        $this->preparePaymentMethodsData()
             ->prepareCurrencyData()
             ->preparePaymentSettingsData()
             ->prepareCommissionData()
             ->prepareUrlsAndActions()
             ->prepareValidationRules();

        // Подготвяне на JavaScript конфигурация
        $this->setData([
            'user_token' => $this->getUserToken()
        ]);
    }

    /**
     * Подготвя данните за payment методи
     *
     * @return $this За верижно извикване на методи
     */
    private function preparePaymentMethodsData() {
        try {
            $this->loadModelAs('setting/payment_settings_model', 'paymentSettings');
            $payment_methods = $this->paymentSettings->getAvailablePaymentMethods();
            
            $this->setData('payment_methods', $payment_methods);
            
        } catch (Exception $e) {
            $this->setError('Грешка при зареждане на payment методи: ' . $e->getMessage());
            
            // Задаване на стойности по подразбиране
            $this->setData('payment_methods', [
                'cod' => [
                    'name' => 'Наложен платеж',
                    'code' => 'cod',
                    'status' => 1,
                    'sort_order' => 1,
                    'icon' => 'ri-truck-line',
                    'description' => 'Плащане при доставка'
                ],
                'bank_transfer' => [
                    'name' => 'Банков превод',
                    'code' => 'bank_transfer',
                    'status' => 1,
                    'sort_order' => 2,
                    'icon' => 'ri-bank-line',
                    'description' => 'Директен банков превод'
                ],
                'paypal' => [
                    'name' => 'PayPal',
                    'code' => 'paypal',
                    'status' => 0,
                    'sort_order' => 3,
                    'icon' => 'ri-paypal-line',
                    'description' => 'Плащане чрез PayPal'
                ],
                'stripe' => [
                    'name' => 'Stripe',
                    'code' => 'stripe',
                    'status' => 0,
                    'sort_order' => 4,
                    'icon' => 'ri-bank-card-line',
                    'description' => 'Плащане с кредитна/дебитна карта'
                ]
            ]);
        }

        return $this;
    }

    /**
     * Подготвя данните за валути
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareCurrencyData() {
        try {
            $this->loadModelAs('localisation/currency', 'currencyModel');
            $currencies = $this->currencyModel->getCurrencies();
            
            $currency_options = [];
            foreach ($currencies as $currency) {
                $currency_options[] = [
                    'code' => $currency['code'],
                    'title' => $currency['title'],
                    'symbol_left' => $currency['symbol_left'],
                    'symbol_right' => $currency['symbol_right'],
                    'value' => $currency['value'],
                    'status' => $currency['status']
                ];
            }
            
            $this->setData('currencies', $currency_options);
            
            // Основна валута
            $default_currency = $this->config->get('config_currency');
            $this->setData('default_currency', $default_currency);
            
        } catch (Exception $e) {
            // Fallback данни
            $this->setData([
                'currencies' => [
                    ['code' => 'BGN', 'title' => 'Български лев', 'symbol_left' => '', 'symbol_right' => ' лв.', 'value' => 1.0000, 'status' => 1],
                    ['code' => 'EUR', 'title' => 'Евро', 'symbol_left' => '', 'symbol_right' => ' €', 'value' => 0.5113, 'status' => 1],
                    ['code' => 'USD', 'title' => 'Долар', 'symbol_left' => '$', 'symbol_right' => '', 'value' => 0.5500, 'status' => 1]
                ],
                'default_currency' => 'BGN'
            ]);
        }

        return $this;
    }

    /**
     * Подготвя основните настройки за плащания
     *
     * @return $this За верижно извикване на методи
     */
    private function preparePaymentSettingsData() {
        try {
            $this->loadModelAs('setting/payment_settings_model', 'paymentSettings');
            $settings = $this->paymentSettings->getPaymentSettings();
            
            // Добавяне на настройките към данните
            foreach ($settings as $key => $value) {
                $this->setData($key, $value);
            }
            
        } catch (Exception $e) {
            $this->setError('Грешка при зареждане на настройки за плащания: ' . $e->getMessage());
            
            // Задаване на стойности по подразбиране
            $this->setData([
                'payment_terms' => '',
                'payment_privacy_policy' => '',
                'payment_minimum_order' => 0,
                'payment_maximum_order' => 0,
                'payment_auto_currency_update' => 1,
                'payment_currency_update_frequency' => 'daily',
                'payment_failed_order_status' => 10,
                'payment_pending_order_status' => 1,
                'payment_processing_order_status' => 2,
                'payment_complete_order_status' => 5,
                'payment_refund_order_status' => 11,
                'payment_cancelled_order_status' => 7
            ]);
        }

        return $this;
    }

    /**
     * Подготвя данните за комисионни
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareCommissionData() {
        // Типове комисионни
        $commission_types = [
            'fixed' => 'Фиксирана сума',
            'percentage' => 'Процент от сумата',
            'mixed' => 'Смесена (фиксирана + процент)'
        ];

        $this->setData('commission_types', $commission_types);

        // Опции за честота на актуализиране на валутите
        $currency_update_options = [
            'manual' => 'Ръчно',
            'daily' => 'Ежедневно',
            'weekly' => 'Седмично',
            'monthly' => 'Месечно'
        ];

        $this->setData('currency_update_options', $currency_update_options);

        return $this;
    }

    /**
     * Подготвя URL-и за AJAX заявки и действия
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareUrlsAndActions() {
        $urls = [
            'save_url' => $this->getAdminLink('setting/setting/payment_save'),
            'test_payment_url' => $this->getAdminLink('setting/setting/test_payment_method'),
            'update_currencies_url' => $this->getAdminLink('setting/setting/update_currencies'),
            'toggle_payment_method_url' => $this->getAdminLink('setting/setting/toggle_payment_method'),
            'reorder_payment_methods_url' => $this->getAdminLink('setting/setting/reorder_payment_methods'),
            'export_payment_settings_url' => $this->getAdminLink('setting/setting/export_payment_settings'),
            'import_payment_settings_url' => $this->getAdminLink('setting/setting/import_payment_settings')
        ];

        foreach ($urls as $key => $url) {
            $this->setData($key, $url);
        }

        return $this;
    }

    /**
     * Подготвя правила за валидация
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareValidationRules() {
        $validation_rules = [
            'payment_minimum_order' => [
                'required' => false,
                'type' => 'number',
                'min' => 0
            ],
            'payment_maximum_order' => [
                'required' => false,
                'type' => 'number',
                'min' => 0
            ],
            'payment_terms' => [
                'required' => false,
                'max_length' => 1000,
                'type' => 'textarea'
            ],
            'payment_privacy_policy' => [
                'required' => false,
                'max_length' => 1000,
                'type' => 'textarea'
            ]
        ];

        $this->setData('validation_rules', $validation_rules);

        return $this;
    }

    /**
     * Получава статистики за плащанията
     *
     * @return array
     */
    private function getPaymentStatistics() {
        try {
            $this->loadModelAs('setting/payment_settings_model', 'paymentModel');
            
            return [
                'active_payment_methods' => $this->paymentModel->getActivePaymentMethodsCount(),
                'total_payment_methods' => $this->paymentModel->getTotalPaymentMethodsCount(),
                'supported_currencies' => $this->paymentModel->getSupportedCurrenciesCount(),
                'last_currency_update' => $this->paymentModel->getLastCurrencyUpdate()
            ];
            
        } catch (Exception $e) {
            return [
                'active_payment_methods' => 2,
                'total_payment_methods' => 4,
                'supported_currencies' => 3,
                'last_currency_update' => date('Y-m-d H:i:s')
            ];
        }
    }

    /**
     * Проверява дали има предупреждения за плащанията
     *
     * @return array
     */
    private function getPaymentWarnings() {
        $warnings = [];
        $settings = $this->getData();
        
        // Проверка за липсващи активни payment методи
        $payment_methods = $this->getData('payment_methods') ?: [];
        $active_methods = array_filter($payment_methods, function($method) {
            return !empty($method['status']);
        });
        
        if (empty($active_methods)) {
            $warnings[] = [
                'type' => 'error',
                'message' => 'Няма активни методи за плащане! Клиентите няма да могат да завършат поръчки.'
            ];
        }
        
        // Проверка за липсваща основна валута
        if (empty($settings['default_currency'])) {
            $warnings[] = [
                'type' => 'warning',
                'message' => 'Не е зададена основна валута'
            ];
        }
        
        // Проверка за остарели валутни курсове
        $last_update = $this->getPaymentStatistics()['last_currency_update'];
        if ($last_update && strtotime($last_update) < strtotime('-7 days')) {
            $warnings[] = [
                'type' => 'info',
                'message' => 'Валутните курсове не са актуализирани повече от седмица'
            ];
        }
        
        return $warnings;
    }

    /**
     * Получава конфигурацията за JavaScript модула
     *
     * @return array
     */
    public function getJavaScriptConfig() {
        return [
            'userToken' => $this->getUserToken(),
            'defaultCurrency' => $this->getData('default_currency'),
            'urls' => [
                'save' => $this->getData('save_url'),
                'testPayment' => $this->getData('test_payment_url'),
                'updateCurrencies' => $this->getData('update_currencies_url'),
                'togglePaymentMethod' => $this->getData('toggle_payment_method_url'),
                'reorderPaymentMethods' => $this->getData('reorder_payment_methods_url'),
                'exportSettings' => $this->getData('export_payment_settings_url'),
                'importSettings' => $this->getData('import_payment_settings_url')
            ],
            'validationRules' => $this->getData('validation_rules'),
            'commissionTypes' => $this->getData('commission_types'),
            'statistics' => $this->getPaymentStatistics()
        ];
    }
}
