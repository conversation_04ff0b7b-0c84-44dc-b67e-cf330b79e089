<?php

namespace Theme25\Backend\Model\Setting;

/**
 * Базов модел за работа с настройки
 * Предоставя общи методи за четене и запис на настройки в базата данни
 */
abstract class SettingModel extends \Theme25\ModelBackend {

    /**
     * Префикс за настройките (по подразбиране 'config')
     */
    protected $settingPrefix = 'config';

    /**
     * Store ID за настройките (по подразбиране 0)
     */
    protected $storeId = 0;

    public function __construct($registry) {
        parent::__construct($registry);
    }

    /**
     * Получава стойност на настройка
     * 
     * @param string $key Ключ на настройката
     * @param mixed $default Стойност по подразбиране
     * @return mixed
     */
    protected function getSetting($key, $default = null) {
        $fullKey = $this->settingPrefix . '_' . $key;
        return $this->config->get($fullKey) ?? $default;
    }

    /**
     * Задава стойност на настройка
     * 
     * @param string $key Ключ на настройката
     * @param mixed $value Стойност
     * @return bool
     */
    protected function setSetting($key, $value) {
        try {
            $this->loadModelAs('setting/setting', 'settingModel');
            $fullKey = $this->settingPrefix . '_' . $key;
            
            return $this->settingModel->editSettingValue($this->settingPrefix, $fullKey, $value, $this->storeId);
        } catch (Exception $e) {
            error_log('Setting save error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Получава множество настройки наведнъж
     * 
     * @param array $keys Масив с ключове
     * @return array Асоциативен масив с настройки
     */
    protected function getMultipleSettings($keys) {
        $settings = [];
        
        foreach ($keys as $key) {
            $settings[$key] = $this->getSetting($key);
        }
        
        return $settings;
    }

    /**
     * Запазва множество настройки наведнъж
     * 
     * @param array $settings Асоциативен масив с настройки
     * @return bool
     */
    protected function setMultipleSettings($settings) {
        try {
            $this->loadModelAs('setting/setting', 'settingModel');
            
            $settingsData = [];
            foreach ($settings as $key => $value) {
                $fullKey = $this->settingPrefix . '_' . $key;
                $settingsData[$fullKey] = $value;
            }
            
            $this->settingModel->editSetting($this->settingPrefix, $settingsData, $this->storeId);
            return true;
        } catch (Exception $e) {
            error_log('Multiple settings save error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Валидира настройка преди запис
     * 
     * @param string $key Ключ на настройката
     * @param mixed $value Стойност
     * @return bool|string True ако е валидна, иначе съобщение за грешка
     */
    protected function validateSetting($key, $value) {
        // Базова валидация - може да се предефинира в наследниците
        if ($value === null || $value === '') {
            return "Настройката '{$key}' не може да бъде празна";
        }
        
        return true;
    }

    /**
     * Получава всички настройки за текущия префикс
     * 
     * @return array
     */
    protected function getAllSettings() {
        $this->loadModelAs('setting/setting', 'settingModel');
        return $this->settingModel->getSetting($this->settingPrefix, $this->storeId);
    }

    /**
     * Изтрива настройка
     * 
     * @param string $key Ключ на настройката
     * @return bool
     */
    protected function deleteSetting($key) {
        try {
            $fullKey = $this->settingPrefix . '_' . $key;
            
            $sql = "DELETE FROM " . DB_PREFIX . "setting 
                    WHERE store_id = '" . (int)$this->storeId . "' 
                    AND `code` = '" . $this->db->escape($this->settingPrefix) . "' 
                    AND `key` = '" . $this->db->escape($fullKey) . "'";
            
            $this->db->query($sql);
            return true;
        } catch (Exception $e) {
            error_log('Setting delete error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Проверява дали настройка съществува
     * 
     * @param string $key Ключ на настройката
     * @return bool
     */
    protected function settingExists($key) {
        $fullKey = $this->settingPrefix . '_' . $key;
        
        $sql = "SELECT COUNT(*) as count FROM " . DB_PREFIX . "setting 
                WHERE store_id = '" . (int)$this->storeId . "' 
                AND `code` = '" . $this->db->escape($this->settingPrefix) . "' 
                AND `key` = '" . $this->db->escape($fullKey) . "'";
        
        $result = $this->db->query($sql);
        return $result->row['count'] > 0;
    }

    /**
     * Получава настройки с определен префикс
     * 
     * @param string $keyPrefix Префикс на ключовете
     * @return array
     */
    protected function getSettingsByPrefix($keyPrefix) {
        $fullPrefix = $this->settingPrefix . '_' . $keyPrefix;
        
        $sql = "SELECT `key`, `value`, `serialized` FROM " . DB_PREFIX . "setting 
                WHERE store_id = '" . (int)$this->storeId . "' 
                AND `code` = '" . $this->db->escape($this->settingPrefix) . "' 
                AND `key` LIKE '" . $this->db->escape($fullPrefix) . "%'";
        
        $result = $this->db->query($sql);
        $settings = [];
        
        foreach ($result->rows as $row) {
            $key = str_replace($this->settingPrefix . '_', '', $row['key']);
            
            if ($row['serialized']) {
                $settings[$key] = json_decode($row['value'], true);
            } else {
                $settings[$key] = $row['value'];
            }
        }
        
        return $settings;
    }

    /**
     * Валидира масив с настройки
     * 
     * @param array $settings Настройки за валидация
     * @return array Масив с грешки (празен ако няма грешки)
     */
    protected function validateMultipleSettings($settings) {
        $errors = [];
        
        foreach ($settings as $key => $value) {
            $validation = $this->validateSetting($key, $value);
            if ($validation !== true) {
                $errors[$key] = $validation;
            }
        }
        
        return $errors;
    }

    /**
     * Създава backup на настройките
     * 
     * @return array|false Backup данни или false при грешка
     */
    protected function createSettingsBackup() {
        try {
            $settings = $this->getAllSettings();
            $backup = [
                'timestamp' => time(),
                'prefix' => $this->settingPrefix,
                'store_id' => $this->storeId,
                'settings' => $settings
            ];
            
            return $backup;
        } catch (Exception $e) {
            error_log('Settings backup error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Възстановява настройки от backup
     * 
     * @param array $backup Backup данни
     * @return bool
     */
    protected function restoreSettingsFromBackup($backup) {
        try {
            if (!isset($backup['settings']) || !is_array($backup['settings'])) {
                return false;
            }
            
            return $this->setMultipleSettings($backup['settings']);
        } catch (Exception $e) {
            error_log('Settings restore error: ' . $e->getMessage());
            return false;
        }
    }
}
