<!-- sub Header -->
<header class="bg-white border-b border-gray-200" style="margin-top: -2px; position: relative;">
	<div class="flex border-b border-gray-200">
		<button id="tab-basic" class="tab-button active px-6 py-3 text-primary whitespace-nowrap">Основни настройки</button>
		<button id="tab-delivery" class="tab-button px-6 py-3 text-gray-600 hover:text-primary whitespace-nowrap">Доставка</button>
		<button id="tab-payment" class="tab-button px-6 py-3 text-gray-600 hover:text-primary whitespace-nowrap">Плащания</button>
		<button id="tab-notifications" class="tab-button px-6 py-3 text-gray-600 hover:text-primary whitespace-nowrap">Известия</button>
		<button id="tab-integrations" class="tab-button px-6 py-3 text-gray-600 hover:text-primary whitespace-nowrap">Интеграции</button>
		<button id="tab-security" class="tab-button px-6 py-3 text-gray-600 hover:text-primary whitespace-nowrap">Сигурност</button>
		<button id="tab-admin-users" class="tab-button px-6 py-3 text-gray-600 hover:text-primary whitespace-nowrap">Админ потребители</button>
	</div>
</header>
<!-- Main Content Area -->
<main
	class="flex-1 overflow-y-auto p-6 bg-gray-50">
	<!-- Basic Settings Tab -->
	<div id="content-basic" class="tab-content">
		<div class="mb-6">
			<h1 class="text-2xl font-bold text-gray-800">Основни настройки</h1>
			<p class="text-gray-600 mt-1">Управлявайте основната информация за вашия магазин</p>
		</div>
		<div
			class="grid grid-cols-1 lg:grid-cols-3 gap-6">
			<!-- Store Information -->
			<div class="lg:col-span-2">
				<div class="bg-white rounded shadow p-6 settings-card">
					<h2 class="text-lg font-semibold text-gray-800 mb-4">Информация за магазина</h2>
					<div class="space-y-4">
						<div>
							<label for="store-name" class="block text-sm font-medium text-gray-700 mb-1">Име на магазина</label>
							<input type="text" id="store-name" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" value="Rakla">
						</div>
						<div>
							<label class="block text-sm font-medium text-gray-700 mb-1">Лого на магазина</label>
							<div class="flex items-center space-x-4">
								<div class="w-16 h-16 bg-gray-100 rounded flex items-center justify-center">
									<img src="https://www.rakla.bg/image/rakla-logo.svg" alt="Store Logo" class="max-w-full max-h-full">
								</div>
								<div>
									<button class="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90 transition-colors whitespace-nowrap !rounded-button">Промяна</button>
									<p class="text-xs text-gray-500 mt-1">Препоръчителен размер: 200x60 пиксела</p>
								</div>
							</div>
						</div>
						<div>
							<label for="store-email" class="block text-sm font-medium text-gray-700 mb-1">Имейл адрес</label>
							<input type="email" id="store-email" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" value="<EMAIL>">
						</div>
						<div>
							<label for="store-phone" class="block text-sm font-medium text-gray-700 mb-1">Телефон</label>
							<input type="tel" id="store-phone" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" value="+359 88 123 4567">
						</div>
						<div>
							<label for="store-address" class="block text-sm font-medium text-gray-700 mb-1">Адрес</label>
							<textarea id="store-address" rows="3" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary">ул. Иван Вазов 12, 1000 София, България</textarea>
						</div>
					</div>
				</div>
				<div class="bg-white rounded shadow p-6 mt-6 settings-card">
					<h2 class="text-lg font-semibold text-gray-800 mb-4">Социални мрежи</h2>
					<div class="space-y-4">
						<div class="flex items-center">
							<div class="w-10 h-10 flex items-center justify-center text-blue-600 mr-2">
								<i class="ri-facebook-fill ri-lg"></i>
							</div>
							<input type="text" class="flex-1 px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" placeholder="Facebook URL" value="https://facebook.com/rakla">
						</div>
						<div class="flex items-center">
							<div class="w-10 h-10 flex items-center justify-center text-pink-600 mr-2">
								<i class="ri-instagram-fill ri-lg"></i>
							</div>
							<input type="text" class="flex-1 px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" placeholder="Instagram URL" value="https://instagram.com/rakla">
						</div>
						<div class="flex items-center">
							<div class="w-10 h-10 flex items-center justify-center text-red-600 mr-2">
								<i class="ri-youtube-fill ri-lg"></i>
							</div>
							<input type="text" class="flex-1 px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" placeholder="YouTube URL" value="">
						</div>
						<div class="flex items-center">
							<div class="w-10 h-10 flex items-center justify-center text-blue-400 mr-2">
								<i class="ri-twitter-fill ri-lg"></i>
							</div>
							<input type="text" class="flex-1 px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" placeholder="Twitter URL" value="">
						</div>
					</div>
				</div>
			</div>
			<!-- Settings Sidebar -->
			<div>
				<div class="bg-white rounded shadow p-6 settings-card">
					<h2 class="text-lg font-semibold text-gray-800 mb-4">Общи настройки</h2>
					<div class="space-y-4">
						<div class="flex items-center justify-between">
							<div>
								<h3 class="text-sm font-medium text-gray-800">Поддръжка</h3>
								<p class="text-xs text-gray-500">Активиране на чат за поддръжка</p>
							</div>
							<label class="toggle-switch">
								<input type="checkbox" checked>
								<span class="toggle-slider"></span>
							</label>
						</div>
						<div class="flex items-center justify-between">
							<div>
								<h3 class="text-sm font-medium text-gray-800">Отзиви за продукти</h3>
								<p class="text-xs text-gray-500">Разрешаване на отзиви за продукти</p>
							</div>
							<label class="toggle-switch">
								<input type="checkbox" checked>
								<span class="toggle-slider"></span>
							</label>
						</div>
						<div class="flex items-center justify-between">
							<div>
								<h3 class="text-sm font-medium text-gray-800">Наличност</h3>
								<p class="text-xs text-gray-500">Показване на наличност на продукти</p>
							</div>
							<label class="toggle-switch">
								<input type="checkbox" checked>
								<span class="toggle-slider"></span>
							</label>
						</div>
						<div class="flex items-center justify-between">
							<div>
								<h3 class="text-sm font-medium text-gray-800">Режим за поддръжка</h3>
								<p class="text-xs text-gray-500">Временно затваряне на магазина</p>
							</div>
							<label class="toggle-switch">
								<input type="checkbox">
								<span class="toggle-slider"></span>
							</label>
						</div>
					</div>
				</div>
				<div class="bg-white rounded shadow p-6 mt-6 settings-card">
					<h2 class="text-lg font-semibold text-gray-800 mb-4">Валута и език</h2>
					<div class="space-y-4">
						<div>
							<label for="currency" class="block text-sm font-medium text-gray-700 mb-1">Основна валута</label>
							<div class="relative">
								<select id="currency" class="w-full px-4 py-2 border border-gray-300 rounded appearance-none focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary pr-8">
									<option value="BGN" selected>Български лев (BGN)</option>
									<option value="EUR">Евро (EUR)</option>
									<option value="USD">Щатски долар (USD)</option>
								</select>
								<div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none text-gray-500">
									<i class="ri-arrow-down-s-line"></i>
								</div>
							</div>
						</div>
						<div>
							<label for="language" class="block text-sm font-medium text-gray-700 mb-1">Основен език</label>
							<div class="relative">
								<select id="language" class="w-full px-4 py-2 border border-gray-300 rounded appearance-none focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary pr-8">
									<option value="bg" selected>Български</option>
									<option value="en">Английски</option>
								</select>
								<div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none text-gray-500">
									<i class="ri-arrow-down-s-line"></i>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="mt-6 flex justify-end">
			<button class="px-6 py-2 bg-primary text-white rounded hover:bg-primary/90 transition-colors whitespace-nowrap !rounded-button">Запази промените</button>
		</div>
	</div>
	<!-- Delivery Settings Tab -->
	<div id="content-delivery" class="tab-content hidden">
		<div class="mb-6">
			<h1 class="text-2xl font-bold text-gray-800">Настройки за доставка</h1>
			<p class="text-gray-600 mt-1">Управлявайте методите за доставка и техните настройки</p>
		</div>
		<div class="bg-white rounded shadow p-6 settings-card mb-6">
			<h2 class="text-lg font-semibold text-gray-800 mb-4">Методи за доставка</h2>
			<div
				class="space-y-6">
				
				<!-- Speedy -->
				<div class="flex items-start p-4 border border-gray-200 rounded">
					<div class="w-48 h-48 flex items-center justify-center mr-4">
						<img src="https://www.speedy.bg/uploads/file_manager_uploads/Pics/speedy-logo-220x98.png" alt="Speedy" class="max-w-full max-h-full">
					</div>
					<div class="flex-1">
						<div class="flex items-center justify-between">
							<h3 class="text-lg font-medium text-gray-800">Спиди</h3>
							<label class="toggle-switch">
								<input type="checkbox" checked>
								<span class="toggle-slider"></span>
							</label>
						</div>
						<p class="text-sm text-gray-600 mt-1">Доставка до офис или адрес чрез Спиди</p>
						<div class="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">Потребителско име</label>
								<input type="text" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" value="rakla_speedy">
							</div>
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">Парола</label>
								<input type="password" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" value="••••••••">
							</div>
						</div>
					</div>
				</div>
				
			</div>
		</div>
		<div class="bg-white rounded shadow p-6 settings-card">
			<h2 class="text-lg font-semibold text-gray-800 mb-4">Безплатна доставка</h2>
			<div class="space-y-4">
				<div class="flex items-center justify-between">
					<div>
						<h3 class="text-sm font-medium text-gray-800">Активиране на безплатна доставка</h3>
						<p class="text-xs text-gray-500">Включване на опция за безплатна доставка</p>
					</div>
					<label class="toggle-switch">
						<input type="checkbox" checked>
						<span class="toggle-slider"></span>
					</label>
				</div>
				<div>
					<label class="block text-sm font-medium text-gray-700 mb-1">Минимална сума за безплатна доставка</label>
					<div class="flex items-center">
						<input type="number" class="w-32 px-4 py-2 border border-gray-300 rounded-l focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" value="100">
						<span class="px-4 py-2 bg-gray-100 border border-l-0 border-gray-300 rounded-r">лв.</span>
					</div>
					<p class="text-xs text-gray-500 mt-1">Клиентите получават безплатна доставка при поръчка над тази сума</p>
				</div>
			</div>
		</div>
		<div class="mt-6 flex justify-end">
			<button class="px-6 py-2 bg-primary text-white rounded hover:bg-primary/90 transition-colors whitespace-nowrap !rounded-button">Запази промените</button>
		</div>
	</div>
	<!-- Payment Settings Tab -->
	<div id="content-payment" class="tab-content hidden">
		<div class="mb-6">
			<h1 class="text-2xl font-bold text-gray-800">Методи за плащане</h1>
			<p class="text-gray-600 mt-1">Управлявайте наличните методи за плащане</p>
		</div>
		<div class="bg-white rounded shadow p-6 settings-card mb-6">
			<h2 class="text-lg font-semibold text-gray-800 mb-4">Методи за плащане</h2>
			<div
				class="space-y-6">
				<!-- Cash on Delivery -->
				<div class="flex items-start p-4 border border-gray-200 rounded">
					<div class="w-12 h-12 flex items-center justify-center text-green-500">
						<i class="ri-money-dollar-box-fill ri-2x"></i>
					</div>
					<div class="flex-1">
						<div class="flex items-center justify-between">
							<h3 class="text-lg font-medium text-gray-800">Наложен платеж</h3>
							<label class="toggle-switch">
								<input type="checkbox" checked>
								<span class="toggle-slider"></span>
							</label>
						</div>
						<p class="text-sm text-gray-600 mt-1">Плащане при доставка</p>
					</div>
				</div>
				<!-- Bank Transfer -->
				<div class="flex items-start p-4 border border-gray-200 rounded">
					<div class="w-12 h-12 flex items-center justify-center text-blue-500">
						<i class="ri-bank-fill ri-2x"></i>
					</div>
					<div class="flex-1">
						<div class="flex items-center justify-between">
							<h3 class="text-lg font-medium text-gray-800">Банков превод</h3>
							<label class="toggle-switch">
								<input type="checkbox" checked>
								<span class="toggle-slider"></span>
							</label>
						</div>
						<p class="text-sm text-gray-600 mt-1">Плащане чрез банков превод</p>
						<div class="mt-4 space-y-4">
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">Банка</label>
								<input type="text" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" value="Уникредит Булбанк">
							</div>
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">IBAN</label>
								<input type="text" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" value="**********************">
							</div>
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">BIC</label>
								<input type="text" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" value="UNCRBGSF">
							</div>
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">Получател</label>
								<input type="text" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" value="Ракла ЕООД">
							</div>
						</div>
					</div>
				</div>
				<!-- Credit Card -->
				<div class="flex items-start p-4 border border-gray-200 rounded">
					<div class="w-12 h-12 flex items-center justify-center text-purple-500">
						<i class="ri-bank-card-fill ri-2x"></i>
					</div>
					<div class="flex-1">
						<div class="flex items-center justify-between">
							<h3 class="text-lg font-medium text-gray-800">Кредитна/Дебитна карта</h3>
							<label class="toggle-switch">
								<input type="checkbox" checked>
								<span class="toggle-slider"></span>
							</label>
						</div>
						<p class="text-sm text-gray-600 mt-1">Онлайн плащане с карта</p>
						<div class="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">Merchant ID</label>
								<input type="text" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" value="rakla_merchant">
							</div>
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">API ключ</label>
								<input type="password" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" value="••••••••••••••••">
							</div>
						</div>
						<div class="mt-4 flex items-center space-x-4">
							<div class="w-10 h-6 flex items-center justify-center">
								<i class="ri-visa-fill ri-lg text-blue-700"></i>
							</div>
							<div class="w-10 h-6 flex items-center justify-center">
								<i class="ri-mastercard-fill ri-lg text-orange-500"></i>
							</div>
							<div class="w-10 h-6 flex items-center justify-center">
								<i class="ri-paypal-fill ri-lg text-blue-500"></i>
							</div>
						</div>
					</div>
				</div>
				<!-- ePay -->
				<div class="flex items-start p-4 border border-gray-200 rounded">
					<div class="w-12 h-12 flex items-center justify-center">
						<img src="/image/epay.png" alt="ePay" class="max-w-full max-h-full">
					</div>
					<div class="flex-1">
						<div class="flex items-center justify-between">
							<h3 class="text-lg font-medium text-gray-800">ePay.bg</h3>
							<label class="toggle-switch">
								<input type="checkbox">
								<span class="toggle-slider"></span>
							</label>
						</div>
						<p class="text-sm text-gray-600 mt-1">Плащане чрез ePay.bg</p>
						<div class="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">Merchant ID</label>
								<input type="text" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" placeholder="Въведете вашия merchant id">
							</div>
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">Secret</label>
								<input type="password" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" placeholder="Въведете вашия secret">
							</div>
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
								<input type="email" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" placeholder="Въведете вашия имейл">
							</div>
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">Sandbox Mode</label>
								<select class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary">
									<option value="yes">Yes</option>
									<option value="no" selected>No</option>
								</select>
							</div>
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">Debug Mode</label>
								<select class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary">
									<option value="enabled">Enabled</option>
									<option value="disabled" selected>Disabled</option>
								</select>
							</div>
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">Transaction Method</label>
								<select class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary">
									<option value="sale" selected>Sale</option>
									<option value="authorize">Authorize</option>
								</select>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="mt-6 flex justify-end">
			<button class="px-6 py-2 bg-primary text-white rounded hover:bg-primary/90 transition-colors whitespace-nowrap !rounded-button">Запази промените</button>
		</div>
	</div>
	<!-- Notifications Settings Tab -->
	<div id="content-notifications" class="tab-content hidden">
		<div class="mb-6">
			<h1 class="text-2xl font-bold text-gray-800">Настройки за известия</h1>
			<p class="text-gray-600 mt-1">Управлявайте имейл и SMS известията</p>
		</div>
		<div
			class="grid grid-cols-1 lg:grid-cols-3 gap-6">
			<!-- Email Templates -->
			<div class="lg:col-span-2">
				<div class="bg-white rounded shadow p-6 settings-card">
					<h2 class="text-lg font-semibold text-gray-800 mb-4">Имейл шаблони</h2>
					<div class="space-y-6">
						<div class="border border-gray-200 rounded overflow-hidden">
							<div class="flex items-center justify-between bg-gray-50 p-4 border-b border-gray-200">
								<h3 class="font-medium text-gray-800">Потвърждение на поръчка</h3>
								<button class="text-primary hover:text-primary/80">
									<div class="w-6 h-6 flex items-center justify-center">
										<i class="ri-edit-line"></i>
									</div>
								</button>
							</div>
							<div class="p-4">
								<p class="text-sm text-gray-600">Изпраща се автоматично при нова поръчка</p>
								<div class="flex items-center justify-between mt-2">
									<div class="text-xs text-gray-500">Последно редактиран: 15 Апр 2025</div>
									<button class="text-sm text-primary hover:underline">Преглед</button>
								</div>
							</div>
						</div>
						<div class="border border-gray-200 rounded overflow-hidden">
							<div class="flex items-center justify-between bg-gray-50 p-4 border-b border-gray-200">
								<h3 class="font-medium text-gray-800">Изпратена поръчка</h3>
								<button class="text-primary hover:text-primary/80">
									<div class="w-6 h-6 flex items-center justify-center">
										<i class="ri-edit-line"></i>
									</div>
								</button>
							</div>
							<div class="p-4">
								<p class="text-sm text-gray-600">Изпраща се когато поръчката е изпратена</p>
								<div class="flex items-center justify-between mt-2">
									<div class="text-xs text-gray-500">Последно редактиран: 10 Апр 2025</div>
									<button class="text-sm text-primary hover:underline">Преглед</button>
								</div>
							</div>
						</div>
						<div class="border border-gray-200 rounded overflow-hidden">
							<div class="flex items-center justify-between bg-gray-50 p-4 border-b border-gray-200">
								<h3 class="font-medium text-gray-800">Регистрация на клиент</h3>
								<button class="text-primary hover:text-primary/80">
									<div class="w-6 h-6 flex items-center justify-center">
										<i class="ri-edit-line"></i>
									</div>
								</button>
							</div>
							<div class="p-4">
								<p class="text-sm text-gray-600">Изпраща се при регистрация на нов клиент</p>
								<div class="flex items-center justify-between mt-2">
									<div class="text-xs text-gray-500">Последно редактиран: 5 Апр 2025</div>
									<button class="text-sm text-primary hover:underline">Преглед</button>
								</div>
							</div>
						</div>
						<div class="border border-gray-200 rounded overflow-hidden">
							<div class="flex items-center justify-between bg-gray-50 p-4 border-b border-gray-200">
								<h3 class="font-medium text-gray-800">Забравена парола</h3>
								<button class="text-primary hover:text-primary/80">
									<div class="w-6 h-6 flex items-center justify-center">
										<i class="ri-edit-line"></i>
									</div>
								</button>
							</div>
							<div class="p-4">
								<p class="text-sm text-gray-600">Изпраща се при заявка за възстановяване на парола</p>
								<div class="flex items-center justify-between mt-2">
									<div class="text-xs text-gray-500">Последно редактиран: 20 Мар 2025</div>
									<button class="text-sm text-primary hover:underline">Преглед</button>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<!-- Notification Settings -->
			<div>
				<div class="bg-white rounded shadow p-6 settings-card">
					<h2 class="text-lg font-semibold text-gray-800 mb-4">Настройки за известия</h2>
					<div class="space-y-4">
						<div class="flex items-center justify-between">
							<div>
								<h3 class="text-sm font-medium text-gray-800">Имейл известия</h3>
								<p class="text-xs text-gray-500">Изпращане на имейл известия</p>
							</div>
							<label class="toggle-switch">
								<input type="checkbox" checked>
								<span class="toggle-slider"></span>
							</label>
						</div>
						<div class="flex items-center justify-between">
							<div>
								<h3 class="text-sm font-medium text-gray-800">SMS известия</h3>
								<p class="text-xs text-gray-500">Изпращане на SMS известия</p>
							</div>
							<label class="toggle-switch">
								<input type="checkbox">
								<span class="toggle-slider"></span>
							</label>
						</div>
						<div class="flex items-center justify-between">
							<div>
								<h3 class="text-sm font-medium text-gray-800">Известия за администратор</h3>
								<p class="text-xs text-gray-500">Известия за нови поръчки</p>
							</div>
							<label class="toggle-switch">
								<input type="checkbox" checked>
								<span class="toggle-slider"></span>
							</label>
						</div>
						<div class="flex items-center justify-between">
							<div>
								<h3 class="text-sm font-medium text-gray-800">Бюлетин</h3>
								<p class="text-xs text-gray-500">Изпращане на бюлетин</p>
							</div>
							<label class="toggle-switch">
								<input type="checkbox" checked>
								<span class="toggle-slider"></span>
							</label>
						</div>
					</div>
				</div>
				<div class="bg-white rounded shadow p-6 mt-6 settings-card">
					<h2 class="text-lg font-semibold text-gray-800 mb-4">Тестване на известия</h2>
					<div class="space-y-4">
						<div>
							<label for="test-email" class="block text-sm font-medium text-gray-700 mb-1">Тестов имейл</label>
							<input type="email" id="test-email" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" placeholder="<EMAIL>">
						</div>
						<div>
							<label for="test-template" class="block text-sm font-medium text-gray-700 mb-1">Шаблон</label>
							<div class="relative">
								<select id="test-template" class="w-full px-4 py-2 border border-gray-300 rounded appearance-none focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary pr-8">
									<option value="order_confirmation">Потвърждение на поръчка</option>
									<option value="order_shipped">Изпратена поръчка</option>
									<option value="customer_registration">Регистрация на клиент</option>
									<option value="password_reset">Забравена парола</option>
								</select>
								<div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none text-gray-500">
									<i class="ri-arrow-down-s-line"></i>
								</div>
							</div>
						</div>
						<button class="w-full px-4 py-2 bg-primary text-white rounded hover:bg-primary/90 transition-colors whitespace-nowrap !rounded-button">Изпрати тестово известие</button>
					</div>
				</div>
			</div>
		</div>
		<div class="mt-6 flex justify-end">
			<button class="px-6 py-2 bg-primary text-white rounded hover:bg-primary/90 transition-colors whitespace-nowrap !rounded-button">Запази промените</button>
		</div>
	</div>
	<!-- Integrations Settings Tab -->
	<div id="content-integrations" class="tab-content hidden">
		<div class="mb-6">
			<h1 class="text-2xl font-bold text-gray-800">Интеграции</h1>
			<p class="text-gray-600 mt-1">Управлявайте интеграциите с външни системи</p>
		</div>
		<div
			class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
			<!-- Google Analytics -->
			<div class="bg-white rounded shadow p-6 settings-card">
				<div class="flex items-center justify-between mb-4">
					<div class="w-12 h-12 flex items-center justify-center">
						<img src="https://www.gstatic.com/analytics/web/images/ga-mark.svg" alt="Google Analytics" class="max-w-full max-h-full">
					</div>
					<label class="toggle-switch">
						<input type="checkbox" checked>
						<span class="toggle-slider"></span>
					</label>
				</div>
				<h3 class="text-lg font-medium text-gray-800 mb-2">Google Analytics</h3>
				<p class="text-sm text-gray-600 mb-4">Проследяване на посетители и конверсии</p>
				<div>
					<label class="block text-sm font-medium text-gray-700 mb-1">Tracking ID</label>
					<input type="text" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" value="UA-12345678-1">
				</div>
				<button class="mt-4 w-full px-4 py-2 bg-primary text-white rounded hover:bg-primary/90 transition-colors whitespace-nowrap !rounded-button">Конфигуриране</button>
			</div>
			<!-- Facebook Pixel -->
			<div class="bg-white rounded shadow p-6 settings-card">
				<div class="flex items-center justify-between mb-4">
					<div class="w-12 h-12 flex items-center justify-center text-blue-600">
						<i class="ri-facebook-fill ri-2x"></i>
					</div>
					<label class="toggle-switch">
						<input type="checkbox" checked>
						<span class="toggle-slider"></span>
					</label>
				</div>
				<h3 class="text-lg font-medium text-gray-800 mb-2">Facebook Pixel</h3>
				<p class="text-sm text-gray-600 mb-4">Проследяване на конверсии и ретаргетинг</p>
				<div>
					<label class="block text-sm font-medium text-gray-700 mb-1">Pixel ID</label>
					<input type="text" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" value="123456789012345">
				</div>
				<button class="mt-4 w-full px-4 py-2 bg-primary text-white rounded hover:bg-primary/90 transition-colors whitespace-nowrap !rounded-button">Конфигуриране</button>
			</div>
			<!-- Brevo (Sendinblue) -->
			<div class="bg-white rounded shadow p-6 settings-card">
				<div class="flex items-center justify-between mb-4">
					<div class="w-12 h-12 flex items-center justify-center">
						<img src="https://www.brevo.com/wp-content/uploads/2023/03/Brevo-Logo.svg" alt="Brevo" class="max-w-full max-h-full">
					</div>
					<label class="toggle-switch">
						<input type="checkbox">
						<span class="toggle-slider"></span>
					</label>
				</div>
				<h3 class="text-lg font-medium text-gray-800 mb-2">Brevo</h3>
				<p class="text-sm text-gray-600 mb-4">Имейл маркетинг и автоматизация</p>
				<div>
					<label class="block text-sm font-medium text-gray-700 mb-1">API ключ</label>
					<input type="text" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" placeholder="Въведете вашия API ключ">
				</div>
				<button class="mt-4 w-full px-4 py-2 bg-primary text-white rounded hover:bg-primary/90 transition-colors whitespace-nowrap !rounded-button">Конфигуриране</button>
			</div>
			<!-- Google reCAPTCHA -->
			<div class="bg-white rounded shadow p-6 settings-card">
				<div class="flex items-center justify-between mb-4">
					<div class="w-12 h-12 flex items-center justify-center">
						<img src="https://www.gstatic.com/recaptcha/api2/logo_48.png" alt="reCAPTCHA" class="max-w-full max-h-full">
					</div>
					<label class="toggle-switch">
						<input type="checkbox" checked>
						<span class="toggle-slider"></span>
					</label>
				</div>
				<h3 class="text-lg font-medium text-gray-800 mb-2">Google reCAPTCHA</h3>
				<p class="text-sm text-gray-600 mb-4">Защита от ботове и спам</p>
				<div class="space-y-4">
					<div>
						<label class="block text-sm font-medium text-gray-700 mb-1">Site Key</label>
						<input type="text" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" value="6LcXXXXXXXXXXXXXXXXXXXX">
					</div>
					<div>
						<label class="block text-sm font-medium text-gray-700 mb-1">Secret Key</label>
						<input type="password" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" value="6LcXXXXXXXXXXXXXXXXXXXX">
					</div>
				</div>
				<button class="mt-4 w-full px-4 py-2 bg-primary text-white rounded hover:bg-primary/90 transition-colors whitespace-nowrap !rounded-button">Конфигуриране</button>
			</div>
			<!-- Mailchimp -->
			<div class="bg-white rounded shadow p-6 settings-card">
				<div class="flex items-center justify-between mb-4">
					<div class="w-12 h-12 flex items-center justify-center">
						<img src="https://cdn-images.mailchimp.com/monkey_rewards/grow-business-banner-2x.png" alt="Mailchimp" class="max-w-full max-h-full">
					</div>
					<label class="toggle-switch">
						<input type="checkbox">
						<span class="toggle-slider"></span>
					</label>
				</div>
				<h3 class="text-lg font-medium text-gray-800 mb-2">Mailchimp</h3>
				<p class="text-sm text-gray-600 mb-4">Имейл маркетинг и автоматизация</p>
				<div class="space-y-4">
					<div>
						<label class="block text-sm font-medium text-gray-700 mb-1">API ключ</label>
						<input type="text" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" placeholder="Въведете вашия API ключ">
					</div>
					<div>
						<label class="block text-sm font-medium text-gray-700 mb-1">List ID</label>
						<input type="text" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" placeholder="Въведете вашия List ID">
					</div>
				</div>
				<button class="mt-4 w-full px-4 py-2 bg-primary text-white rounded hover:bg-primary/90 transition-colors whitespace-nowrap !rounded-button">Конфигуриране</button>
			</div>
			<!-- Google Search Console -->
			<div class="bg-white rounded shadow p-6 settings-card">
				<div class="flex items-center justify-between mb-4">
					<div class="w-12 h-12 flex items-center justify-center">
						<img src="https://ssl.gstatic.com/search-console/scfe/search_console-64.png" alt="Search Console" class="max-w-full max-h-full">
					</div>
					<label class="toggle-switch">
						<input type="checkbox">
						<span class="toggle-slider"></span>
					</label>
				</div>
				<h3 class="text-lg font-semibold text-gray-800 mb-2">Google Search Console</h3>
				<p class="text-sm text-gray-600 mb-4">SEO и проследяване на търсачки</p>
				<div>
					<label class="block text-sm font-medium text-gray-700 mb-1">Verification Code</label>
					<input type="text" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" placeholder="Въведете код за верификация">
				</div>
				<button class="mt-4 w-full px-4 py-2 bg-primary text-white rounded hover:bg-primary/90 transition-colors whitespace-nowrap !rounded-button">Конфигуриране</button>
			</div>
		</div>
		<div class="mt-6 flex justify-end">
			<button class="px-6 py-2 bg-primary text-white rounded hover:bg-primary/90 transition-colors whitespace-nowrap !rounded-button">Запази промените</button>
		</div>
	</div>
	<!-- Security Settings Tab -->
	<div id="content-security" class="tab-content hidden">
		<div class="mb-6">
			<h1 class="text-2xl font-bold text-gray-800">Настройки за сигурност</h1>
			<p class="text-gray-600 mt-1">Управлявайте настройките за сигурност на вашия магазин</p>
		</div>
		<div
			class="grid grid-cols-1 lg:grid-cols-3 gap-6">
			<!-- Security Settings -->
			<div class="lg:col-span-2">
				<div class="bg-white rounded shadow p-6 settings-card">
					<h2 class="text-lg font-semibold text-gray-800 mb-4">Настройки за сигурност</h2>
					<div class="space-y-4">
						<div class="flex items-center justify-between">
							<div>
								<h3 class="text-sm font-medium text-gray-800">Заключване след неуспешни опити</h3>
								<p class="text-xs text-gray-500">Заключване на акаунта след 5 неуспешни опита</p>
							</div>
							<label class="toggle-switch">
								<input type="checkbox" checked>
								<span class="toggle-slider"></span>
							</label>
						</div>
					</div>
				</div>
				<div class="bg-white rounded shadow p-6 mt-6 settings-card">
					<h2 class="text-lg font-semibold text-gray-800 mb-4">IP ограничения</h2>
					<div class="space-y-4">
						<div class="flex items-center justify-between">
							<div>
								<h3 class="text-sm font-medium text-gray-800">Активиране на IP ограничения</h3>
								<p class="text-xs text-gray-500">Ограничаване на достъп до административния панел</p>
							</div>
							<label class="toggle-switch">
								<input type="checkbox">
								<span class="toggle-slider"></span>
							</label>
						</div>
						<div>
							<label for="restricted-ips" class="block text-sm font-medium text-gray-700 mb-1">Разрешени IP адреси</label>
							<div class="flex items-center space-x-2">
								<input type="text" id="restricted-ips" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" value="**************">
								<button class="p-2 text-gray-400 hover:text-gray-500"><i class="ri-delete-bin-line"></i></button>
							</div>
							<button class="mt-2 px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors whitespace-nowrap !rounded-button"><i class="ri-add-line mr-1"></i> Добави IP адрес</button>
							<p class="text-xs text-gray-500 mt-1">Входящи IP адреси (напр. ***********)</p>
						</div>
					</div>
				</div>
				<div class="bg-white rounded shadow p-6 mt-6 settings-card">
					<h2 class="text-lg font-semibold text-gray-800 mb-4">Логове за достъп</h2>
					<div class="space-y-4">
						<div class="border-b border-gray-200 pb-3">
							<div class="flex items-center justify-between">
								<div>
									<p class="text-sm font-medium text-gray-900"><EMAIL></p>
									<p class="text-xs text-gray-500">IP: ***********</p>
								</div>
								<p class="text-xs text-gray-500">25 Апр 2025, 10:45</p>
							</div>
						</div>
						<div class="border-b border-gray-200 pb-3">
							<div class="flex items-center justify-between">
								<div>
									<p class="text-sm font-medium text-gray-900"><EMAIL></p>
									<p class="text-xs text-gray-500">IP: ***********</p>
								</div>
								<p class="text-xs text-gray-500">24 Апр 2025, 15:30</p>
							</div>
						</div>
						<div class="border-b border-gray-200 pb-3">
							<div class="flex items-center justify-between">
								<div>
									<p class="text-sm font-medium text-gray-900"><EMAIL></p>
									<p class="text-xs text-gray-500">IP: ***********</p>
								</div>
								<p class="text-xs text-gray-500">24 Апр 2025, 09:15</p>
							</div>
						</div>
						<div class="border-b border-gray-200 pb-3">
							<div class="flex items-center justify-between">
								<div>
									<p class="text-sm font-medium text-gray-900"><EMAIL></p>
									<p class="text-xs text-gray-500">IP: ***********</p>
								</div>
								<p class="text-xs text-gray-500">23 Апр 2025, 14:20</p>
							</div>
						</div>
						<div>
							<div class="flex items-center justify-between">
								<div>
									<p class="text-sm font-medium text-gray-900"><EMAIL></p>
									<p class="text-xs text-gray-500">IP: ***********</p>
								</div>
								<p class="text-xs text-gray-500">23 Апр 2025, 08:50</p>
							</div>
						</div>
						<button class="w-full px-4 py-2 bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors whitespace-nowrap !rounded-button">Виж всички логове</button>
					</div>
				</div>
			</div>
			<!-- Security Sidebar -->
			<div>
				<div class="bg-white rounded shadow p-6 settings-card">
					<h2 class="text-lg font-semibold text-gray-800 mb-4">Смяна на парола</h2>
					<div class="space-y-4">
						<div>
							<label for="current-password" class="block text-sm font-medium text-gray-700 mb-1">Текуща парола</label>
							<input type="password" id="current-password" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary">
						</div>
						<div>
							<label for="new-password" class="block text-sm font-medium text-gray-700 mb-1">Нова парола</label>
							<input type="password" id="new-password" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary">
						</div>
						<div>
							<label for="confirm-password" class="block text-sm font-medium text-gray-700 mb-1">Повторете новата парола</label>
							<input type="password" id="confirm-password" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary">
						</div>
						<button class="px-6 py-2 bg-primary text-white rounded hover:bg-primary/90 transition-colors whitespace-nowrap !rounded-button">Смени паролата</button>
					</div>
				</div>
			</div>
		</div>
		<div class="mt-6 flex justify-end">
			<button class="px-6 py-2 bg-primary text-white rounded hover:bg-primary/90 transition-colors whitespace-nowrap !rounded-button">Запази промените</button>
		</div>
	</div>
	<!-- Admin Users Tab -->
	<div id="content-admin-users" class="tab-content hidden">
		<div class="mb-6 flex justify-between items-center">
			<div>
				<h1 class="text-2xl font-bold text-gray-800">Админ потребители</h1>
				<p class="text-gray-600 mt-1">Управление на администраторски акаунти</p>
			</div>
			<button id="add-admin-user" class="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90 transition-colors whitespace-nowrap !rounded-button flex items-center">
				<div class="w-5 h-5 flex items-center justify-center mr-2">
					<i class="ri-user-add-line"></i>
				</div>
				<span>Добави потребител</span>
			</button>
		</div>
		<div class="bg-white rounded shadow overflow-hidden">
			<table class="w-full">
				<thead>
					<tr class="bg-gray-50 border-b border-gray-200">
						<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Потребител</th>
						<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Роля</th>
						<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Последно влизане</th>
						<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Статус</th>
						<th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Действия</th>
					</tr>
				</thead>
				<tbody class="divide-y divide-gray-200">
					<tr>
						<td class="px-6 py-4">
							<div class="flex items-center">
								<div class="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center text-primary mr-3">
									<i class="ri-user-line"></i>
								</div>
								<div>
									<div class="text-sm font-medium text-gray-900">Георги Иванов</div>
									<div class="text-sm text-gray-500"><EMAIL></div>
								</div>
							</div>
						</td>
						<td class="px-6 py-4">
							<span class="px-2 py-1 text-xs font-medium bg-purple-100 text-purple-800 rounded">Супер админ</span>
						</td>
						<td class="px-6 py-4 text-sm text-gray-500">25 Апр 2025, 10:45</td>
						<td class="px-6 py-4">
							<span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded">Активен</span>
						</td>
						<td class="px-6 py-4 text-right">
							<button class="edit-admin-user text-primary hover:text-primary/80">
								<div class="w-8 h-8 flex items-center justify-center">
									<i class="ri-edit-line"></i>
								</div>
							</button>
						</td>
					</tr>
					<tr>
						<td class="px-6 py-4">
							<div class="flex items-center">
								<div class="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center text-primary mr-3">
									<i class="ri-user-line"></i>
								</div>
								<div>
									<div class="text-sm font-medium text-gray-900">Мария Петрова</div>
									<div class="text-sm text-gray-500"><EMAIL></div>
								</div>
							</div>
						</td>
						<td class="px-6 py-4">
							<span class="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded">Мениджър</span>
						</td>
						<td class="px-6 py-4 text-sm text-gray-500">24 Апр 2025, 15:30</td>
						<td class="px-6 py-4">
							<span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded">Активен</span>
						</td>
						<td class="px-6 py-4 text-right">
							<button class="edit-admin-user text-primary hover:text-primary/80">
								<div class="w-8 h-8 flex items-center justify-center">
									<i class="ri-edit-line"></i>
								</div>
							</button>
						</td>
					</tr>
					<tr>
						<td class="px-6 py-4">
							<div class="flex items-center">
								<div class="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center text-primary mr-3">
									<i class="ri-user-line"></i>
								</div>
								<div>
									<div class="text-sm font-medium text-gray-900">Стефан Димитров</div>
									<div class="text-sm text-gray-500"><EMAIL></div>
								</div>
							</div>
						</td>
						<td class="px-6 py-4">
							<span class="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded">Поддръжка</span>
						</td>
						<td class="px-6 py-4 text-sm text-gray-500">23 Апр 2025, 14:20</td>
						<td class="px-6 py-4">
							<span class="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded">Неактивен</span>
						</td>
						<td class="px-6 py-4 text-right">
							<button class="edit-admin-user text-primary hover:text-primary/80">
								<div class="w-8 h-8 flex items-center justify-center">
									<i class="ri-edit-line"></i>
								</div>
							</button>
						</td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>
	<!-- Admin User Modal -->
	<div id="admin-user-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden z-50">
		<div class="bg-white rounded-lg w-full max-w-lg mx-4">
			<div class="flex items-center justify-between p-6 border-b border-gray-200">
				<h2 class="text-xl font-semibold text-gray-800" id="modal-title">Добави потребител</h2>
				<button class="close-modal text-gray-400 hover:text-gray-500">
					<div class="w-6 h-6 flex items-center justify-center">
						<i class="ri-close-line"></i>
					</div>
				</button>
			</div>
			<div class="p-6">
				<form id="admin-user-form" class="space-y-4">
					<div>
						<label class="block text-sm font-medium text-gray-700 mb-1">Име</label>
						<input type="text" name="name" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary">
					</div>
					<div>
						<label class="block text-sm font-medium text-gray-700 mb-1">Имейл</label>
						<input type="email" name="email" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary">
					</div>
					<div>
						<label class="block text-sm font-medium text-gray-700 mb-1">Роля</label>
						<select name="role" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary">
							<option value="super_admin">Супер админ</option>
							<option value="manager">Мениджър</option>
							<option value="support">Поддръжка</option>
						</select>
					</div>
					<div>
						<label class="block text-sm font-medium text-gray-700 mb-1">Парола</label>
						<input type="password" name="password" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary">
					</div>
					<div>
						<label class="block text-sm font-medium text-gray-700 mb-1">Повтори парола</label>
						<input type="password" name="password_confirmation" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary">
					</div>
					<div class="flex items-center justify-between">
						<div>
							<label class="flex items-center">
								<input type="checkbox" name="status" class="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary">
								<span class="ml-2 text-sm text-gray-600">Активен потребител</span>
							</label>
						</div>
					</div>
				</form>
			</div>
			<div class="flex items-center justify-end px-6 py-4 bg-gray-50 border-t border-gray-200">
				<button id="save-admin-user" class="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90 transition-colors whitespace-nowrap !rounded-button">Запази</button>
			</div>
		</div>
	</div>
</main>

<script>
	document.addEventListener('DOMContentLoaded', function () {
		const modal = document.getElementById('admin-user-modal');
		const addUserBtn = document.getElementById('add-admin-user');
		const editUserBtns = document.querySelectorAll('.edit-admin-user');
		const closeModalBtns = document.querySelectorAll('.close-modal');

		// Tabs functionality
		const tabButtons = document.querySelectorAll('.tab-button');
		const tabContents = document.querySelectorAll('.tab-content');

		tabButtons.forEach(button => {
			button.addEventListener('click', () => {
				tabButtons.forEach(btn => btn.classList.remove('active', 'text-primary'));
				button.classList.add('active', 'text-primary');

				tabContents.forEach(content => content.classList.add('hidden'));
				document.getElementById(button.id.replace('tab-', 'content-')).classList.remove('hidden');
			});
		});

		// IP Address Management
		const ipAddressesContainer = document.getElementById('ip-addresses-container');
		const addIpButton = document.getElementById('add-ip-button');

		function addIpField(value = '') {
			const ipInputGroup = document.createElement('div');
			ipInputGroup.classList.add('flex', 'items-center', 'ip-input-group');
			ipInputGroup.innerHTML = `
				<input type="text" name="config_security_allowed_ips[]" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" value="${value}" placeholder="Въведете IP адрес">
				<button type="button" class="remove-ip-button ml-2 px-3 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"><i class="ri-delete-bin-line"></i></button>
			`;
			ipAddressesContainer.appendChild(ipInputGroup);
		}

		// Add initial empty IP field if none exist
		if (ipAddressesContainer.children.length === 0) {
			addIpField();
		}

		addIpButton.addEventListener('click', () => {
			addIpField();
		});

		ipAddressesContainer.addEventListener('click', (e) => {
			if (e.target.closest('.remove-ip-button')) {
				e.target.closest('.ip-input-group').remove();
				if (ipAddressesContainer.children.length === 0) {
					addIpField(); // Ensure at least one field remains
				}
			}
		});
	});
</script>
