<?php

namespace Theme25\Backend\Controller\Ajax;

/**
 * Базов клас за AJAX контролери в административния панел
 * Предоставя общи методи за обработка на AJAX заявки
 */
abstract class AjaxController extends \Theme25\Controller {

    public function __construct($registry, $route = '') {
        parent::__construct($registry, $route);
        
        // Автоматична проверка на user_token за всички AJAX заявки
        $this->validateAjaxRequest();
    }

    /**
     * Валидира AJAX заявката
     * Проверява дали заявката е POST и дали има валиден user_token
     */
    protected function validateAjaxRequest() {
        // Проверка дали заявката е AJAX
        if (!$this->isAjaxRequest()) {
            $this->jsonResponse(['error' => 'Невалидна заявка'], false);
            exit;
        }

        // Проверка дали заявката е POST
        if (!$this->isPostRequest()) {
            $this->jsonResponse(['error' => 'Метод не е разрешен'], false);
            exit;
        }

        // Проверка на user_token
        if (!$this->validateUserToken()) {
            $this->jsonResponse(['error' => 'Невалиден токен за достъп'], false);
            exit;
        }
    }

    /**
     * Проверява дали заявката е AJAX
     */
    protected function isAjaxRequest() {
        return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }

    /**
     * Валидира user_token
     */
    protected function validateUserToken() {
        $token = $this->requestPost('user_token') ?: $this->requestGet('user_token');
        return $token && $token === $this->getUserToken();
    }

    /**
     * Изпраща JSON отговор
     * 
     * @param array $data Данни за изпращане
     * @param bool $success Дали операцията е успешна
     */
    protected function jsonResponse($data, $success = true) {
        $response = [
            'success' => $success,
            'timestamp' => time()
        ];

        if ($success) {
            $response = array_merge($response, $data);
        } else {
            $response['error'] = $data['error'] ?? 'Възникна грешка';
            if (isset($data['details'])) {
                $response['details'] = $data['details'];
            }
        }

        $this->setJSONResponseOutput($response);
    }

    /**
     * Обработва изключения и изпраща подходящ JSON отговор
     * 
     * @param \Exception $e Изключението
     */
    protected function handleException($e) {
        // Логиране на грешката
        error_log('AJAX Error: ' . $e->getMessage() . ' in ' . $e->getFile() . ':' . $e->getLine());

        $this->jsonResponse([
            'error' => 'Възникна грешка при обработката',
            'details' => $e->getMessage()
        ], false);
    }

    /**
     * Валидира задължителни полета в POST данните
     * 
     * @param array $requiredFields Списък с задължителни полета
     * @return bool|array True ако всички полета са налични, иначе масив с грешки
     */
    protected function validateRequiredFields($requiredFields) {
        $errors = [];
        
        foreach ($requiredFields as $field) {
            if (!$this->requestPost($field)) {
                $errors[] = "Полето '{$field}' е задължително";
            }
        }

        return empty($errors) ? true : $errors;
    }

    /**
     * Проверява права за достъп до конкретна операция
     * 
     * @param string $permission Правото за проверка
     * @param string $route Route за проверка (по подразбиране текущия route)
     * @return bool
     */
    protected function checkPermission($permission, $route = null) {
        if (!$route) {
            $route = $this->_route;
        }
        
        return $this->hasPermission($permission, $route);
    }

    /**
     * Валидира и почиства входни данни
     * 
     * @param array $data Данните за валидация
     * @param array $rules Правила за валидация
     * @return array Почистени данни
     */
    protected function sanitizeData($data, $rules = []) {
        $sanitized = [];
        
        foreach ($data as $key => $value) {
            if (isset($rules[$key])) {
                switch ($rules[$key]) {
                    case 'email':
                        $sanitized[$key] = filter_var($value, FILTER_SANITIZE_EMAIL);
                        break;
                    case 'url':
                        $sanitized[$key] = filter_var($value, FILTER_SANITIZE_URL);
                        break;
                    case 'int':
                        $sanitized[$key] = (int) $value;
                        break;
                    case 'float':
                        $sanitized[$key] = (float) $value;
                        break;
                    case 'string':
                    default:
                        $sanitized[$key] = htmlspecialchars(trim($value), ENT_QUOTES, 'UTF-8');
                        break;
                }
            } else {
                $sanitized[$key] = htmlspecialchars(trim($value), ENT_QUOTES, 'UTF-8');
            }
        }
        
        return $sanitized;
    }

    /**
     * Създава успешен отговор с данни
     * 
     * @param array $data Данните за връщане
     * @param string $message Съобщение за успех
     */
    protected function successResponse($data = [], $message = 'Операцията е изпълнена успешно') {
        $response = ['message' => $message];
        if (!empty($data)) {
            $response['data'] = $data;
        }
        $this->jsonResponse($response);
    }

    /**
     * Създава отговор с грешка
     * 
     * @param string $message Съобщение за грешка
     * @param array $details Допълнителни детайли
     */
    protected function errorResponse($message = 'Възникна грешка', $details = []) {
        $error = ['error' => $message];
        if (!empty($details)) {
            $error['details'] = $details;
        }
        $this->jsonResponse($error, false);
    }
}
