<?php

namespace Theme25\Backend\Controller\Setting\Setting;

/**
 * AJAX контролер за качване на лого
 */
class UploadLogo extends \Theme25\ControllerSubMethods {

    /**
     * Изпълнява качването на лого
     */
    public function execute() {
        $json = [];

        try {
            // Проверка на права за достъп
            if (!$this->hasPermission('modify', 'setting/setting')) {
                $json['error'] = 'Нямате права за тази операция';
                $this->outputJson($json);
                return;
            }

            // Проверка дали има качен файл
            if (!isset($_FILES['logo']) || $_FILES['logo']['error'] !== UPLOAD_ERR_OK) {
                $json['error'] = 'Моля, изберете валиден файл за лого';
                $this->outputJson($json);
                return;
            }

            $file = $_FILES['logo'];
            
            // Валидация на файла
            $allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
            if (!in_array($file['type'], $allowed_types)) {
                $json['error'] = 'Позволени са само JPEG, PNG, GIF и WebP файлове';
                $this->outputJson($json);
                return;
            }

            // Проверка на размера (максимум 2MB)
            if ($file['size'] > 2 * 1024 * 1024) {
                $json['error'] = 'Файлът е твърде голям. Максимум 2MB';
                $this->outputJson($json);
                return;
            }

            // Генериране на уникално име
            $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
            $filename = 'logo_' . time() . '.' . $extension;
            
            // Определяне на пътя за запазване
            $upload_path = ThemeData()->getImageServerPath() . 'catalog/';
            if (!is_dir($upload_path)) {
                mkdir($upload_path, 0755, true);
            }

            $full_path = $upload_path . $filename;

            // Качване на файла
            if (move_uploaded_file($file['tmp_name'], $full_path)) {
                // Запазване в настройките
                $this->loadModelAs('setting/setting', 'settingModel');
                $this->settingModel->editSetting('config', [
                    'config_logo' => 'catalog/' . $filename
                ]);

                // Генериране на URL за показване
                $logo_url = ThemeData()->getImageWebUrl() . 'catalog/' . $filename;

                $json['success'] = 'Логото е качено успешно';
                $json['logo_url'] = $logo_url;
                $json['logo_path'] = 'catalog/' . $filename;
            } else {
                $json['error'] = 'Възникна грешка при качването на файла';
            }

        } catch (Exception $e) {
            $json['error'] = 'Възникна грешка: ' . $e->getMessage();
        }

        $this->outputJson($json);
    }
}
