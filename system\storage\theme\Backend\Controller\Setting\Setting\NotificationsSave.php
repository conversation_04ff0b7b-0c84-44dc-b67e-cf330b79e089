<?php

namespace Theme25\Backend\Controller\Setting\Setting;

/**
 * Sub-контролер за AJAX запазване на настройки за известия
 */
class NotificationsSave extends \Theme25\ControllerSubMethods {

    /**
     * Изпълнява AJAX запазване на настройки за известия
     */
    public function execute() {
        $json = [];

        // Проверка за права на достъп
        if (!$this->hasPermission('modify', 'setting/setting')) {
            $json['error'] = 'Нямате права за редактиране на настройки за известия';
            $this->outputJson($json);
            return;
        }

        // Проверка за POST заявка
        if (!$this->isPostRequest()) {
            $json['error'] = 'Невалидна заявка';
            $this->outputJson($json);
            return;
        }

        try {
            // Получаване на данните от POST
            $postData = $this->requestPost();
            
            // Валидация на данните
            $validation_errors = $this->validateNotificationSettings($postData);
            if (!empty($validation_errors)) {
                $json['error'] = 'Грешки при валидацията';
                $json['validation_errors'] = $validation_errors;
                $this->outputJson($json);
                return;
            }

            $this->loadModelAs('setting/notifications_settings_model', 'notificationsSettings');
            
            // Запазване на настройките
            $result = $this->notificationsSettings->saveNotificationSettings($postData);
            
            if ($result === true) {
                $json['success'] = 'Настройките за известия са запазени успешно';
                $json['timestamp'] = date('d.m.Y H:i:s');
                
                // Добавяне на актуализирани данни
                $json['updated_data'] = $this->getUpdatedNotificationData($postData);
                
                // Предупреждения ако има
                $warnings = $this->checkNotificationWarnings($postData);
                if (!empty($warnings)) {
                    $json['warnings'] = $warnings;
                }
            } else {
                // Има грешки при валидацията от модела
                $json['error'] = 'Грешки при валидацията';
                $json['validation_errors'] = $result;
            }

        } catch (Exception $e) {
            $json['error'] = 'Грешка при запазване: ' . $e->getMessage();
        }

        $this->outputJson($json);
    }

    /**
     * Валидира настройките за известия
     *
     * @param array $data Данни за валидация
     * @return array Масив с грешки
     */
    private function validateNotificationSettings($data) {
        $errors = [];

        // Валидация на email адрес
        if (empty($data['config_email'])) {
            $errors['config_email'] = 'Email адресът е задължителен';
        } elseif (!filter_var($data['config_email'], FILTER_VALIDATE_EMAIL)) {
            $errors['config_email'] = 'Невалиден email адрес';
        }

        // Валидация на име на магазина
        if (empty($data['config_name'])) {
            $errors['config_name'] = 'Името на магазина е задължително';
        } elseif (mb_strlen($data['config_name']) > 100) {
            $errors['config_name'] = 'Името на магазина не може да бъде повече от 100 символа';
        }

        // Валидация на SMTP настройки ако е избран SMTP
        if (isset($data['config_mail_protocol']) && $data['config_mail_protocol'] === 'smtp') {
            if (empty($data['config_mail_smtp_hostname'])) {
                $errors['config_mail_smtp_hostname'] = 'SMTP hostname е задължителен при SMTP протокол';
            }

            if (empty($data['config_mail_smtp_username'])) {
                $errors['config_mail_smtp_username'] = 'SMTP username е задължителен при SMTP протокол';
            }

            if (empty($data['config_mail_smtp_password'])) {
                $errors['config_mail_smtp_password'] = 'SMTP password е задължителна при SMTP протокол';
            }

            if (isset($data['config_mail_smtp_port'])) {
                $port = (int)$data['config_mail_smtp_port'];
                if ($port < 1 || $port > 65535) {
                    $errors['config_mail_smtp_port'] = 'SMTP портът трябва да бъде между 1 и 65535';
                }
            }

            if (isset($data['config_mail_smtp_timeout'])) {
                $timeout = (int)$data['config_mail_smtp_timeout'];
                if ($timeout < 1 || $timeout > 300) {
                    $errors['config_mail_smtp_timeout'] = 'SMTP timeout трябва да бъде между 1 и 300 секунди';
                }
            }
        }

        // Валидация на admin emails
        if (!empty($data['config_mail_alert_email'])) {
            $emails = explode(',', $data['config_mail_alert_email']);
            foreach ($emails as $email) {
                $email = trim($email);
                if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
                    $errors['config_mail_alert_email'] = 'Един или повече admin email адреси са невалидни';
                    break;
                }
            }
        }

        // Валидация на low stock threshold
        if (isset($data['notification_low_stock_threshold'])) {
            $threshold = (int)$data['notification_low_stock_threshold'];
            if ($threshold < 0) {
                $errors['notification_low_stock_threshold'] = 'Прагът за нисък запас не може да бъде отрицателен';
            }
        }

        // Валидация на retry attempts
        if (isset($data['notification_retry_attempts'])) {
            $attempts = (int)$data['notification_retry_attempts'];
            if ($attempts < 0 || $attempts > 10) {
                $errors['notification_retry_attempts'] = 'Опитите за повторно изпращане трябва да бъдат между 0 и 10';
            }
        }

        // Валидация на retry delay
        if (isset($data['notification_retry_delay'])) {
            $delay = (int)$data['notification_retry_delay'];
            if ($delay < 60 || $delay > 3600) {
                $errors['notification_retry_delay'] = 'Забавянето трябва да бъде между 60 и 3600 секунди';
            }
        }

        // Валидация на email шаблони
        if (isset($data['email_templates']) && is_array($data['email_templates'])) {
            foreach ($data['email_templates'] as $template_code => $template_data) {
                if (empty($template_data['subject'])) {
                    $errors['email_templates'][$template_code]['subject'] = 'Темата на шаблона е задължителна';
                }
                
                if (empty($template_data['content'])) {
                    $errors['email_templates'][$template_code]['content'] = 'Съдържанието на шаблона е задължително';
                }
                
                if (mb_strlen($template_data['subject']) > 255) {
                    $errors['email_templates'][$template_code]['subject'] = 'Темата не може да бъде повече от 255 символа';
                }
            }
        }

        // Валидация на SMS настройки ако е активиран SMS
        if (!empty($data['notification_sms_enabled'])) {
            if (empty($data['sms_provider'])) {
                $errors['sms_provider'] = 'SMS доставчикът е задължителен при активиран SMS';
            }
            
            if (empty($data['sms_api_key'])) {
                $errors['sms_api_key'] = 'SMS API ключът е задължителен при активиран SMS';
            }
        }

        return $errors;
    }

    /**
     * Проверява за предупреждения относно известията
     *
     * @param array $data Данни за проверка
     * @return array Масив с предупреждения
     */
    private function checkNotificationWarnings($data) {
        $warnings = [];

        // Проверка за SMTP настройки
        if ($data['config_mail_protocol'] === 'smtp') {
            if (empty($data['config_mail_smtp_password'])) {
                $warnings[] = [
                    'type' => 'warning',
                    'message' => 'SMTP паролата не е зададена. Известията може да не се изпращат правилно.'
                ];
            }
        }

        // Проверка за активни шаблони
        $active_templates = 0;
        if (isset($data['email_templates']) && is_array($data['email_templates'])) {
            foreach ($data['email_templates'] as $template_data) {
                if (!empty($template_data['status'])) {
                    $active_templates++;
                }
            }
        }

        if ($active_templates === 0) {
            $warnings[] = [
                'type' => 'warning',
                'message' => 'Няма активни email шаблони. Автоматичните известия няма да се изпращат.'
            ];
        }

        // Проверка за admin emails
        if (empty($data['config_mail_alert_email'])) {
            $warnings[] = [
                'type' => 'info',
                'message' => 'Не са зададени admin email адреси за получаване на известия.'
            ];
        }

        // Проверка за SMS настройки
        if (!empty($data['notification_sms_enabled']) && empty($data['sms_api_secret'])) {
            $warnings[] = [
                'type' => 'warning',
                'message' => 'SMS е активиран, но API secret не е зададен. SMS известията може да не работят.'
            ];
        }

        // Проверка за queue настройки
        if (!empty($data['notification_queue_enabled'])) {
            $warnings[] = [
                'type' => 'info',
                'message' => 'Опашката за известия е активирана. Известията ще се изпращат с малко забавяне.'
            ];
        }

        return $warnings;
    }

    /**
     * Получава актуализираните данни за настройките за известия
     *
     * @param array $postData POST данни
     * @return array
     */
    private function getUpdatedNotificationData($postData) {
        $updated_data = [
            // Email настройки
            'config_mail_protocol' => $postData['config_mail_protocol'] ?? 'mail',
            'config_mail_parameter' => $postData['config_mail_parameter'] ?? '',
            'config_mail_smtp_hostname' => $postData['config_mail_smtp_hostname'] ?? '',
            'config_mail_smtp_username' => $postData['config_mail_smtp_username'] ?? '',
            'config_mail_smtp_password' => $postData['config_mail_smtp_password'] ?? '',
            'config_mail_smtp_port' => (int)($postData['config_mail_smtp_port'] ?? 587),
            'config_mail_smtp_timeout' => (int)($postData['config_mail_smtp_timeout'] ?? 5),
            'config_email' => $postData['config_email'] ?? '',
            'config_name' => $postData['config_name'] ?? '',
            'config_mail_alert_email' => $postData['config_mail_alert_email'] ?? '',
            
            // Notification настройки
            'notification_email_enabled' => isset($postData['notification_email_enabled']) ? 1 : 0,
            'notification_sms_enabled' => isset($postData['notification_sms_enabled']) ? 1 : 0,
            'notification_admin_new_order' => isset($postData['notification_admin_new_order']) ? 1 : 0,
            'notification_admin_new_customer' => isset($postData['notification_admin_new_customer']) ? 1 : 0,
            'notification_customer_order_status' => isset($postData['notification_customer_order_status']) ? 1 : 0,
            'notification_customer_registration' => isset($postData['notification_customer_registration']) ? 1 : 0,
            'notification_customer_password_reset' => isset($postData['notification_customer_password_reset']) ? 1 : 0,
            'notification_low_stock' => isset($postData['notification_low_stock']) ? 1 : 0,
            'notification_low_stock_threshold' => (int)($postData['notification_low_stock_threshold'] ?? 5),
            'notification_admin_emails' => $postData['notification_admin_emails'] ?? '',
            'notification_frequency' => $postData['notification_frequency'] ?? 'immediate',
            'notification_queue_enabled' => isset($postData['notification_queue_enabled']) ? 1 : 0,
            'notification_retry_attempts' => (int)($postData['notification_retry_attempts'] ?? 3),
            'notification_retry_delay' => (int)($postData['notification_retry_delay'] ?? 300),
            
            // SMS настройки
            'sms_provider' => $postData['sms_provider'] ?? '',
            'sms_api_key' => $postData['sms_api_key'] ?? '',
            'sms_api_secret' => $postData['sms_api_secret'] ?? '',
            'sms_sender_name' => $postData['sms_sender_name'] ?? ''
        ];

        // Обработка на email шаблони
        if (isset($postData['email_templates']) && is_array($postData['email_templates'])) {
            $updated_data['email_templates'] = [];
            foreach ($postData['email_templates'] as $template_code => $template_data) {
                $updated_data['email_templates'][$template_code] = [
                    'subject' => $template_data['subject'] ?? '',
                    'content' => $template_data['content'] ?? '',
                    'status' => isset($template_data['status']) ? 1 : 0
                ];
            }
        }

        return $updated_data;
    }

    /**
     * Изпраща JSON отговор
     *
     * @param array $data
     */
    private function outputJson($data) {
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($data));
    }

    /**
     * Логира действието за запазване на настройки за известия
     *
     * @param array $data Запазените данни
     */
    private function logNotificationSettingsSave($data) {
        try {
            $log_data = [
                'action' => 'notification_settings_save',
                'user_id' => $this->user->getId(),
                'user_name' => $this->user->getUserName(),
                'timestamp' => date('Y-m-d H:i:s'),
                'ip_address' => $this->request->server['REMOTE_ADDR'] ?? 'unknown',
                'changed_fields' => array_keys($data)
            ];
            
            // Логиране в системния лог
            $this->log->write('Notification Settings: Updated by user ' . $this->user->getUserName() . ' (' . $this->user->getId() . ')');
            
        } catch (Exception $e) {
            // Ако логирането се провали, не спираме операцията
            error_log('Failed to log notification settings save: ' . $e->getMessage());
        }
    }
}
