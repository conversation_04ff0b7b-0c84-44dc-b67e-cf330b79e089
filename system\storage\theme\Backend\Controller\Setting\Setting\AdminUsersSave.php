<?php

namespace Theme25\Backend\Controller\Setting\Setting;

/**
 * Sub-контролер за AJAX запазване на настройки за административни потребители
 */
class AdminUsersSave extends \Theme25\ControllerSubMethods {

    /**
     * Изпълнява AJAX запазване на настройки за административни потребители
     */
    public function execute() {
        $json = [];

        // Проверка за права на достъп
        if (!$this->hasPermission('modify', 'setting/setting')) {
            $json['error'] = 'Нямате права за редактиране на настройки за потребители';
            $this->outputJson($json);
            return;
        }

        // Проверка за POST заявка
        if (!$this->isPostRequest()) {
            $json['error'] = 'Невалидна заявка';
            $this->outputJson($json);
            return;
        }

        try {
            // Получаване на данните от POST
            $postData = $this->requestPost();
            
            // Валидация на данните
            $validation_errors = $this->validateAdminUsersSettings($postData);
            if (!empty($validation_errors)) {
                $json['error'] = 'Грешки при валидацията';
                $json['validation_errors'] = $validation_errors;
                $this->outputJson($json);
                return;
            }

            $this->loadModelAs('setting/admin_users_settings_model', 'adminUsersSettings');
            
            // Запазване на настройките
            $result = $this->adminUsersSettings->saveAdminUsersSettings($postData);
            
            if ($result === true) {
                $json['success'] = 'Настройките за потребители са запазени успешно';
                $json['timestamp'] = date('d.m.Y H:i:s');
                
                // Добавяне на актуализирани данни
                $json['updated_data'] = $this->getUpdatedAdminUsersData($postData);
                
                // Предупреждения ако има
                $warnings = $this->checkAdminUsersWarnings($postData);
                if (!empty($warnings)) {
                    $json['warnings'] = $warnings;
                }
                
                // Логиране на действието
                $this->logAdminUsersSettingsSave($postData);
            } else {
                // Има грешки при валидацията от модела
                $json['error'] = 'Грешки при валидацията';
                $json['validation_errors'] = $result;
            }

        } catch (Exception $e) {
            $json['error'] = 'Грешка при запазване: ' . $e->getMessage();
        }

        $this->outputJson($json);
    }

    /**
     * Валидира настройките за административни потребители
     *
     * @param array $data Данни за валидация
     * @return array Масив с грешки
     */
    private function validateAdminUsersSettings($data) {
        $errors = [];

        // Валидация на настройки за сигурност
        if (isset($data['security_settings']) && is_array($data['security_settings'])) {
            $security = $data['security_settings'];
            
            // Валидация на минимална дължина на паролата
            if (isset($security['password_min_length'])) {
                $min_length = (int)$security['password_min_length'];
                if ($min_length < 6 || $min_length > 32) {
                    $errors['security_settings']['password_min_length'] = 'Минималната дължина на паролата трябва да бъде между 6 и 32 символа';
                }
            }
            
            // Валидация на дни за изтичане на паролата
            if (isset($security['password_expiry_days'])) {
                $expiry_days = (int)$security['password_expiry_days'];
                if ($expiry_days < 30 || $expiry_days > 365) {
                    $errors['security_settings']['password_expiry_days'] = 'Дните за изтичане на паролата трябва да бъдат между 30 и 365';
                }
            }
            
            // Валидация на максимални опити за вход
            if (isset($security['max_login_attempts'])) {
                $max_attempts = (int)$security['max_login_attempts'];
                if ($max_attempts < 3 || $max_attempts > 20) {
                    $errors['security_settings']['max_login_attempts'] = 'Максималните опити за вход трябва да бъдат между 3 и 20';
                }
            }
            
            // Валидация на времето за заключване
            if (isset($security['lockout_duration'])) {
                $lockout_duration = (int)$security['lockout_duration'];
                if ($lockout_duration < 5 || $lockout_duration > 1440) {
                    $errors['security_settings']['lockout_duration'] = 'Времето за заключване трябва да бъде между 5 и 1440 минути';
                }
            }
            
            // Валидация на timeout на сесията
            if (isset($security['session_timeout'])) {
                $session_timeout = (int)$security['session_timeout'];
                if ($session_timeout < 300 || $session_timeout > 86400) {
                    $errors['security_settings']['session_timeout'] = 'Timeout на сесията трябва да бъде между 300 и 86400 секунди';
                }
            }
            
            // Валидация на IP whitelist
            if (!empty($security['ip_whitelist'])) {
                $ips = explode(',', $security['ip_whitelist']);
                foreach ($ips as $ip) {
                    $ip = trim($ip);
                    if (!empty($ip) && !filter_var($ip, FILTER_VALIDATE_IP)) {
                        $errors['security_settings']['ip_whitelist'] = 'Невалиден IP адрес в whitelist: ' . $ip;
                        break;
                    }
                }
            }
        }

        // Валидация на потребители ако има промени
        if (isset($data['users']) && is_array($data['users'])) {
            foreach ($data['users'] as $user_index => $user) {
                // Валидация на потребителско име
                if (isset($user['username'])) {
                    if (empty($user['username'])) {
                        $errors['users'][$user_index]['username'] = 'Потребителското име е задължително';
                    } elseif (strlen($user['username']) < 3 || strlen($user['username']) > 20) {
                        $errors['users'][$user_index]['username'] = 'Потребителското име трябва да бъде между 3 и 20 символа';
                    } elseif (!preg_match('/^[a-zA-Z0-9_]+$/', $user['username'])) {
                        $errors['users'][$user_index]['username'] = 'Потребителското име може да съдържа само букви, цифри и долна черта';
                    }
                }
                
                // Валидация на email
                if (isset($user['email'])) {
                    if (empty($user['email'])) {
                        $errors['users'][$user_index]['email'] = 'Email адресът е задължителен';
                    } elseif (!filter_var($user['email'], FILTER_VALIDATE_EMAIL)) {
                        $errors['users'][$user_index]['email'] = 'Невалиден email адрес';
                    }
                }
                
                // Валидация на име
                if (isset($user['firstname'])) {
                    if (empty($user['firstname'])) {
                        $errors['users'][$user_index]['firstname'] = 'Името е задължително';
                    } elseif (strlen($user['firstname']) < 2 || strlen($user['firstname']) > 32) {
                        $errors['users'][$user_index]['firstname'] = 'Името трябва да бъде между 2 и 32 символа';
                    }
                }
                
                // Валидация на фамилия
                if (isset($user['lastname'])) {
                    if (empty($user['lastname'])) {
                        $errors['users'][$user_index]['lastname'] = 'Фамилията е задължителна';
                    } elseif (strlen($user['lastname']) < 2 || strlen($user['lastname']) > 32) {
                        $errors['users'][$user_index]['lastname'] = 'Фамилията трябва да бъде между 2 и 32 символа';
                    }
                }
                
                // Валидация на парола (само при нови потребители или промяна)
                if (isset($user['password']) && !empty($user['password'])) {
                    $password = $user['password'];
                    $security_settings = $data['security_settings'] ?? [];
                    
                    $min_length = (int)($security_settings['password_min_length'] ?? 8);
                    if (strlen($password) < $min_length) {
                        $errors['users'][$user_index]['password'] = 'Паролата трябва да бъде поне ' . $min_length . ' символа';
                    }
                    
                    if (!empty($security_settings['password_require_uppercase']) && !preg_match('/[A-Z]/', $password)) {
                        $errors['users'][$user_index]['password'] = 'Паролата трябва да съдържа поне една главна буква';
                    }
                    
                    if (!empty($security_settings['password_require_lowercase']) && !preg_match('/[a-z]/', $password)) {
                        $errors['users'][$user_index]['password'] = 'Паролата трябва да съдържа поне една малка буква';
                    }
                    
                    if (!empty($security_settings['password_require_numbers']) && !preg_match('/[0-9]/', $password)) {
                        $errors['users'][$user_index]['password'] = 'Паролата трябва да съдържа поне една цифра';
                    }
                    
                    if (!empty($security_settings['password_require_symbols']) && !preg_match('/[^a-zA-Z0-9]/', $password)) {
                        $errors['users'][$user_index]['password'] = 'Паролата трябва да съдържа поне един специален символ';
                    }
                }
                
                // Валидация на група потребители
                if (isset($user['user_group_id'])) {
                    $user_group_id = (int)$user['user_group_id'];
                    if ($user_group_id < 1) {
                        $errors['users'][$user_index]['user_group_id'] = 'Изберете валидна група потребители';
                    }
                }
            }
        }

        // Валидация на групи потребители ако има промени
        if (isset($data['user_groups']) && is_array($data['user_groups'])) {
            foreach ($data['user_groups'] as $group_index => $group) {
                // Валидация на име на групата
                if (isset($group['name'])) {
                    if (empty($group['name'])) {
                        $errors['user_groups'][$group_index]['name'] = 'Името на групата е задължително';
                    } elseif (strlen($group['name']) < 2 || strlen($group['name']) > 64) {
                        $errors['user_groups'][$group_index]['name'] = 'Името на групата трябва да бъде между 2 и 64 символа';
                    }
                }
                
                // Валидация на описанието
                if (isset($group['description']) && strlen($group['description']) > 255) {
                    $errors['user_groups'][$group_index]['description'] = 'Описанието не може да бъде повече от 255 символа';
                }
            }
        }

        return $errors;
    }

    /**
     * Проверява за предупреждения относно настройките за потребители
     *
     * @param array $data Данни за проверка
     * @return array Масив с предупреждения
     */
    private function checkAdminUsersWarnings($data) {
        $warnings = [];

        // Проверка за настройки за сигурност
        if (isset($data['security_settings']) && is_array($data['security_settings'])) {
            $security = $data['security_settings'];
            
            // Предупреждение за слаби пароли
            if (empty($security['password_require_uppercase']) && 
                empty($security['password_require_numbers'])) {
                $warnings[] = [
                    'type' => 'warning',
                    'message' => 'Политиката за пароли не изисква главни букви и цифри. Това може да намали сигурността.'
                ];
            }
            
            // Предупреждение за двуфакторна автентикация
            if (empty($security['two_factor_auth'])) {
                $warnings[] = [
                    'type' => 'info',
                    'message' => 'Двуфакторната автентикация не е активирана. Препоръчваме да я включите за по-висока сигурност.'
                ];
            }
            
            // Предупреждение за дълъг timeout на сесията
            if (isset($security['session_timeout']) && (int)$security['session_timeout'] > 7200) {
                $warnings[] = [
                    'type' => 'warning',
                    'message' => 'Timeout на сесията е много дълъг. Препоръчваме максимум 2 часа за по-добра сигурност.'
                ];
            }
            
            // Предупреждение за IP whitelist
            if (!empty($security['ip_whitelist_enabled']) && empty($security['ip_whitelist'])) {
                $warnings[] = [
                    'type' => 'warning',
                    'message' => 'IP whitelist е активиран, но няма зададени IP адреси. Това може да блокира достъпа.'
                ];
            }
        }

        return $warnings;
    }

    /**
     * Получава актуализираните данни за настройките за потребители
     *
     * @param array $postData POST данни
     * @return array
     */
    private function getUpdatedAdminUsersData($postData) {
        $updated_data = [];

        // Настройки за сигурност
        if (isset($postData['security_settings']) && is_array($postData['security_settings'])) {
            $updated_data['security_settings'] = [];
            foreach ($postData['security_settings'] as $setting_key => $setting_value) {
                if (in_array($setting_key, ['password_require_uppercase', 'password_require_lowercase', 'password_require_numbers', 'password_require_symbols', 'force_password_change', 'two_factor_auth', 'ip_whitelist_enabled', 'login_notifications', 'activity_logging', 'auto_logout_idle'])) {
                    $updated_data['security_settings'][$setting_key] = isset($setting_value) ? 1 : 0;
                } else {
                    $updated_data['security_settings'][$setting_key] = $setting_value;
                }
            }
        }

        // Потребители
        if (isset($postData['users']) && is_array($postData['users'])) {
            $updated_data['users'] = [];
            foreach ($postData['users'] as $user_index => $user) {
                $updated_data['users'][$user_index] = [
                    'username' => $user['username'] ?? '',
                    'firstname' => $user['firstname'] ?? '',
                    'lastname' => $user['lastname'] ?? '',
                    'email' => $user['email'] ?? '',
                    'user_group_id' => (int)($user['user_group_id'] ?? 1),
                    'status' => isset($user['status']) ? 1 : 0,
                    'image' => $user['image'] ?? ''
                ];
                
                // Не включваме паролата в отговора
                if (!empty($user['password'])) {
                    $updated_data['users'][$user_index]['password_changed'] = true;
                }
            }
        }

        // Групи потребители
        if (isset($postData['user_groups']) && is_array($postData['user_groups'])) {
            $updated_data['user_groups'] = [];
            foreach ($postData['user_groups'] as $group_index => $group) {
                $updated_data['user_groups'][$group_index] = [
                    'name' => $group['name'] ?? '',
                    'description' => $group['description'] ?? '',
                    'permissions' => $group['permissions'] ?? []
                ];
            }
        }

        return $updated_data;
    }

    /**
     * Изпраща JSON отговор
     *
     * @param array $data
     */
    private function outputJson($data) {
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($data));
    }

    /**
     * Логира действието за запазване на настройки за потребители
     *
     * @param array $data Запазените данни
     */
    private function logAdminUsersSettingsSave($data) {
        try {
            $log_data = [
                'action' => 'admin_users_settings_save',
                'user_id' => $this->user->getId(),
                'user_name' => $this->user->getUserName(),
                'timestamp' => date('Y-m-d H:i:s'),
                'ip_address' => $this->request->server['REMOTE_ADDR'] ?? 'unknown',
                'changed_fields' => array_keys($data)
            ];
            
            // Логиране в системния лог
            $this->log->write('Admin Users Settings: Updated by user ' . $this->user->getUserName() . ' (' . $this->user->getId() . ')');
            
        } catch (Exception $e) {
            // Ако логирането се провали, не спираме операцията
            error_log('Failed to log admin users settings save: ' . $e->getMessage());
        }
    }
}
