<div class="mb-6">
    <h1 class="text-2xl font-bold text-gray-800">Настройки за доставка</h1>
    <p class="text-gray-600 mt-1">Управлявайте методите за доставка и зоните в магазина</p>
</div>

<form id="delivery-settings-form" class="space-y-6">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Shipping Methods -->
        <div class="lg:col-span-2">
            <!-- Shipping Methods List -->
            <div class="bg-white rounded shadow p-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="ri-truck-line mr-2"></i>
                    Методи за доставка
                </h2>
                
                <div id="shipping-methods-list" class="space-y-4">
                    {% for method_code, method in shipping_methods %}
                    <div data-method="{{ method_code }}" 
                         class="border border-gray-200 rounded p-4 {{ method.status ? 'bg-green-50' : 'opacity-50' }} hover:shadow-sm transition-all cursor-move">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="drag-handle text-gray-400 hover:text-gray-600 cursor-move">
                                    <i class="ri-drag-move-line text-lg"></i>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <i class="{{ method.icon }} text-2xl text-gray-600"></i>
                                    <div>
                                        <h3 class="font-medium text-gray-900">{{ method.name }}</h3>
                                        <p class="text-sm text-gray-500">{{ method.description }}</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="flex items-center space-x-4">
                                <!-- Method Settings -->
                                <div class="flex items-center space-x-2">
                                    {% if method_code == 'flat' %}
                                    <input type="number" 
                                           name="shipping_methods[{{ method_code }}][cost]"
                                           class="text-sm border border-gray-300 rounded px-2 py-1 w-20"
                                           value="{{ method.cost ?? 0 }}"
                                           step="0.01"
                                           min="0"
                                           placeholder="0.00">
                                    <span class="text-sm text-gray-500">лв.</span>
                                    {% elseif method_code == 'free' %}
                                    <input type="number" 
                                           name="shipping_methods[{{ method_code }}][minimum_total]"
                                           class="text-sm border border-gray-300 rounded px-2 py-1 w-20"
                                           value="{{ method.minimum_total ?? 0 }}"
                                           step="0.01"
                                           min="0"
                                           placeholder="0.00">
                                    <span class="text-sm text-gray-500">мин. лв.</span>
                                    {% endif %}
                                    
                                    <input type="number" 
                                           name="shipping_methods[{{ method_code }}][sort_order]"
                                           class="text-sm border border-gray-300 rounded px-2 py-1 w-16"
                                           value="{{ method.sort_order ?? 0 }}"
                                           min="0"
                                           placeholder="0">
                                </div>
                                
                                <!-- Test Button -->
                                <button type="button" 
                                        data-method="{{ method_code }}"
                                        class="test-shipping-method px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600 transition-colors">
                                    <i class="ri-test-tube-line mr-1"></i>
                                    Тест
                                </button>
                                
                                <!-- Status Toggle -->
                                <label class="flex items-center cursor-pointer">
                                    <input type="checkbox" 
                                           name="shipping_methods[{{ method_code }}][status]"
                                           data-method="{{ method_code }}"
                                           class="shipping-method-toggle toggle-switch"
                                           {{ method.status ? 'checked' : '' }}>
                                    <span class="ml-2 text-sm text-gray-700">Активен</span>
                                </label>
                            </div>
                        </div>
                        
                        <!-- Advanced Settings for Weight-based shipping -->
                        {% if method_code == 'weight' %}
                        <div class="mt-4 pt-4 border-t border-gray-200">
                            <h4 class="text-sm font-medium text-gray-700 mb-3">Цени по тегло</h4>
                            <div id="weight-rates-container" class="space-y-2">
                                {% if method.rates is defined and method.rates %}
                                    {% for rate_index, rate in method.rates %}
                                    <div id="weight-rate-{{ rate_index }}" class="grid grid-cols-4 gap-2 items-center p-2 border border-gray-200 rounded">
                                        <input type="number" 
                                               name="shipping_methods[weight][rates][{{ rate_index }}][weight_from]"
                                               class="text-sm border border-gray-300 rounded px-2 py-1"
                                               value="{{ rate.weight_from ?? '' }}"
                                               placeholder="От (кг)"
                                               step="0.01"
                                               min="0">
                                        <input type="number" 
                                               name="shipping_methods[weight][rates][{{ rate_index }}][weight_to]"
                                               class="text-sm border border-gray-300 rounded px-2 py-1"
                                               value="{{ rate.weight_to ?? '' }}"
                                               placeholder="До (кг)"
                                               step="0.01"
                                               min="0">
                                        <input type="number" 
                                               name="shipping_methods[weight][rates][{{ rate_index }}][cost]"
                                               class="text-sm border border-gray-300 rounded px-2 py-1"
                                               value="{{ rate.cost ?? '' }}"
                                               placeholder="Цена (лв.)"
                                               step="0.01"
                                               min="0">
                                        <button type="button" 
                                                class="remove-shipping-rate px-2 py-1 bg-red-500 text-white text-sm rounded hover:bg-red-600"
                                                data-method="weight"
                                                data-index="{{ rate_index }}">
                                            <i class="ri-delete-bin-line"></i>
                                        </button>
                                    </div>
                                    {% endfor %}
                                {% endif %}
                            </div>
                            <button type="button" 
                                    class="add-shipping-rate mt-2 px-3 py-1 bg-green-500 text-white text-sm rounded hover:bg-green-600"
                                    data-method="weight">
                                <i class="ri-add-line mr-1"></i>
                                Добави ред
                            </button>
                        </div>
                        {% endif %}
                        
                        <!-- Geo Zone Selection -->
                        <div class="mt-4 pt-4 border-t border-gray-200">
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">
                                        Зона за доставка
                                    </label>
                                    <select name="shipping_methods[{{ method_code }}][geo_zone_id]"
                                            class="w-full text-sm border border-gray-300 rounded px-2 py-1">
                                        <option value="0">Всички зони</option>
                                        {% for geo_zone in geo_zones %}
                                        <option value="{{ geo_zone.geo_zone_id }}" {{ (method.geo_zone_id ?? 0) == geo_zone.geo_zone_id ? 'selected' : '' }}>
                                            {{ geo_zone.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                                
                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">
                                        Данъчен клас
                                    </label>
                                    <select name="shipping_methods[{{ method_code }}][tax_class_id]"
                                            class="w-full text-sm border border-gray-300 rounded px-2 py-1">
                                        <option value="0">Без данък</option>
                                        <option value="1" {{ (method.tax_class_id ?? 0) == 1 ? 'selected' : '' }}>ДДС 20%</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                <div class="mt-4 text-sm text-gray-500">
                    <i class="ri-information-line mr-1"></i>
                    Плъзнете и пуснете за да промените подредбата на методите за доставка
                </div>
            </div>

            <!-- Delivery Zones -->
            <div class="bg-white rounded shadow p-6 mt-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="ri-map-pin-line mr-2"></i>
                    Зони за доставка
                </h2>
                
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Зона</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Описание</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Статус</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Действия</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for geo_zone in geo_zones %}
                            <tr>
                                <td class="px-4 py-2 whitespace-nowrap">
                                    <span class="font-medium text-gray-900">{{ geo_zone.name }}</span>
                                </td>
                                <td class="px-4 py-2 whitespace-nowrap">
                                    <span class="text-sm text-gray-500">{{ geo_zone.description }}</span>
                                </td>
                                <td class="px-4 py-2 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                        Активна
                                    </span>
                                </td>
                                <td class="px-4 py-2 whitespace-nowrap">
                                    <button type="button" class="text-blue-600 hover:text-blue-900 text-sm">
                                        Редактирай
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- General Delivery Settings -->
            <div class="bg-white rounded shadow p-6 mt-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="ri-settings-3-line mr-2"></i>
                    Общи настройки за доставка
                </h2>
                
                <div class="space-y-4">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label for="delivery_time_min" class="block text-sm font-medium text-gray-700 mb-1">
                                Минимално време за доставка (дни)
                            </label>
                            <input type="number" 
                                   id="delivery_time_min" 
                                   name="delivery_time_min"
                                   class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" 
                                   value="{{ delivery_time_min ?? 1 }}"
                                   min="0"
                                   placeholder="1">
                        </div>

                        <div>
                            <label for="delivery_time_max" class="block text-sm font-medium text-gray-700 mb-1">
                                Максимално време за доставка (дни)
                            </label>
                            <input type="number" 
                                   id="delivery_time_max" 
                                   name="delivery_time_max"
                                   class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" 
                                   value="{{ delivery_time_max ?? 3 }}"
                                   min="1"
                                   placeholder="3">
                        </div>
                    </div>

                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label for="delivery_weight_class" class="block text-sm font-medium text-gray-700 mb-1">
                                Единица за тегло
                            </label>
                            <select id="delivery_weight_class" 
                                    name="delivery_weight_class"
                                    class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary">
                                {% for class_id, class_name in weight_classes %}
                                <option value="{{ class_id }}" {{ delivery_weight_class == class_id ? 'selected' : '' }}>
                                    {{ class_name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>

                        <div>
                            <label for="delivery_length_class" class="block text-sm font-medium text-gray-700 mb-1">
                                Единица за дължина
                            </label>
                            <select id="delivery_length_class" 
                                    name="delivery_length_class"
                                    class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary">
                                {% for class_id, class_name in length_classes %}
                                <option value="{{ class_id }}" {{ delivery_length_class == class_id ? 'selected' : '' }}>
                                    {{ class_name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>

                    <div class="space-y-3">
                        <div class="flex items-center justify-between">
                            <div>
                                <label for="delivery_auto_calculate" class="text-sm font-medium text-gray-700">
                                    Автоматично изчисляване на цени
                                </label>
                                <p class="text-xs text-gray-500">Автоматично изчислява цените според теглото и зоната</p>
                            </div>
                            <input type="checkbox" 
                                   id="delivery_auto_calculate" 
                                   name="delivery_auto_calculate"
                                   class="toggle-switch"
                                   {{ delivery_auto_calculate ? 'checked' : '' }}>
                        </div>

                        <div class="flex items-center justify-between">
                            <div>
                                <label for="delivery_display_weight" class="text-sm font-medium text-gray-700">
                                    Показване на тегло при доставка
                                </label>
                                <p class="text-xs text-gray-500">Показва теглото на поръчката в информацията за доставка</p>
                            </div>
                            <input type="checkbox" 
                                   id="delivery_display_weight" 
                                   name="delivery_display_weight"
                                   class="toggle-switch"
                                   {{ delivery_display_weight ? 'checked' : '' }}>
                        </div>

                        <div class="flex items-center justify-between">
                            <div>
                                <label for="delivery_display_time" class="text-sm font-medium text-gray-700">
                                    Показване на време за доставка
                                </label>
                                <p class="text-xs text-gray-500">Показва очакваното време за доставка на клиента</p>
                            </div>
                            <input type="checkbox" 
                                   id="delivery_display_time" 
                                   name="delivery_display_time"
                                   class="toggle-switch"
                                   {{ delivery_display_time ? 'checked' : '' }}>
                        </div>
                    </div>

                    <div>
                        <label for="delivery_terms" class="block text-sm font-medium text-gray-700 mb-1">
                            Условия за доставка
                        </label>
                        <textarea id="delivery_terms" 
                                  name="delivery_terms"
                                  rows="3" 
                                  class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
                                  maxlength="1000"
                                  placeholder="Въведете условията за доставка...">{{ delivery_terms ?? '' }}</textarea>
                        <p class="text-xs text-gray-500 mt-1">Максимум 1000 символа</p>
                    </div>

                    <div>
                        <label for="delivery_info" class="block text-sm font-medium text-gray-700 mb-1">
                            Информация за доставка
                        </label>
                        <textarea id="delivery_info" 
                                  name="delivery_info"
                                  rows="3" 
                                  class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
                                  maxlength="1000"
                                  placeholder="Въведете информация за доставка...">{{ delivery_info ?? '' }}</textarea>
                        <p class="text-xs text-gray-500 mt-1">Максимум 1000 символа</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Delivery Panel -->
        <div class="lg:col-span-1">
            <!-- Delivery Statistics -->
            <div class="bg-white rounded shadow p-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="ri-bar-chart-line mr-2"></i>
                    Статистики за доставка
                </h2>
                
                <div class="space-y-3">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Активни методи:</span>
                        <span id="active-shipping-methods-count" class="text-sm font-medium text-green-600">
                            {{ active_shipping_methods ?? 0 }}
                        </span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Общо методи:</span>
                        <span class="text-sm font-medium text-blue-600">
                            {{ total_shipping_methods ?? 0 }}
                        </span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Конфигурирани зони:</span>
                        <span class="text-sm font-medium text-purple-600">
                            {{ configured_zones ?? 0 }}
                        </span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Средно време:</span>
                        <span class="text-sm font-medium text-gray-600">
                            {{ average_delivery_time ?? '1-3 дни' }}
                        </span>
                    </div>
                </div>
            </div>

            <!-- Shipping Calculator -->
            <div class="bg-white rounded shadow p-6 mt-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="ri-calculator-line mr-2"></i>
                    Калкулатор за доставка
                </h2>
                
                <div class="space-y-3">
                    <div>
                        <label for="calc-weight" class="block text-sm font-medium text-gray-700 mb-1">
                            Тегло (кг)
                        </label>
                        <input type="number" 
                               id="calc-weight"
                               class="w-full px-3 py-2 border border-gray-300 rounded text-sm"
                               placeholder="1.5"
                               step="0.01"
                               min="0">
                    </div>
                    
                    <div>
                        <label for="calc-total" class="block text-sm font-medium text-gray-700 mb-1">
                            Стойност (лв.)
                        </label>
                        <input type="number" 
                               id="calc-total"
                               class="w-full px-3 py-2 border border-gray-300 rounded text-sm"
                               placeholder="50.00"
                               step="0.01"
                               min="0">
                    </div>
                    
                    <div>
                        <label for="calc-country" class="block text-sm font-medium text-gray-700 mb-1">
                            Държава
                        </label>
                        <select id="calc-country" class="w-full px-3 py-2 border border-gray-300 rounded text-sm">
                            <option value="">Изберете държава</option>
                            {% for country in countries %}
                            <option value="{{ country.country_id }}">{{ country.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <button type="button" 
                            id="calculate-shipping"
                            class="w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors">
                        <i class="ri-calculator-line mr-2"></i>
                        Калкулирай
                    </button>
                </div>
                
                <div id="shipping-calculation-results"></div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded shadow p-6 mt-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="ri-tools-line mr-2"></i>
                    Бързи действия
                </h2>
                
                <div class="space-y-3">
                    <button type="submit" 
                            id="save-delivery-settings"
                            class="w-full px-4 py-2 bg-primary text-white rounded hover:bg-primary/90 transition-colors">
                        <i class="ri-save-line mr-2"></i>
                        Запази настройки
                    </button>

                    <button type="button" 
                            onclick="location.reload()"
                            class="w-full px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors">
                        <i class="ri-refresh-line mr-2"></i>
                        Презареди
                    </button>
                </div>
            </div>

            <!-- Delivery Tips -->
            <div class="bg-green-50 border border-green-200 rounded p-6 mt-6">
                <h2 class="text-lg font-semibold text-green-800 mb-4">
                    <i class="ri-lightbulb-line mr-2"></i>
                    Съвети за доставка
                </h2>
                
                <div class="space-y-3 text-sm text-green-700">
                    <div class="flex items-start">
                        <i class="ri-check-line text-green-500 mr-2 mt-0.5"></i>
                        <span>Предложете безплатна доставка при определена сума за увеличаване на продажбите</span>
                    </div>
                    
                    <div class="flex items-start">
                        <i class="ri-check-line text-green-500 mr-2 mt-0.5"></i>
                        <span>Конфигурирайте различни цени за различни зони</span>
                    </div>
                    
                    <div class="flex items-start">
                        <i class="ri-check-line text-green-500 mr-2 mt-0.5"></i>
                        <span>Тествайте методите за доставка преди да ги активирате</span>
                    </div>
                    
                    <div class="flex items-start">
                        <i class="ri-check-line text-green-500 mr-2 mt-0.5"></i>
                        <span>Задайте реалистично време за доставка</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
