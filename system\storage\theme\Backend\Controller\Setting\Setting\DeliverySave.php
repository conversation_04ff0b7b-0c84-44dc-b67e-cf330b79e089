<?php

namespace Theme25\Backend\Controller\Setting\Setting;

/**
 * Sub-контролер за AJAX запазване на настройки за доставка
 */
class DeliverySave extends \Theme25\ControllerSubMethods {

    /**
     * Изпълнява AJAX запазване на настройки за доставка
     */
    public function execute() {
        $json = [];

        // Проверка за права на достъп
        if (!$this->hasPermission('modify', 'setting/setting')) {
            $json['error'] = 'Нямате права за редактиране на настройки за доставка';
            $this->outputJson($json);
            return;
        }

        // Проверка за POST заявка
        if (!$this->isPostRequest()) {
            $json['error'] = 'Невалидна заявка';
            $this->outputJson($json);
            return;
        }

        try {
            // Получаване на данните от POST
            $postData = $this->requestPost();
            
            // Валидация на данните
            $validation_errors = $this->validateDeliverySettings($postData);
            if (!empty($validation_errors)) {
                $json['error'] = 'Грешки при валидацията';
                $json['validation_errors'] = $validation_errors;
                $this->outputJson($json);
                return;
            }

            $this->loadModelAs('setting/delivery_settings_model', 'deliverySettings');
            
            // Запазване на настройките
            $result = $this->deliverySettings->saveDeliverySettings($postData);
            
            if ($result === true) {
                $json['success'] = 'Настройките за доставка са запазени успешно';
                $json['timestamp'] = date('d.m.Y H:i:s');
                
                // Добавяне на актуализирани данни
                $json['updated_data'] = $this->getUpdatedDeliveryData($postData);
                
                // Предупреждения ако има
                $warnings = $this->checkDeliveryWarnings($postData);
                if (!empty($warnings)) {
                    $json['warnings'] = $warnings;
                }
            } else {
                // Има грешки при валидацията от модела
                $json['error'] = 'Грешки при валидацията';
                $json['validation_errors'] = $result;
            }

        } catch (Exception $e) {
            $json['error'] = 'Грешка при запазване: ' . $e->getMessage();
        }

        $this->outputJson($json);
    }

    /**
     * Валидира настройките за доставка
     *
     * @param array $data Данни за валидация
     * @return array Масив с грешки
     */
    private function validateDeliverySettings($data) {
        $errors = [];

        // Валидация на минимално време за доставка
        if (isset($data['delivery_time_min'])) {
            $min_time = (int)$data['delivery_time_min'];
            if ($min_time < 0) {
                $errors['delivery_time_min'] = 'Минималното време за доставка не може да бъде отрицателно';
            }
        }

        // Валидация на максимално време за доставка
        if (isset($data['delivery_time_max'])) {
            $max_time = (int)$data['delivery_time_max'];
            if ($max_time < 1) {
                $errors['delivery_time_max'] = 'Максималното време за доставка трябва да бъде поне 1 ден';
            }
            
            // Проверка дали максималното време е по-голямо от минималното
            if (isset($data['delivery_time_min'])) {
                $min_time = (int)$data['delivery_time_min'];
                if ($max_time <= $min_time) {
                    $errors['delivery_time_max'] = 'Максималното време трябва да бъде по-голямо от минималното';
                }
            }
        }

        // Валидация на условия за доставка
        if (!empty($data['delivery_terms']) && mb_strlen($data['delivery_terms']) > 1000) {
            $errors['delivery_terms'] = 'Условията за доставка не могат да бъдат повече от 1000 символа';
        }

        // Валидация на информация за доставка
        if (!empty($data['delivery_info']) && mb_strlen($data['delivery_info']) > 1000) {
            $errors['delivery_info'] = 'Информацията за доставка не може да бъде повече от 1000 символа';
        }

        // Валидация на shipping методи
        if (isset($data['shipping_methods']) && is_array($data['shipping_methods'])) {
            foreach ($data['shipping_methods'] as $method_code => $method_data) {
                // Валидация на цена
                if (isset($method_data['cost'])) {
                    $cost = (float)$method_data['cost'];
                    if ($cost < 0) {
                        $errors['shipping_methods'][$method_code]['cost'] = 'Цената не може да бъде отрицателна';
                    }
                }
                
                // Валидация на минимална сума за безплатна доставка
                if (isset($method_data['minimum_total'])) {
                    $min_total = (float)$method_data['minimum_total'];
                    if ($min_total < 0) {
                        $errors['shipping_methods'][$method_code]['minimum_total'] = 'Минималната сума не може да бъде отрицателна';
                    }
                }
                
                // Валидация на sort_order
                if (isset($method_data['sort_order'])) {
                    $sort_order = (int)$method_data['sort_order'];
                    if ($sort_order < 0) {
                        $errors['shipping_methods'][$method_code]['sort_order'] = 'Подредбата не може да бъде отрицателна';
                    }
                }
                
                // Валидация на shipping rates за weight-based методи
                if ($method_code === 'weight' && isset($method_data['rates']) && is_array($method_data['rates'])) {
                    foreach ($method_data['rates'] as $rate_index => $rate) {
                        if (isset($rate['weight_from']) && isset($rate['weight_to'])) {
                            $weight_from = (float)$rate['weight_from'];
                            $weight_to = (float)$rate['weight_to'];
                            
                            if ($weight_from < 0) {
                                $errors['shipping_methods'][$method_code]['rates'][$rate_index]['weight_from'] = 'Теглото не може да бъде отрицателно';
                            }
                            
                            if ($weight_to <= $weight_from) {
                                $errors['shipping_methods'][$method_code]['rates'][$rate_index]['weight_to'] = 'Крайното тегло трябва да бъде по-голямо от началното';
                            }
                        }
                        
                        if (isset($rate['cost'])) {
                            $rate_cost = (float)$rate['cost'];
                            if ($rate_cost < 0) {
                                $errors['shipping_methods'][$method_code]['rates'][$rate_index]['cost'] = 'Цената не може да бъде отрицателна';
                            }
                        }
                    }
                }
            }
        }

        return $errors;
    }

    /**
     * Проверява за предупреждения относно доставката
     *
     * @param array $data Данни за проверка
     * @return array Масив с предупреждения
     */
    private function checkDeliveryWarnings($data) {
        $warnings = [];

        // Проверка за активни shipping методи
        $active_methods = 0;
        if (isset($data['shipping_methods']) && is_array($data['shipping_methods'])) {
            foreach ($data['shipping_methods'] as $method_data) {
                if (!empty($method_data['status'])) {
                    $active_methods++;
                }
            }
        }

        if ($active_methods === 0) {
            $warnings[] = [
                'type' => 'error',
                'message' => 'ВНИМАНИЕ: Няма активни методи за доставка! Клиентите няма да могат да завършат поръчки.'
            ];
        } elseif ($active_methods === 1) {
            $warnings[] = [
                'type' => 'warning',
                'message' => 'Имате само един активен метод за доставка. Препоръчваме да активирате поне два метода.'
            ];
        }

        // Проверка за време за доставка
        if (isset($data['delivery_time_min']) && isset($data['delivery_time_max'])) {
            $min_time = (int)$data['delivery_time_min'];
            $max_time = (int)$data['delivery_time_max'];
            
            if ($max_time - $min_time > 7) {
                $warnings[] = [
                    'type' => 'info',
                    'message' => 'Голяма разлика във времето за доставка (' . $min_time . '-' . $max_time . ' дни). Това може да объркае клиентите.'
                ];
            }
        }

        // Проверка за безплатна доставка
        $has_free_shipping = false;
        if (isset($data['shipping_methods']['free']) && !empty($data['shipping_methods']['free']['status'])) {
            $has_free_shipping = true;
            $min_total = (float)($data['shipping_methods']['free']['minimum_total'] ?? 0);
            
            if ($min_total > 100) {
                $warnings[] = [
                    'type' => 'warning',
                    'message' => 'Високата минимална сума за безплатна доставка (' . $min_total . ' лв.) може да обезкуражи клиентите.'
                ];
            }
        }

        if (!$has_free_shipping) {
            $warnings[] = [
                'type' => 'info',
                'message' => 'Няма активирана безплатна доставка. Това може да увеличи продажбите.'
            ];
        }

        return $warnings;
    }

    /**
     * Получава актуализираните данни за настройките за доставка
     *
     * @param array $postData POST данни
     * @return array
     */
    private function getUpdatedDeliveryData($postData) {
        $updated_data = [
            'delivery_terms' => $postData['delivery_terms'] ?? '',
            'delivery_info' => $postData['delivery_info'] ?? '',
            'delivery_time_min' => (int)($postData['delivery_time_min'] ?? 1),
            'delivery_time_max' => (int)($postData['delivery_time_max'] ?? 3),
            'delivery_weight_class' => (int)($postData['delivery_weight_class'] ?? 1),
            'delivery_length_class' => (int)($postData['delivery_length_class'] ?? 1),
            'delivery_tax_class' => (int)($postData['delivery_tax_class'] ?? 0),
            'delivery_auto_calculate' => isset($postData['delivery_auto_calculate']) ? 1 : 0,
            'delivery_display_weight' => isset($postData['delivery_display_weight']) ? 1 : 0,
            'delivery_display_time' => isset($postData['delivery_display_time']) ? 1 : 0,
            'delivery_sort_order' => (int)($postData['delivery_sort_order'] ?? 1),
            'delivery_status' => isset($postData['delivery_status']) ? 1 : 0
        ];

        // Обработка на shipping методи
        if (isset($postData['shipping_methods']) && is_array($postData['shipping_methods'])) {
            $updated_data['shipping_methods'] = [];
            foreach ($postData['shipping_methods'] as $method_code => $method_data) {
                $updated_data['shipping_methods'][$method_code] = [
                    'status' => isset($method_data['status']) ? 1 : 0,
                    'sort_order' => (int)($method_data['sort_order'] ?? 0),
                    'cost' => (float)($method_data['cost'] ?? 0),
                    'minimum_total' => (float)($method_data['minimum_total'] ?? 0),
                    'geo_zone_id' => (int)($method_data['geo_zone_id'] ?? 0),
                    'tax_class_id' => (int)($method_data['tax_class_id'] ?? 0)
                ];
                
                // Специална обработка за weight-based rates
                if ($method_code === 'weight' && isset($method_data['rates']) && is_array($method_data['rates'])) {
                    $updated_data['shipping_methods'][$method_code]['rates'] = [];
                    foreach ($method_data['rates'] as $rate) {
                        if (!empty($rate['weight_from']) || !empty($rate['weight_to']) || !empty($rate['cost'])) {
                            $updated_data['shipping_methods'][$method_code]['rates'][] = [
                                'weight_from' => (float)($rate['weight_from'] ?? 0),
                                'weight_to' => (float)($rate['weight_to'] ?? 0),
                                'cost' => (float)($rate['cost'] ?? 0)
                            ];
                        }
                    }
                }
            }
        }

        return $updated_data;
    }

    /**
     * Изпраща JSON отговор
     *
     * @param array $data
     */
    private function outputJson($data) {
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($data));
    }

    /**
     * Логира действието за запазване на настройки за доставка
     *
     * @param array $data Запазените данни
     */
    private function logDeliverySettingsSave($data) {
        try {
            $log_data = [
                'action' => 'delivery_settings_save',
                'user_id' => $this->user->getId(),
                'user_name' => $this->user->getUserName(),
                'timestamp' => date('Y-m-d H:i:s'),
                'ip_address' => $this->request->server['REMOTE_ADDR'] ?? 'unknown',
                'changed_fields' => array_keys($data)
            ];
            
            // Логиране в системния лог
            $this->log->write('Delivery Settings: Updated by user ' . $this->user->getUserName() . ' (' . $this->user->getId() . ')');
            
        } catch (Exception $e) {
            // Ако логирането се провали, не спираме операцията
            error_log('Failed to log delivery settings save: ' . $e->getMessage());
        }
    }
}
