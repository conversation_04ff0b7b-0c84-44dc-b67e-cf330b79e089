<!-- sub Header -->
<header class="bg-white border-b border-gray-200" style="margin-top: -2px; position: relative;">
    <div class="flex border-b border-gray-200">
        <button id="tab-basic" class="tab-button {{ current_tab == 'basic' ? 'active text-primary' : 'text-gray-600 hover:text-primary' }} px-6 py-3 whitespace-nowrap" data-tab="basic">Основни настройки</button>
        <button id="tab-security" class="tab-button {{ current_tab == 'security' ? 'active text-primary' : 'text-gray-600 hover:text-primary' }} px-6 py-3 whitespace-nowrap" data-tab="security">Сигурност</button>
        <button id="tab-payment" class="tab-button {{ current_tab == 'payment' ? 'active text-primary' : 'text-gray-600 hover:text-primary' }} px-6 py-3 whitespace-nowrap" data-tab="payment">Плащания</button>
        <button id="tab-delivery" class="tab-button {{ current_tab == 'delivery' ? 'active text-primary' : 'text-gray-600 hover:text-primary' }} px-6 py-3 whitespace-nowrap" data-tab="delivery">Доставка</button>
        <button id="tab-notifications" class="tab-button {{ current_tab == 'notifications' ? 'active text-primary' : 'text-gray-600 hover:text-primary' }} px-6 py-3 whitespace-nowrap" data-tab="notifications">Известия</button>
        <button id="tab-integrations" class="tab-button {{ current_tab == 'integrations' ? 'active text-primary' : 'text-gray-600 hover:text-primary' }} px-6 py-3 whitespace-nowrap" data-tab="integrations">Интеграции</button>
        <button id="tab-admin_users" class="tab-button {{ current_tab == 'admin_users' ? 'active text-primary' : 'text-gray-600 hover:text-primary' }} px-6 py-3 whitespace-nowrap" data-tab="admin_users">Админ потребители</button>
    </div>
</header>

<!-- Main Content Area -->
<main class="flex-1 overflow-y-auto p-6 bg-gray-50">
    <!-- Tab Contents -->
    <div class="tab-contents">
        <!-- Basic Settings Tab -->
        <div id="content-basic" class="tab-content {{ current_tab == 'basic' ? '' : 'hidden' }}">
            {% if current_tab == 'basic' %}
                {% include 'setting/tabs/basic.twig' %}
            {% else %}
                <div class="bg-white rounded shadow p-6">
                    <div class="text-center py-8">
                        <i class="ri-store-line text-4xl text-gray-400 mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Основни настройки</h3>
                        <p class="text-gray-500">Кликнете върху таба за да заредите настройките</p>
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- Security Settings Tab -->
        <div id="content-security" class="tab-content {{ current_tab == 'security' ? '' : 'hidden' }}">
            {% if current_tab == 'security' %}
                {% include 'setting/tabs/security.twig' %}
            {% else %}
                <div class="bg-white rounded shadow p-6">
                    <div class="text-center py-8">
                        <i class="ri-shield-line text-4xl text-gray-400 mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Настройки за сигурност</h3>
                        <p class="text-gray-500">Кликнете върху таба за да заредите настройките</p>
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- Payment Settings Tab -->
        <div id="content-payment" class="tab-content {{ current_tab == 'payment' ? '' : 'hidden' }}">
            {% if current_tab == 'payment' %}
                {% include 'setting/tabs/payment.twig' %}
            {% else %}
                <div class="bg-white rounded shadow p-6">
                    <div class="text-center py-8">
                        <i class="ri-bank-card-line text-4xl text-gray-400 mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Настройки за плащания</h3>
                        <p class="text-gray-500">Кликнете върху таба за да заредите настройките</p>
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- Delivery Settings Tab -->
        <div id="content-delivery" class="tab-content {{ current_tab == 'delivery' ? '' : 'hidden' }}">
            {% if current_tab == 'delivery' %}
                {% include 'setting/tabs/delivery.twig' %}
            {% else %}
                <div class="bg-white rounded shadow p-6">
                    <div class="text-center py-8">
                        <i class="ri-truck-line text-4xl text-gray-400 mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Настройки за доставка</h3>
                        <p class="text-gray-500">Кликнете върху таба за да заредите настройките</p>
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- Notifications Settings Tab -->
        <div id="content-notifications" class="tab-content {{ current_tab == 'notifications' ? '' : 'hidden' }}">
            {% if current_tab == 'notifications' %}
                {% include 'setting/tabs/notifications.twig' %}
            {% else %}
                <div class="bg-white rounded shadow p-6">
                    <div class="text-center py-8">
                        <i class="ri-notification-line text-4xl text-gray-400 mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Настройки за известия</h3>
                        <p class="text-gray-500">Кликнете върху таба за да заредите настройките</p>
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- Integrations Settings Tab -->
        <div id="content-integrations" class="tab-content {{ current_tab == 'integrations' ? '' : 'hidden' }}">
            {% if current_tab == 'integrations' %}
                {% include 'setting/tabs/integrations.twig' %}
            {% else %}
                <div class="bg-white rounded shadow p-6">
                    <div class="text-center py-8">
                        <i class="ri-links-line text-4xl text-gray-400 mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Интеграции</h3>
                        <p class="text-gray-500">Кликнете върху таба за да заредите настройките</p>
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- Admin Users Settings Tab -->
        <div id="content-admin_users" class="tab-content {{ current_tab == 'admin_users' ? '' : 'hidden' }}">
            {% if current_tab == 'admin_users' %}
                {% include 'setting/tabs/admin-users.twig' %}
            {% else %}
                <div class="bg-white rounded shadow p-6">
                    <div class="text-center py-8">
                        <i class="ri-admin-line text-4xl text-gray-400 mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Админ потребители</h3>
                        <p class="text-gray-500">Кликнете върху таба за да заредите настройките</p>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</main>

<!-- JavaScript Configuration -->
<script>
window.settingsConfig = {
    userToken: '{{ user_token }}',
    currentTab: '{{ current_tab }}',
    baseUrl: '{{ base_url }}',
    ajaxUrls: {
        basic_save: '{{ ajax_urls.basic_save ?? "" }}',
        upload_logo: '{{ ajax_urls.upload_logo ?? "" }}',
        test_email: '{{ ajax_urls.test_email ?? "" }}',
        security_update: '{{ ajax_urls.security_update ?? "" }}',
        payment_save: '{{ ajax_urls.payment_save ?? "" }}',
        test_payment: '{{ ajax_urls.test_payment ?? "" }}',
        update_currencies: '{{ ajax_urls.update_currencies ?? "" }}',
        toggle_payment_method: '{{ ajax_urls.toggle_payment_method ?? "" }}',
        delivery_save: '{{ ajax_urls.delivery_save ?? "" }}',
        test_shipping: '{{ ajax_urls.test_shipping ?? "" }}',
        calculate_shipping: '{{ ajax_urls.calculate_shipping ?? "" }}',
        toggle_shipping_method: '{{ ajax_urls.toggle_shipping_method ?? "" }}',
        notifications_save: '{{ ajax_urls.notifications_save ?? "" }}',
        test_email_settings: '{{ ajax_urls.test_email_settings ?? "" }}',
        test_sms: '{{ ajax_urls.test_sms ?? "" }}',
        preview_template: '{{ ajax_urls.preview_template ?? "" }}',
        send_test_notification: '{{ ajax_urls.send_test_notification ?? "" }}',
        reset_template: '{{ ajax_urls.reset_template ?? "" }}',
        integrations_save: '{{ ajax_urls.integrations_save ?? "" }}',
        test_integration: '{{ ajax_urls.test_integration ?? "" }}',
        toggle_integration: '{{ ajax_urls.toggle_integration ?? "" }}',
        generate_api_key: '{{ ajax_urls.generate_api_key ?? "" }}',
        sync_marketplace: '{{ ajax_urls.sync_marketplace ?? "" }}',
        add_webhook: '{{ ajax_urls.add_webhook ?? "" }}',
        remove_webhook: '{{ ajax_urls.remove_webhook ?? "" }}',
        test_webhook: '{{ ajax_urls.test_webhook ?? "" }}',
        admin_users_save: '{{ ajax_urls.admin_users_save ?? "" }}',
        add_user: '{{ ajax_urls.add_user ?? "" }}',
        edit_user: '{{ ajax_urls.edit_user ?? "" }}',
        delete_user: '{{ ajax_urls.delete_user ?? "" }}',
        toggle_user_status: '{{ ajax_urls.toggle_user_status ?? "" }}',
        reset_password: '{{ ajax_urls.reset_password ?? "" }}',
        unlock_user: '{{ ajax_urls.unlock_user ?? "" }}',
        add_user_group: '{{ ajax_urls.add_user_group ?? "" }}',
        edit_user_group: '{{ ajax_urls.edit_user_group ?? "" }}',
        delete_user_group: '{{ ajax_urls.delete_user_group ?? "" }}',
        update_permissions: '{{ ajax_urls.update_permissions ?? "" }}',
        test_security: '{{ ajax_urls.test_security ?? "" }}',
        export_activity_logs: '{{ ajax_urls.export_activity_logs ?? "" }}',
        clear_activity_logs: '{{ ajax_urls.clear_activity_logs ?? "" }}'
    }
};

// Tab switching functionality with AJAX loading and caching
document.addEventListener('DOMContentLoaded', function() {
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');
    const tabCache = new Map(); // Cache for loaded tab content
    const tabFormData = new Map(); // Cache for form data to preserve changes

    // Initialize settings module if available
    if (typeof BackendModule !== 'undefined' && BackendModule.settings) {
        window.settingsModule = BackendModule;
        // Ensure settings config is loaded
        if (window.settingsConfig) {
            BackendModule.settings.config = { ...BackendModule.settings.config, ...window.settingsConfig };
        }
    }

    // Tab switching logic
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');

            // Don't reload if clicking on active tab
            if (this.classList.contains('active')) {
                return;
            }

            // Save current form data before switching
            saveCurrentTabFormData();

            // Update active tab button
            tabButtons.forEach(btn => {
                btn.classList.remove('active', 'text-primary');
                btn.classList.add('text-gray-600');
            });
            this.classList.add('active', 'text-primary');
            this.classList.remove('text-gray-600');

            // Hide all tab contents
            tabContents.forEach(content => {
                content.classList.add('hidden');
            });

            // Show target tab content
            const targetContent = document.getElementById('content-' + targetTab);
            if (targetContent) {
                targetContent.classList.remove('hidden');

                // Load content via AJAX if not cached
                loadTabContent(targetTab, targetContent);
            }
        });
    });

    // Function to save current form data
    function saveCurrentTabFormData() {
        const activeTab = document.querySelector('.tab-button.active');
        if (activeTab) {
            const tabId = activeTab.getAttribute('data-tab');
            const activeContent = document.getElementById('content-' + tabId);
            if (activeContent) {
                const forms = activeContent.querySelectorAll('form');
                forms.forEach(form => {
                    const formData = new FormData(form);
                    const formDataObj = {};
                    for (let [key, value] of formData.entries()) {
                        formDataObj[key] = value;
                    }
                    tabFormData.set(tabId, formDataObj);
                });
            }
        }
    }

    // Function to restore form data
    function restoreTabFormData(tabId, content) {
        const savedData = tabFormData.get(tabId);
        if (savedData) {
            const forms = content.querySelectorAll('form');
            forms.forEach(form => {
                Object.keys(savedData).forEach(key => {
                    const field = form.querySelector(`[name="${key}"]`);
                    if (field) {
                        if (field.type === 'checkbox' || field.type === 'radio') {
                            field.checked = savedData[key] === field.value;
                        } else {
                            field.value = savedData[key];
                        }
                    }
                });
            });
        }
    }

    // Function to load tab content via AJAX
    function loadTabContent(tabId, contentElement) {
        // Check if content is already loaded and cached
        if (tabCache.has(tabId)) {
            contentElement.innerHTML = tabCache.get(tabId);
            restoreTabFormData(tabId, contentElement);
            initializeTabSpecificFunctionality(tabId);
            return;
        }

        // Check if content is already loaded (not placeholder)
        const placeholder = contentElement.querySelector('.text-center.py-8');
        if (!placeholder) {
            // Content is already loaded, cache it
            tabCache.set(tabId, contentElement.innerHTML);
            restoreTabFormData(tabId, contentElement);
            initializeTabSpecificFunctionality(tabId);
            return;
        }

        // Show loading state
        contentElement.innerHTML = `
            <div class="bg-white rounded shadow p-6">
                <div class="text-center py-8">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Зареждане...</h3>
                    <p class="text-gray-500">Моля, изчакайте</p>
                </div>
            </div>
        `;

        // Load content via AJAX
        const url = window.settingsConfig.baseUrl + '&tab=' + tabId;

        fetch(url, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.text())
        .then(html => {
            // Extract the tab content from the response
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            const newContent = doc.getElementById('content-' + tabId);

            if (newContent) {
                contentElement.innerHTML = newContent.innerHTML;
                // Cache the loaded content
                tabCache.set(tabId, newContent.innerHTML);
                restoreTabFormData(tabId, contentElement);
                initializeTabSpecificFunctionality(tabId);
            } else {
                // Fallback if content not found
                contentElement.innerHTML = `
                    <div class="bg-white rounded shadow p-6">
                        <div class="text-center py-8">
                            <i class="ri-error-warning-line text-4xl text-red-400 mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">Грешка при зареждане</h3>
                            <p class="text-gray-500">Не можа да се зареди съдържанието</p>
                            <button onclick="location.reload()" class="mt-4 px-4 py-2 bg-primary text-white rounded hover:bg-primary/90">
                                Опитай отново
                            </button>
                        </div>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error loading tab content:', error);
            contentElement.innerHTML = `
                <div class="bg-white rounded shadow p-6">
                    <div class="text-center py-8">
                        <i class="ri-wifi-off-line text-4xl text-red-400 mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Грешка в мрежата</h3>
                        <p class="text-gray-500">Проверете интернет връзката</p>
                        <button onclick="location.reload()" class="mt-4 px-4 py-2 bg-primary text-white rounded hover:bg-primary/90">
                            Опитай отново
                        </button>
                    </div>
                </div>
            `;
        });
    }

    // Function to initialize tab-specific functionality
    function initializeTabSpecificFunctionality(tabId) {
        if (typeof window.settingsModule !== 'undefined') {
            // Use BackendModule's loadSettingsModule method if available
            if (typeof window.settingsModule.loadSettingsModule === 'function') {
                window.settingsModule.loadSettingsModule(tabId);
            } else {
                // Fallback to direct method calls
                const methodName = 'init' + tabId.charAt(0).toUpperCase() + tabId.slice(1).replace('_', '') + 'Settings';
                if (typeof window.settingsModule[methodName] === 'function') {
                    window.settingsModule[methodName]();
                }
            }
        }
    }

    // Auto-save functionality to prevent data loss
    let autoSaveTimeout;
    document.addEventListener('input', function(e) {
        if (e.target.closest('.tab-content')) {
            clearTimeout(autoSaveTimeout);
            autoSaveTimeout = setTimeout(() => {
                saveCurrentTabFormData();
            }, 1000); // Save after 1 second of inactivity
        }
    });

    // Initialize the current tab
    const currentTab = window.settingsConfig.currentTab || 'basic';
    const currentTabButton = document.getElementById('tab-' + currentTab);
    const currentTabContent = document.getElementById('content-' + currentTab);

    if (currentTabButton && currentTabContent) {
        // Ensure current tab is marked as active
        tabButtons.forEach(btn => {
            btn.classList.remove('active', 'text-primary');
            btn.classList.add('text-gray-600');
        });
        currentTabButton.classList.add('active', 'text-primary');
        currentTabButton.classList.remove('text-gray-600');

        // Initialize current tab functionality
        initializeTabSpecificFunctionality(currentTab);
    }
});
</script>
