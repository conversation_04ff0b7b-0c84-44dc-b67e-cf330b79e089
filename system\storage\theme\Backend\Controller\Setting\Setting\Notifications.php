<?php

namespace Theme25\Backend\Controller\Setting\Setting;

/**
 * Sub-контролер за настройки за известия
 *
 * Този контролер управлява логиката за показване и обработка на настройките за известия,
 * включително email настройки, SMTP конфигурация, email шаблони и notification settings.
 *
 * @package Theme25\Backend\Controller\Setting\Setting
 * <AUTHOR> Development Team
 * @version 1.0.0
 */
class Notifications extends \Theme25\ControllerSubMethods {

    /**
     * Подготвя данните за настройките за известия
     */
    public function prepareData() {
        $this->prepareEmailSettingsData()
             ->prepareEmailTemplatesData()
             ->prepareNotificationSettingsData()
             ->prepareSMSSettingsData()
             ->prepareUrlsAndActions()
             ->prepareValidationRules();

        // Подготвяне на JavaScript конфигурация
        $this->setData([
            'user_token' => $this->getUserToken()
        ]);
    }

    /**
     * Подготвя данните за email настройки
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareEmailSettingsData() {
        try {
            $this->loadModelAs('setting/notifications_settings_model', 'notificationsSettings');
            $email_settings = $this->notificationsSettings->getEmailSettings();
            
            $this->setData('email_settings', $email_settings);
            
        } catch (Exception $e) {
            $this->setError('Грешка при зареждане на email настройки: ' . $e->getMessage());
            
            // Задаване на стойности по подразбиране
            $this->setData('email_settings', [
                'config_mail_protocol' => 'mail',
                'config_mail_parameter' => '',
                'config_mail_smtp_hostname' => '',
                'config_mail_smtp_username' => '',
                'config_mail_smtp_password' => '',
                'config_mail_smtp_port' => 587,
                'config_mail_smtp_timeout' => 5,
                'config_email' => '',
                'config_name' => '',
                'config_mail_alert_email' => '',
                'config_mail_engine' => 'mail'
            ]);
        }

        // Mail protocols
        $mail_protocols = [
            'mail' => 'PHP Mail',
            'smtp' => 'SMTP'
        ];

        $this->setData('mail_protocols', $mail_protocols);

        // SMTP encryption types
        $smtp_encryption = [
            '' => 'Без шифроване',
            'tls' => 'TLS',
            'ssl' => 'SSL'
        ];

        $this->setData('smtp_encryption', $smtp_encryption);

        return $this;
    }

    /**
     * Подготвя данните за email шаблони
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareEmailTemplatesData() {
        try {
            $this->loadModelAs('setting/notifications_settings_model', 'notificationsSettings');
            $email_templates = $this->notificationsSettings->getEmailTemplates();
            
            $this->setData('email_templates', $email_templates);
            
        } catch (Exception $e) {
            $this->setError('Грешка при зареждане на email шаблони: ' . $e->getMessage());
            
            // Задаване на стойности по подразбиране
            $this->setData('email_templates', [
                'order_confirmation' => [
                    'name' => 'Потвърждение на поръчка',
                    'subject' => 'Потвърждение на поръчка #{order_id}',
                    'content' => "Здравейте {firstname},\n\nБлагодарим ви за поръчката #{order_id}.\n\nОбща сума: {total}\nСтатус: {order_status}\n\nС уважение,\n{store_name}",
                    'status' => 1,
                    'variables' => ['firstname', 'order_id', 'total', 'order_status', 'store_name']
                ],
                'order_shipped' => [
                    'name' => 'Поръчката е изпратена',
                    'subject' => 'Поръчка #{order_id} е изпратена',
                    'content' => "Здравейте {firstname},\n\nВашата поръчка #{order_id} е изпратена.\n\nМетод на доставка: {shipping_method}\n\nС уважение,\n{store_name}",
                    'status' => 1,
                    'variables' => ['firstname', 'order_id', 'shipping_method', 'store_name']
                ],
                'customer_registration' => [
                    'name' => 'Регистрация на клиент',
                    'subject' => 'Добре дошли в {store_name}',
                    'content' => "Здравейте {firstname},\n\nДобре дошли в {store_name}!\n\nВашият акаунт е създаден успешно.\n\nС уважение,\n{store_name}",
                    'status' => 1,
                    'variables' => ['firstname', 'store_name']
                ],
                'password_reset' => [
                    'name' => 'Възстановяване на парола',
                    'subject' => 'Възстановяване на парола',
                    'content' => "Здравейте {firstname},\n\nПолучихме заявка за възстановяване на паролата ви.\n\nКликнете тук: {reset_link}\n\nС уважение,\n{store_name}",
                    'status' => 1,
                    'variables' => ['firstname', 'reset_link', 'store_name']
                ]
            ]);
        }

        return $this;
    }

    /**
     * Подготвя основните настройки за известия
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareNotificationSettingsData() {
        try {
            $this->loadModelAs('setting/notifications_settings_model', 'notificationsSettings');
            $settings = $this->notificationsSettings->getNotificationSettings();
            
            // Добавяне на настройките към данните
            foreach ($settings as $key => $value) {
                $this->setData($key, $value);
            }
            
        } catch (Exception $e) {
            $this->setError('Грешка при зареждане на настройки за известия: ' . $e->getMessage());
            
            // Задаване на стойности по подразбиране
            $this->setData([
                'notification_email_enabled' => 1,
                'notification_sms_enabled' => 0,
                'notification_admin_new_order' => 1,
                'notification_admin_new_customer' => 1,
                'notification_customer_order_status' => 1,
                'notification_customer_registration' => 1,
                'notification_customer_password_reset' => 1,
                'notification_low_stock' => 1,
                'notification_low_stock_threshold' => 5,
                'notification_admin_emails' => '',
                'notification_frequency' => 'immediate',
                'notification_queue_enabled' => 0,
                'notification_retry_attempts' => 3,
                'notification_retry_delay' => 300
            ]);
        }

        // Notification frequencies
        $notification_frequencies = [
            'immediate' => 'Незабавно',
            'hourly' => 'Всеки час',
            'daily' => 'Ежедневно',
            'weekly' => 'Седмично'
        ];

        $this->setData('notification_frequencies', $notification_frequencies);

        return $this;
    }

    /**
     * Подготвя данните за SMS настройки
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareSMSSettingsData() {
        try {
            $this->loadModelAs('setting/notifications_settings_model', 'notificationsSettings');
            $sms_settings = $this->notificationsSettings->getSMSSettings();
            
            $this->setData('sms_settings', $sms_settings);
            
        } catch (Exception $e) {
            // Задаване на стойности по подразбиране
            $this->setData('sms_settings', [
                'sms_provider' => '',
                'sms_api_key' => '',
                'sms_api_secret' => '',
                'sms_sender_name' => '',
                'sms_enabled' => 0
            ]);
        }

        // SMS providers
        $sms_providers = [
            '' => 'Изберете доставчик',
            'twilio' => 'Twilio',
            'nexmo' => 'Nexmo/Vonage',
            'clickatell' => 'Clickatell',
            'bulksms' => 'BulkSMS'
        ];

        $this->setData('sms_providers', $sms_providers);

        return $this;
    }

    /**
     * Подготвя URL-и за AJAX заявки и действия
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareUrlsAndActions() {
        $urls = [
            'save_url' => $this->getAdminLink('setting/setting/notifications_save'),
            'test_email_url' => $this->getAdminLink('setting/setting/test_email_settings'),
            'test_sms_url' => $this->getAdminLink('setting/setting/test_sms_settings'),
            'preview_template_url' => $this->getAdminLink('setting/setting/preview_email_template'),
            'send_test_notification_url' => $this->getAdminLink('setting/setting/send_test_notification'),
            'export_templates_url' => $this->getAdminLink('setting/setting/export_email_templates'),
            'import_templates_url' => $this->getAdminLink('setting/setting/import_email_templates'),
            'reset_template_url' => $this->getAdminLink('setting/setting/reset_email_template')
        ];

        foreach ($urls as $key => $url) {
            $this->setData($key, $url);
        }

        return $this;
    }

    /**
     * Подготвя правила за валидация
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareValidationRules() {
        $validation_rules = [
            'config_email' => [
                'required' => true,
                'type' => 'email'
            ],
            'config_name' => [
                'required' => true,
                'max_length' => 100
            ],
            'config_mail_smtp_hostname' => [
                'required' => false,
                'type' => 'hostname'
            ],
            'config_mail_smtp_port' => [
                'required' => false,
                'type' => 'number',
                'min' => 1,
                'max' => 65535
            ],
            'config_mail_smtp_timeout' => [
                'required' => false,
                'type' => 'number',
                'min' => 1,
                'max' => 300
            ],
            'notification_low_stock_threshold' => [
                'required' => false,
                'type' => 'number',
                'min' => 0
            ]
        ];

        $this->setData('validation_rules', $validation_rules);

        return $this;
    }

    /**
     * Получава статистики за известията
     *
     * @return array
     */
    private function getNotificationStatistics() {
        try {
            $this->loadModelAs('setting/notifications_settings_model', 'notificationsModel');
            
            return [
                'active_email_templates' => $this->notificationsModel->getActiveEmailTemplatesCount(),
                'total_email_templates' => $this->notificationsModel->getTotalEmailTemplatesCount(),
                'emails_sent_today' => $this->notificationsModel->getEmailsSentToday(),
                'emails_sent_month' => $this->notificationsModel->getEmailsSentThisMonth(),
                'failed_emails_today' => $this->notificationsModel->getFailedEmailsToday(),
                'sms_enabled' => $this->notificationsModel->isSMSEnabled()
            ];
            
        } catch (Exception $e) {
            return [
                'active_email_templates' => 4,
                'total_email_templates' => 4,
                'emails_sent_today' => 0,
                'emails_sent_month' => 0,
                'failed_emails_today' => 0,
                'sms_enabled' => false
            ];
        }
    }

    /**
     * Проверява дали има предупреждения за известията
     *
     * @return array
     */
    private function getNotificationWarnings() {
        $warnings = [];
        $settings = $this->getData();
        
        // Проверка за email настройки
        if (empty($settings['config_email'])) {
            $warnings[] = [
                'type' => 'error',
                'message' => 'Не е зададен email адрес за изпращане на известия!'
            ];
        }
        
        // Проверка за SMTP настройки
        if ($settings['config_mail_protocol'] === 'smtp') {
            if (empty($settings['config_mail_smtp_hostname'])) {
                $warnings[] = [
                    'type' => 'warning',
                    'message' => 'SMTP hostname не е зададен'
                ];
            }
            
            if (empty($settings['config_mail_smtp_username'])) {
                $warnings[] = [
                    'type' => 'warning',
                    'message' => 'SMTP username не е зададен'
                ];
            }
        }
        
        // Проверка за активни шаблони
        $email_templates = $this->getData('email_templates') ?: [];
        $active_templates = array_filter($email_templates, function($template) {
            return !empty($template['status']);
        });
        
        if (empty($active_templates)) {
            $warnings[] = [
                'type' => 'warning',
                'message' => 'Няма активни email шаблони'
            ];
        }
        
        return $warnings;
    }

    /**
     * Получава конфигурацията за JavaScript модула
     *
     * @return array
     */
    public function getJavaScriptConfig() {
        return [
            'userToken' => $this->getUserToken(),
            'urls' => [
                'save' => $this->getData('save_url'),
                'testEmail' => $this->getData('test_email_url'),
                'testSMS' => $this->getData('test_sms_url'),
                'previewTemplate' => $this->getData('preview_template_url'),
                'sendTestNotification' => $this->getData('send_test_notification_url'),
                'exportTemplates' => $this->getData('export_templates_url'),
                'importTemplates' => $this->getData('import_templates_url'),
                'resetTemplate' => $this->getData('reset_template_url')
            ],
            'validationRules' => $this->getData('validation_rules'),
            'mailProtocols' => $this->getData('mail_protocols'),
            'smtpEncryption' => $this->getData('smtp_encryption'),
            'smsProviders' => $this->getData('sms_providers'),
            'notificationFrequencies' => $this->getData('notification_frequencies'),
            'statistics' => $this->getNotificationStatistics()
        ];
    }
}
