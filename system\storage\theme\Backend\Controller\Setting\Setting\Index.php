<?php

namespace Theme25\Backend\Controller\Setting\Setting;

/**
 * Sub-контролер за главната страница с настройки
 *
 * Този контролер управлява логиката за показване на главната страница с настройки,
 * включително табовете и общата навигация.
 *
 * @package Theme25\Backend\Controller\Setting\Setting
 * <AUTHOR> Development Team
 * @version 1.0.0
 */
class Index extends \Theme25\ControllerSubMethods {

    /**
     * Подготвя данните за главната страница с настройки
     */
    public function prepareData() {
        $this->prepareTabsData()
             ->prepareCurrentTab()
             ->prepareNavigationData()
             ->preparePermissions();

        // Подготвяне на URL-и за табовете
        $this->setData([
            'user_token' => $this->getUserToken()
        ]);
    }

    /**
     * Подготвя данните за табовете
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareTabsData() {
        $tabs = [
            'basic' => [
                'id' => 'basic',
                'title' => 'Основни',
                'icon' => 'ri-store-line',
                'description' => 'Основна информация за магазина',
                'url' => $this->getAdminLink('setting/setting', 'tab=basic')
            ],
            'security' => [
                'id' => 'security',
                'title' => 'Сигурност',
                'icon' => 'ri-shield-line',
                'description' => 'Настройки за сигурност и достъп',
                'url' => $this->getAdminLink('setting/setting', 'tab=security')
            ],
            'payment' => [
                'id' => 'payment',
                'title' => 'Плащания',
                'icon' => 'ri-bank-card-line',
                'description' => 'Методи за плащане и валути',
                'url' => $this->getAdminLink('setting/setting', 'tab=payment')
            ],
            'delivery' => [
                'id' => 'delivery',
                'title' => 'Доставка',
                'icon' => 'ri-truck-line',
                'description' => 'Методи за доставка и зони',
                'url' => $this->getAdminLink('setting/setting', 'tab=delivery')
            ],
            'notifications' => [
                'id' => 'notifications',
                'title' => 'Известия',
                'icon' => 'ri-notification-line',
                'description' => 'Имейл известия и шаблони',
                'url' => $this->getAdminLink('setting/setting', 'tab=notifications')
            ],
            'integrations' => [
                'id' => 'integrations',
                'title' => 'Интеграции',
                'icon' => 'ri-links-line',
                'description' => 'Външни услуги и API',
                'url' => $this->getAdminLink('setting/setting', 'tab=integrations')
            ],
            'admin_users' => [
                'id' => 'admin_users',
                'title' => 'Админ потребители',
                'icon' => 'ri-admin-line',
                'description' => 'Управление на администратори',
                'url' => $this->getAdminLink('setting/setting', 'tab=admin_users')
            ]
        ];

        $this->setData('tabs', $tabs);

        return $this;
    }

    /**
     * Определя текущия активен таб
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareCurrentTab() {
        $current_tab = $this->requestGet('tab', 'basic');
        
        // Валидация на таба
        $valid_tabs = ['basic', 'security', 'payment', 'delivery', 'notifications', 'integrations', 'admin_users'];
        if (!in_array($current_tab, $valid_tabs)) {
            $current_tab = 'basic';
        }

        $this->setData('current_tab', $current_tab);

        return $this;
    }

    /**
     * Подготвя данните за навигация
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareNavigationData() {
        // Breadcrumbs
        $breadcrumbs = [
            [
                'text' => 'Начало',
                'href' => $this->getAdminLink('common/dashboard')
            ],
            [
                'text' => 'Система',
                'href' => false
            ],
            [
                'text' => 'Настройки',
                'href' => $this->getAdminLink('setting/setting')
            ]
        ];

        $this->setData([
            'breadcrumbs' => $breadcrumbs,
            'back_url' => $this->getAdminLink('common/dashboard')
        ]);

        return $this;
    }

    /**
     * Проверява правата за достъп до различните табове
     *
     * @return $this За верижно извикване на методи
     */
    private function preparePermissions() {
        $permissions = [
            'basic' => $this->hasPermission('modify', 'setting/setting'),
            'security' => $this->hasPermission('modify', 'setting/setting'),
            'payment' => $this->hasPermission('modify', 'setting/setting'),
            'delivery' => $this->hasPermission('modify', 'setting/setting'),
            'notifications' => $this->hasPermission('modify', 'setting/setting'),
            'integrations' => $this->hasPermission('modify', 'setting/setting'),
            'admin_users' => $this->hasPermission('modify', 'user/user')
        ];

        $this->setData('tab_permissions', $permissions);

        return $this;
    }

    /**
     * Подготвя конфигурацията за JavaScript
     *
     * @return array
     */
    public function getJavaScriptConfig() {
        $current_tab = $this->getData('current_tab') ?: 'basic';
        
        return [
            'userToken' => $this->getUserToken(),
            'currentTab' => $current_tab,
            'baseUrl' => $this->getAdminLink('setting/setting'),
            'ajaxUrls' => [
                'basic_save' => $this->getAdminLink('setting/setting/basic_save'),
                'upload_logo' => $this->getAdminLink('setting/setting/upload_logo'),
                'test_email' => $this->getAdminLink('setting/setting/test_email'),
                'security_update' => $this->getAdminLink('setting/setting/security_update')
            ]
        ];
    }

    /**
     * Получава статистики за настройките
     *
     * @return array
     */
    private function getSettingsStatistics() {
        try {
            $this->loadModelAs('setting/setting', 'settingModel');
            
            // Броене на конфигурирани настройки
            $total_settings = $this->settingModel->getTotalSettings();
            
            // Проверка на критични настройки
            $critical_settings = [
                'config_name' => $this->config->get('config_name'),
                'config_email' => $this->config->get('config_email'),
                'config_telephone' => $this->config->get('config_telephone')
            ];
            
            $configured_critical = count(array_filter($critical_settings));
            
            return [
                'total_settings' => $total_settings,
                'critical_configured' => $configured_critical,
                'critical_total' => count($critical_settings),
                'completion_percentage' => round(($configured_critical / count($critical_settings)) * 100)
            ];
            
        } catch (Exception $e) {
            return [
                'total_settings' => 0,
                'critical_configured' => 0,
                'critical_total' => 3,
                'completion_percentage' => 0
            ];
        }
    }

    /**
     * Проверява дали има неконфигурирани критични настройки
     *
     * @return array
     */
    private function checkCriticalSettings() {
        $warnings = [];
        
        // Проверка на основни настройки
        if (empty($this->config->get('config_name'))) {
            $warnings[] = [
                'type' => 'warning',
                'message' => 'Не е зададено име на магазина',
                'tab' => 'basic'
            ];
        }
        
        if (empty($this->config->get('config_email'))) {
            $warnings[] = [
                'type' => 'warning',
                'message' => 'Не е зададен имейл адрес на магазина',
                'tab' => 'basic'
            ];
        }
        
        // Проверка на настройки за сигурност
        if (!$this->config->get('config_security_ip_restriction_status')) {
            $warnings[] = [
                'type' => 'info',
                'message' => 'IP ограниченията не са активирани',
                'tab' => 'security'
            ];
        }
        
        return $warnings;
    }
}
