/**
 * JavaScript модул за основни настройки
 */
const BasicSettings = {
    
    config: {
        userToken: '',
        saveUrl: '',
        uploadLogoUrl: '',
        testEmailUrl: '',
        testSocialUrl: '',
        validateContactUrl: ''
    },

    elements: {
        form: null,
        saveButton: null,
        logoInput: null,
        logoPreview: null,
        logoUploadButton: null,
        logoRemoveButton: null,
        socialInputs: [],
        contactInputs: []
    },

    init() {
        this.loadConfig();
        this.bindElements();
        this.bindEvents();
        this.initValidation();
        this.loadLogoInfo();
    },

    loadConfig() {
        if (window.settingsConfig) {
            this.config = { ...this.config, ...window.settingsConfig };
        }
    },

    bindElements() {
        this.elements.form = document.getElementById('basic-settings-form');
        this.elements.saveButton = document.getElementById('save-basic-settings');
        this.elements.logoInput = document.getElementById('logo-input');
        this.elements.logoPreview = document.getElementById('logo-preview');
        this.elements.logoUploadButton = document.getElementById('upload-logo-button');
        this.elements.logoRemoveButton = document.getElementById('remove-logo-button');
        
        // Социални мрежи
        this.elements.socialInputs = [
            document.getElementById('facebook-url'),
            document.getElementById('instagram-url'),
            document.getElementById('youtube-url'),
            document.getElementById('twitter-url')
        ].filter(el => el !== null);

        // Контактна информация
        this.elements.contactInputs = [
            document.getElementById('store-email'),
            document.getElementById('store-phone')
        ].filter(el => el !== null);
    },

    bindEvents() {
        // Запазване на настройки
        if (this.elements.saveButton) {
            this.elements.saveButton.addEventListener('click', (e) => {
                e.preventDefault();
                this.saveSettings();
            });
        }

        // Upload на лого
        if (this.elements.logoInput) {
            this.elements.logoInput.addEventListener('change', (e) => {
                this.handleLogoUpload(e);
            });
        }

        if (this.elements.logoUploadButton) {
            this.elements.logoUploadButton.addEventListener('click', () => {
                this.elements.logoInput?.click();
            });
        }

        // Премахване на лого
        if (this.elements.logoRemoveButton) {
            this.elements.logoRemoveButton.addEventListener('click', () => {
                this.removeLogo();
            });
        }

        // Валидация в реално време за контактна информация
        this.elements.contactInputs.forEach(input => {
            input.addEventListener('blur', () => {
                this.validateContactField(input);
            });
        });

        // Тестване на социални URL-и
        this.elements.socialInputs.forEach(input => {
            const testButton = input.parentElement.querySelector('.test-url-button');
            if (testButton) {
                testButton.addEventListener('click', () => {
                    this.testSocialUrl(input.value, testButton);
                });
            }
        });
    },

    initValidation() {
        // Добавяне на визуални индикатори за валидация
        this.elements.contactInputs.forEach(input => {
            this.addValidationIndicator(input);
        });

        this.elements.socialInputs.forEach(input => {
            this.addValidationIndicator(input);
        });
    },

    addValidationIndicator(input) {
        const indicator = document.createElement('div');
        indicator.className = 'validation-indicator hidden';
        indicator.innerHTML = '<i class="ri-check-line text-green-500"></i>';
        input.parentElement.appendChild(indicator);
    },

    async saveSettings() {
        if (!this.elements.form) return;

        const saveButton = this.elements.saveButton;
        const originalText = saveButton.textContent;
        
        try {
            // Показване на loading състояние
            saveButton.disabled = true;
            saveButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Запазване...';

            const formData = new FormData(this.elements.form);
            formData.append('user_token', this.config.userToken);

            const response = await fetch(this.config.saveUrl, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification('success', result.message || 'Настройките са запазени успешно');
                
                // Актуализиране на визуалните индикатори
                this.updateValidationIndicators();
            } else {
                if (result.validation_errors) {
                    this.showValidationErrors(result.validation_errors);
                } else {
                    this.showNotification('error', result.error || 'Възникна грешка при запазването');
                }
            }
        } catch (error) {
            this.showNotification('error', 'Грешка при комуникация със сървъра');
            console.error('Save error:', error);
        } finally {
            // Възстановяване на бутона
            saveButton.disabled = false;
            saveButton.textContent = originalText;
        }
    },

    async handleLogoUpload(event) {
        const file = event.target.files[0];
        if (!file) return;

        // Валидация на файла
        const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/svg+xml'];
        if (!allowedTypes.includes(file.type)) {
            this.showNotification('error', 'Разрешени са само JPEG, PNG, GIF и SVG файлове');
            return;
        }

        if (file.size > 2 * 1024 * 1024) {
            this.showNotification('error', 'Файлът не може да бъде по-голям от 2MB');
            return;
        }

        try {
            const formData = new FormData();
            formData.append('logo', file);
            formData.append('user_token', this.config.userToken);

            // Показване на preview
            this.showLogoPreview(file);

            const response = await fetch(this.config.uploadLogoUrl, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification('success', result.message);
                this.updateLogoDisplay(result.logo_url);
            } else {
                this.showNotification('error', result.error);
                this.clearLogoPreview();
            }
        } catch (error) {
            this.showNotification('error', 'Грешка при качване на лого');
            this.clearLogoPreview();
            console.error('Upload error:', error);
        }
    },

    showLogoPreview(file) {
        if (!this.elements.logoPreview) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            this.elements.logoPreview.src = e.target.result;
            this.elements.logoPreview.classList.remove('hidden');
        };
        reader.readAsDataURL(file);
    },

    clearLogoPreview() {
        if (this.elements.logoPreview) {
            this.elements.logoPreview.src = '';
            this.elements.logoPreview.classList.add('hidden');
        }
    },

    updateLogoDisplay(logoUrl) {
        if (this.elements.logoPreview && logoUrl) {
            this.elements.logoPreview.src = logoUrl;
            this.elements.logoPreview.classList.remove('hidden');
        }

        // Показване/скриване на бутоните
        if (this.elements.logoRemoveButton) {
            this.elements.logoRemoveButton.classList.toggle('hidden', !logoUrl);
        }
    },

    async removeLogo() {
        if (!confirm('Сигурни ли сте, че искате да премахнете логото?')) {
            return;
        }

        try {
            const formData = new FormData();
            formData.append('user_token', this.config.userToken);

            const response = await fetch(this.config.removeLogoUrl, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification('success', result.message);
                this.clearLogoPreview();
                this.elements.logoRemoveButton?.classList.add('hidden');
            } else {
                this.showNotification('error', result.error);
            }
        } catch (error) {
            this.showNotification('error', 'Грешка при премахване на лого');
            console.error('Remove logo error:', error);
        }
    },

    async loadLogoInfo() {
        try {
            const response = await fetch(this.config.getLogoInfoUrl + '&user_token=' + this.config.userToken);
            const result = await response.json();

            if (result.success && result.has_logo) {
                this.updateLogoDisplay(result.logo_url);
            }
        } catch (error) {
            console.error('Load logo info error:', error);
        }
    },

    async validateContactField(input) {
        const value = input.value.trim();
        if (!value) return;

        const type = input.id.includes('email') ? 'email' : 'phone';

        try {
            const formData = new FormData();
            formData.append('type', type);
            formData.append('value', value);
            formData.append('user_token', this.config.userToken);

            const response = await fetch(this.config.validateContactUrl, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            const result = await response.json();
            this.updateFieldValidation(input, result.valid, result.message);
        } catch (error) {
            console.error('Validation error:', error);
        }
    },

    async testSocialUrl(url, button) {
        if (!url.trim()) {
            this.showNotification('warning', 'Моля въведете URL адрес');
            return;
        }

        const originalText = button.textContent;
        
        try {
            button.disabled = true;
            button.innerHTML = '<i class="ri-loader-4-line animate-spin"></i>';

            const formData = new FormData();
            formData.append('url', url);
            formData.append('user_token', this.config.userToken);

            const response = await fetch(this.config.testSocialUrl, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            const result = await response.json();
            
            if (result.success) {
                this.showNotification('success', result.message);
            } else {
                this.showNotification('error', result.message);
            }
        } catch (error) {
            this.showNotification('error', 'Грешка при тестване на URL');
            console.error('Test URL error:', error);
        } finally {
            button.disabled = false;
            button.textContent = originalText;
        }
    },

    updateFieldValidation(input, isValid, message) {
        const indicator = input.parentElement.querySelector('.validation-indicator');
        if (!indicator) return;

        indicator.classList.remove('hidden');
        
        if (isValid) {
            indicator.innerHTML = '<i class="ri-check-line text-green-500"></i>';
            input.classList.remove('border-red-300');
            input.classList.add('border-green-300');
        } else {
            indicator.innerHTML = '<i class="ri-close-line text-red-500"></i>';
            input.classList.remove('border-green-300');
            input.classList.add('border-red-300');
            
            if (message) {
                this.showNotification('error', message);
            }
        }
    },

    updateValidationIndicators() {
        // Актуализиране на всички индикатори след запазване
        this.elements.contactInputs.forEach(input => {
            this.validateContactField(input);
        });
    },

    showValidationErrors(errors) {
        Object.keys(errors).forEach(field => {
            const input = document.getElementById(field.replace('_', '-'));
            if (input) {
                this.updateFieldValidation(input, false, errors[field]);
            }
        });
    },

    showNotification(type, message) {
        // Интеграция с глобалната система за известия
        if (window.SettingsManager && window.SettingsManager.showNotification) {
            window.SettingsManager.showNotification(message, type);
        } else {
            // Fallback
            alert(message);
        }
    }
};

// Автоматично инициализиране при зареждане на страницата
document.addEventListener('DOMContentLoaded', () => {
    BasicSettings.init();
});
