<?php

namespace Theme25\Backend\Controller\Setting\Setting;

/**
 * Sub-контролер за AJAX запазване на настройки за плащания
 */
class PaymentSave extends \Theme25\ControllerSubMethods {

    /**
     * Изпълнява AJAX запазване на настройки за плащания
     */
    public function execute() {
        $json = [];

        // Проверка за права на достъп
        if (!$this->hasPermission('modify', 'setting/setting')) {
            $json['error'] = 'Нямате права за редактиране на настройки за плащания';
            $this->outputJson($json);
            return;
        }

        // Проверка за POST заявка
        if (!$this->isPostRequest()) {
            $json['error'] = 'Невалидна заявка';
            $this->outputJson($json);
            return;
        }

        try {
            // Получаване на данните от POST
            $postData = $this->requestPost();
            
            // Валидация на данните
            $validation_errors = $this->validatePaymentSettings($postData);
            if (!empty($validation_errors)) {
                $json['error'] = 'Грешки при валидацията';
                $json['validation_errors'] = $validation_errors;
                $this->outputJson($json);
                return;
            }

            $this->loadModelAs('setting/payment_settings_model', 'paymentSettings');
            
            // Запазване на настройките
            $result = $this->paymentSettings->savePaymentSettings($postData);
            
            if ($result === true) {
                $json['success'] = 'Настройките за плащания са запазени успешно';
                $json['timestamp'] = date('d.m.Y H:i:s');
                
                // Добавяне на актуализирани данни
                $json['updated_data'] = $this->getUpdatedPaymentData($postData);
                
                // Предупреждения ако има
                $warnings = $this->checkPaymentWarnings($postData);
                if (!empty($warnings)) {
                    $json['warnings'] = $warnings;
                }
            } else {
                // Има грешки при валидацията от модела
                $json['error'] = 'Грешки при валидацията';
                $json['validation_errors'] = $result;
            }

        } catch (Exception $e) {
            $json['error'] = 'Грешка при запазване: ' . $e->getMessage();
        }

        $this->outputJson($json);
    }

    /**
     * Валидира настройките за плащания
     *
     * @param array $data Данни за валидация
     * @return array Масив с грешки
     */
    private function validatePaymentSettings($data) {
        $errors = [];

        // Валидация на минимална сума за поръчка
        if (isset($data['payment_minimum_order'])) {
            $min_order = (float)$data['payment_minimum_order'];
            if ($min_order < 0) {
                $errors['payment_minimum_order'] = 'Минималната сума за поръчка не може да бъде отрицателна';
            }
        }

        // Валидация на максимална сума за поръчка
        if (isset($data['payment_maximum_order'])) {
            $max_order = (float)$data['payment_maximum_order'];
            if ($max_order < 0) {
                $errors['payment_maximum_order'] = 'Максималната сума за поръчка не може да бъде отрицателна';
            }
            
            // Проверка дали максималната сума е по-голяма от минималната
            if (isset($data['payment_minimum_order']) && $max_order > 0) {
                $min_order = (float)$data['payment_minimum_order'];
                if ($max_order <= $min_order) {
                    $errors['payment_maximum_order'] = 'Максималната сума трябва да бъде по-голяма от минималната';
                }
            }
        }

        // Валидация на условия за плащане
        if (!empty($data['payment_terms']) && mb_strlen($data['payment_terms']) > 1000) {
            $errors['payment_terms'] = 'Условията за плащане не могат да бъдат повече от 1000 символа';
        }

        // Валидация на политика за поверителност
        if (!empty($data['payment_privacy_policy']) && mb_strlen($data['payment_privacy_policy']) > 1000) {
            $errors['payment_privacy_policy'] = 'Политиката за поверителност не може да бъде повече от 1000 символа';
        }

        // Валидация на payment методи
        if (isset($data['payment_methods']) && is_array($data['payment_methods'])) {
            foreach ($data['payment_methods'] as $method_code => $method_data) {
                // Валидация на комисионни
                if (isset($method_data['commission_type']) && isset($method_data['commission_value'])) {
                    $commission_value = (float)$method_data['commission_value'];
                    
                    if ($method_data['commission_type'] === 'percentage' && ($commission_value < 0 || $commission_value > 100)) {
                        $errors['payment_methods'][$method_code]['commission_value'] = 'Процентът трябва да бъде между 0 и 100';
                    } elseif ($method_data['commission_type'] === 'fixed' && $commission_value < 0) {
                        $errors['payment_methods'][$method_code]['commission_value'] = 'Фиксираната комисионна не може да бъде отрицателна';
                    }
                }
                
                // Валидация на sort_order
                if (isset($method_data['sort_order'])) {
                    $sort_order = (int)$method_data['sort_order'];
                    if ($sort_order < 0) {
                        $errors['payment_methods'][$method_code]['sort_order'] = 'Подредбата не може да бъде отрицателна';
                    }
                }
            }
        }

        return $errors;
    }

    /**
     * Проверява за предупреждения относно плащанията
     *
     * @param array $data Данни за проверка
     * @return array Масив с предупреждения
     */
    private function checkPaymentWarnings($data) {
        $warnings = [];

        // Проверка за активни payment методи
        $active_methods = 0;
        if (isset($data['payment_methods']) && is_array($data['payment_methods'])) {
            foreach ($data['payment_methods'] as $method_data) {
                if (!empty($method_data['status'])) {
                    $active_methods++;
                }
            }
        }

        if ($active_methods === 0) {
            $warnings[] = [
                'type' => 'error',
                'message' => 'ВНИМАНИЕ: Няма активни методи за плащане! Клиентите няма да могат да завършат поръчки.'
            ];
        } elseif ($active_methods === 1) {
            $warnings[] = [
                'type' => 'warning',
                'message' => 'Имате само един активен метод за плащане. Препоръчваме да активирате поне два метода.'
            ];
        }

        // Проверка за минимална/максимална сума
        if (isset($data['payment_minimum_order']) && $data['payment_minimum_order'] > 0) {
            $warnings[] = [
                'type' => 'info',
                'message' => 'Зададена е минимална сума за поръчка: ' . $data['payment_minimum_order'] . ' лв.'
            ];
        }

        if (isset($data['payment_maximum_order']) && $data['payment_maximum_order'] > 0) {
            $warnings[] = [
                'type' => 'info',
                'message' => 'Зададена е максимална сума за поръчка: ' . $data['payment_maximum_order'] . ' лв.'
            ];
        }

        // Проверка за автоматично актуализиране на валути
        if (empty($data['payment_auto_currency_update'])) {
            $warnings[] = [
                'type' => 'warning',
                'message' => 'Автоматичното актуализиране на валутите е изключено. Не забравяйте да актуализирате курсовете ръчно.'
            ];
        }

        return $warnings;
    }

    /**
     * Получава актуализираните данни за настройките за плащания
     *
     * @param array $postData POST данни
     * @return array
     */
    private function getUpdatedPaymentData($postData) {
        $updated_data = [
            'payment_terms' => $postData['payment_terms'] ?? '',
            'payment_privacy_policy' => $postData['payment_privacy_policy'] ?? '',
            'payment_minimum_order' => (float)($postData['payment_minimum_order'] ?? 0),
            'payment_maximum_order' => (float)($postData['payment_maximum_order'] ?? 0),
            'payment_auto_currency_update' => isset($postData['payment_auto_currency_update']) ? 1 : 0,
            'payment_currency_update_frequency' => $postData['payment_currency_update_frequency'] ?? 'daily',
            'payment_failed_order_status' => (int)($postData['payment_failed_order_status'] ?? 10),
            'payment_pending_order_status' => (int)($postData['payment_pending_order_status'] ?? 1),
            'payment_processing_order_status' => (int)($postData['payment_processing_order_status'] ?? 2),
            'payment_complete_order_status' => (int)($postData['payment_complete_order_status'] ?? 5),
            'payment_refund_order_status' => (int)($postData['payment_refund_order_status'] ?? 11),
            'payment_cancelled_order_status' => (int)($postData['payment_cancelled_order_status'] ?? 7)
        ];

        // Обработка на payment методи
        if (isset($postData['payment_methods']) && is_array($postData['payment_methods'])) {
            $updated_data['payment_methods'] = [];
            foreach ($postData['payment_methods'] as $method_code => $method_data) {
                $updated_data['payment_methods'][$method_code] = [
                    'status' => isset($method_data['status']) ? 1 : 0,
                    'sort_order' => (int)($method_data['sort_order'] ?? 0),
                    'commission_type' => $method_data['commission_type'] ?? 'fixed',
                    'commission_value' => (float)($method_data['commission_value'] ?? 0),
                    'minimum_amount' => (float)($method_data['minimum_amount'] ?? 0),
                    'maximum_amount' => (float)($method_data['maximum_amount'] ?? 0)
                ];
            }
        }

        return $updated_data;
    }

    /**
     * Изпраща JSON отговор
     *
     * @param array $data
     */
    private function outputJson($data) {
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($data));
    }

    /**
     * Логира действието за запазване на настройки за плащания
     *
     * @param array $data Запазените данни
     */
    private function logPaymentSettingsSave($data) {
        try {
            $log_data = [
                'action' => 'payment_settings_save',
                'user_id' => $this->user->getId(),
                'user_name' => $this->user->getUserName(),
                'timestamp' => date('Y-m-d H:i:s'),
                'ip_address' => $this->request->server['REMOTE_ADDR'] ?? 'unknown',
                'changed_fields' => array_keys($data)
            ];
            
            // Логиране в системния лог
            $this->log->write('Payment Settings: Updated by user ' . $this->user->getUserName() . ' (' . $this->user->getId() . ')');
            
        } catch (Exception $e) {
            // Ако логирането се провали, не спираме операцията
            error_log('Failed to log payment settings save: ' . $e->getMessage());
        }
    }
}
