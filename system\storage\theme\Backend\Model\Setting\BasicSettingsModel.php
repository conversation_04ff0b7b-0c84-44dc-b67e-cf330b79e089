<?php

namespace Theme25\Backend\Model\Setting;

use Theme25\Backend\Helper\SettingValidator;

/**
 * Модел за основни настройки на магазина
 */
class BasicSettingsModel extends SettingModel {

    private $validator;

    public function __construct($registry) {
        parent::__construct($registry);
        $this->settingPrefix = 'config';
        $this->validator = new SettingValidator($registry);
    }

    /**
     * Получава всички основни настройки
     * 
     * @return array
     */
    public function getBasicSettings() {
        $settings = [
            // Информация за магазина
            'store_name' => $this->getSetting('name', 'Rakla'),
            'store_email' => $this->getSetting('email', '<EMAIL>'),
            'store_phone' => $this->getSetting('telephone', '+359 88 123 4567'),
            'store_address' => $this->getSetting('address', 'ул. Иван Вазов 12, 1000 София, България'),
            'store_logo' => $this->getSetting('logo', ''),
            
            // Социални мрежи
            'facebook_url' => $this->getSetting('facebook_url', 'https://facebook.com/rakla'),
            'instagram_url' => $this->getSetting('instagram_url', 'https://instagram.com/rakla'),
            'youtube_url' => $this->getSetting('youtube_url', ''),
            'twitter_url' => $this->getSetting('twitter_url', ''),
            
            // Общи настройки
            'maintenance_mode' => $this->getSetting('maintenance', 0),
            'support_chat' => $this->getSetting('support_chat', 1),
            'reviews_enabled' => $this->getSetting('reviews_enabled', 1),
            'stock_display' => $this->getSetting('stock_display', 1),
            
            // Валута и език
            'currency' => $this->getSetting('currency', 'BGN'),
            'language' => $this->getSetting('language', 'bg-bg'),
            
            // SEO настройки
            'meta_title' => $this->getSetting('meta_title', 'Rakla - Онлайн магазин'),
            'meta_description' => $this->getSetting('meta_description', ''),
            'meta_keywords' => $this->getSetting('meta_keywords', ''),
        ];

        return $settings;
    }

    /**
     * Запазва основните настройки
     * 
     * @param array $data Данни за запазване
     * @return bool|array True при успех, масив с грешки при неуспех
     */
    public function saveBasicSettings($data) {
        // Валидация на данните
        $errors = $this->validateBasicSettings($data);
        if (!empty($errors)) {
            return $errors;
        }

        // Подготовка на данните за запазване
        $settingsToSave = [
            'name' => $data['store_name'] ?? '',
            'email' => $data['store_email'] ?? '',
            'telephone' => $data['store_phone'] ?? '',
            'address' => $data['store_address'] ?? '',
            'logo' => $data['store_logo'] ?? '',
            'facebook_url' => $data['facebook_url'] ?? '',
            'instagram_url' => $data['instagram_url'] ?? '',
            'youtube_url' => $data['youtube_url'] ?? '',
            'twitter_url' => $data['twitter_url'] ?? '',
            'maintenance' => isset($data['maintenance_mode']) ? (int)$data['maintenance_mode'] : 0,
            'support_chat' => isset($data['support_chat']) ? (int)$data['support_chat'] : 0,
            'reviews_enabled' => isset($data['reviews_enabled']) ? (int)$data['reviews_enabled'] : 0,
            'stock_display' => isset($data['stock_display']) ? (int)$data['stock_display'] : 0,
            'currency' => $data['currency'] ?? 'BGN',
            'language' => $data['language'] ?? 'bg-bg',
            'meta_title' => $data['meta_title'] ?? '',
            'meta_description' => $data['meta_description'] ?? '',
            'meta_keywords' => $data['meta_keywords'] ?? '',
        ];

        // Запазване на настройките
        return $this->setMultipleSettings($settingsToSave);
    }

    /**
     * Валидира основните настройки
     * 
     * @param array $data Данни за валидация
     * @return array Масив с грешки
     */
    protected function validateBasicSettings($data) {
        $errors = [];

        // Валидация на име на магазина
        if (empty($data['store_name'])) {
            $errors['store_name'] = 'Името на магазина е задължително';
        } elseif (mb_strlen($data['store_name']) > 64) {
            $errors['store_name'] = 'Името на магазина не може да бъде повече от 64 символа';
        }

        // Валидация на имейл
        $emailValidation = $this->validator->validateEmail($data['store_email'] ?? '');
        if ($emailValidation !== true) {
            $errors['store_email'] = $emailValidation;
        }

        // Валидация на телефон
        $phoneValidation = $this->validator->validatePhone($data['store_phone'] ?? '', true);
        if ($phoneValidation !== true) {
            $errors['store_phone'] = $phoneValidation;
        }

        // Валидация на адрес
        if (empty($data['store_address'])) {
            $errors['store_address'] = 'Адресът е задължителен';
        } elseif (mb_strlen($data['store_address']) > 255) {
            $errors['store_address'] = 'Адресът не може да бъде повече от 255 символа';
        }

        // Валидация на социални мрежи URL-и
        $socialUrls = ['facebook_url', 'instagram_url', 'youtube_url', 'twitter_url'];
        foreach ($socialUrls as $urlField) {
            if (!empty($data[$urlField])) {
                $urlValidation = $this->validator->validateUrl($data[$urlField]);
                if ($urlValidation !== true) {
                    $errors[$urlField] = $urlValidation;
                }
            }
        }

        // Валидация на мета заглавие
        if (empty($data['meta_title'])) {
            $errors['meta_title'] = 'Мета заглавието е задължително';
        } elseif (mb_strlen($data['meta_title']) > 255) {
            $errors['meta_title'] = 'Мета заглавието не може да бъде повече от 255 символа';
        }

        // Валидация на мета описание
        if (!empty($data['meta_description']) && mb_strlen($data['meta_description']) > 500) {
            $errors['meta_description'] = 'Мета описанието не може да бъде повече от 500 символа';
        }

        return $errors;
    }

    /**
     * Качва лого на магазина
     * 
     * @param array $file Файл от $_FILES
     * @return array Резултат с path или error
     */
    public function uploadLogo($file) {
        if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
            return ['error' => 'Няма качен файл'];
        }

        // Проверка на типа файл
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/svg+xml'];
        if (!in_array($file['type'], $allowedTypes)) {
            return ['error' => 'Разрешени са само JPEG, PNG, GIF и SVG файлове'];
        }

        // Проверка на размера (максимум 2MB)
        if ($file['size'] > 2 * 1024 * 1024) {
            return ['error' => 'Файлът не може да бъде по-голям от 2MB'];
        }

        // Генериране на уникално име
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = 'logo_' . time() . '.' . $extension;
        $uploadPath = DIR_IMAGE . 'catalog/' . $filename;

        // Качване на файла
        if (move_uploaded_file($file['tmp_name'], $uploadPath)) {
            // Запазване на пътя в настройките
            $logoPath = 'catalog/' . $filename;
            $this->setSetting('logo', $logoPath);
            
            return ['success' => true, 'path' => $logoPath];
        } else {
            return ['error' => 'Грешка при качване на файла'];
        }
    }

    /**
     * Получава налични валути
     * 
     * @return array
     */
    public function getAvailableCurrencies() {
        $this->loadModelAs('localisation/currency', 'currencyModel');
        $currencies = $this->currencyModel->getCurrencies();
        
        $result = [];
        foreach ($currencies as $currency) {
            $result[] = [
                'code' => $currency['code'],
                'title' => $currency['title'],
                'symbol_left' => $currency['symbol_left'],
                'symbol_right' => $currency['symbol_right']
            ];
        }
        
        return $result;
    }

    /**
     * Получава налични езици
     * 
     * @return array
     */
    public function getAvailableLanguages() {
        $this->loadModelAs('localisation/language', 'languageModel');
        $languages = $this->languageModel->getLanguages();
        
        $result = [];
        foreach ($languages as $language) {
            $result[] = [
                'code' => $language['code'],
                'name' => $language['name'],
                'image' => $language['image']
            ];
        }
        
        return $result;
    }
}
