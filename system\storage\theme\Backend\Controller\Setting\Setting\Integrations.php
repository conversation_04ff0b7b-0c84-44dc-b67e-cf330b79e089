<?php

namespace Theme25\Backend\Controller\Setting\Setting;

/**
 * Sub-контролер за настройки за интеграции
 *
 * Този контролер управлява логиката за показване и обработка на настройките за интеграции,
 * включително API интеграции, analytics tools, social media, webhooks и external services.
 *
 * @package Theme25\Backend\Controller\Setting\Setting
 * <AUTHOR> Development Team
 * @version 1.0.0
 */
class Integrations extends \Theme25\ControllerSubMethods {

    /**
     * Подготвя данните за настройките за интеграции
     */
    public function prepareData() {
        $this->prepareAnalyticsIntegrationsData()
             ->prepareSocialMediaIntegrationsData()
             ->prepareAPIIntegrationsData()
             ->prepareWebhooksData()
             ->prepareMarketplacesData()
             ->prepareUrlsAndActions()
             ->prepareValidationRules();

        // Подготвяне на JavaScript конфигурация
        $this->setData([
            'user_token' => $this->getUserToken()
        ]);
    }

    /**
     * Подготвя данните за analytics интеграции
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareAnalyticsIntegrationsData() {
        try {
            $this->loadModelAs('setting/integrations_settings_model', 'integrationsSettings');
            $analytics_integrations = $this->integrationsSettings->getAnalyticsIntegrations();
            
            $this->setData('analytics_integrations', $analytics_integrations);
            
        } catch (Exception $e) {
            $this->setError('Грешка при зареждане на analytics интеграции: ' . $e->getMessage());
            
            // Задаване на стойности по подразбиране
            $this->setData('analytics_integrations', [
                'google_analytics' => [
                    'name' => 'Google Analytics',
                    'description' => 'Проследяване на посетители и конверсии',
                    'status' => 0,
                    'tracking_id' => '',
                    'enhanced_ecommerce' => 0,
                    'anonymize_ip' => 1,
                    'icon' => 'ri-google-line',
                    'color' => 'text-orange-600'
                ],
                'google_tag_manager' => [
                    'name' => 'Google Tag Manager',
                    'description' => 'Управление на tracking кодове',
                    'status' => 0,
                    'container_id' => '',
                    'icon' => 'ri-code-box-line',
                    'color' => 'text-blue-600'
                ],
                'facebook_pixel' => [
                    'name' => 'Facebook Pixel',
                    'description' => 'Facebook реклами и проследяване',
                    'status' => 0,
                    'pixel_id' => '',
                    'advanced_matching' => 0,
                    'icon' => 'ri-facebook-line',
                    'color' => 'text-blue-700'
                ],
                'hotjar' => [
                    'name' => 'Hotjar',
                    'description' => 'Heatmaps и session recordings',
                    'status' => 0,
                    'site_id' => '',
                    'icon' => 'ri-fire-line',
                    'color' => 'text-red-600'
                ]
            ]);
        }

        return $this;
    }

    /**
     * Подготвя данните за social media интеграции
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareSocialMediaIntegrationsData() {
        try {
            $this->loadModelAs('setting/integrations_settings_model', 'integrationsSettings');
            $social_integrations = $this->integrationsSettings->getSocialMediaIntegrations();
            
            $this->setData('social_integrations', $social_integrations);
            
        } catch (Exception $e) {
            // Задаване на стойности по подразбиране
            $this->setData('social_integrations', [
                'facebook_login' => [
                    'name' => 'Facebook Login',
                    'description' => 'Вход чрез Facebook акаунт',
                    'status' => 0,
                    'app_id' => '',
                    'app_secret' => '',
                    'icon' => 'ri-facebook-line',
                    'color' => 'text-blue-700'
                ],
                'google_login' => [
                    'name' => 'Google Login',
                    'description' => 'Вход чрез Google акаунт',
                    'status' => 0,
                    'client_id' => '',
                    'client_secret' => '',
                    'icon' => 'ri-google-line',
                    'color' => 'text-red-600'
                ],
                'instagram_feed' => [
                    'name' => 'Instagram Feed',
                    'description' => 'Показване на Instagram снимки',
                    'status' => 0,
                    'access_token' => '',
                    'user_id' => '',
                    'icon' => 'ri-instagram-line',
                    'color' => 'text-pink-600'
                ],
                'youtube_channel' => [
                    'name' => 'YouTube Channel',
                    'description' => 'Интеграция с YouTube канал',
                    'status' => 0,
                    'channel_id' => '',
                    'api_key' => '',
                    'icon' => 'ri-youtube-line',
                    'color' => 'text-red-600'
                ]
            ]);
        }

        return $this;
    }

    /**
     * Подготвя данните за API интеграции
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareAPIIntegrationsData() {
        try {
            $this->loadModelAs('setting/integrations_settings_model', 'integrationsSettings');
            $api_integrations = $this->integrationsSettings->getAPIIntegrations();
            
            $this->setData('api_integrations', $api_integrations);
            
        } catch (Exception $e) {
            // Задаване на стойности по подразбиране
            $this->setData('api_integrations', [
                'opencart_api' => [
                    'name' => 'OpenCart API',
                    'description' => 'Вътрешен API за интеграции',
                    'status' => 1,
                    'api_key' => '',
                    'allowed_ips' => '',
                    'rate_limit' => 1000,
                    'icon' => 'ri-code-line',
                    'color' => 'text-green-600'
                ],
                'rest_api' => [
                    'name' => 'REST API',
                    'description' => 'RESTful API за външни приложения',
                    'status' => 0,
                    'api_key' => '',
                    'secret_key' => '',
                    'version' => 'v1',
                    'icon' => 'ri-server-line',
                    'color' => 'text-blue-600'
                ],
                'webhook_api' => [
                    'name' => 'Webhook API',
                    'description' => 'Webhooks за real-time известия',
                    'status' => 0,
                    'secret_key' => '',
                    'retry_attempts' => 3,
                    'timeout' => 30,
                    'icon' => 'ri-webhook-line',
                    'color' => 'text-purple-600'
                ]
            ]);
        }

        return $this;
    }

    /**
     * Подготвя данните за webhooks
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareWebhooksData() {
        try {
            $this->loadModelAs('setting/integrations_settings_model', 'integrationsSettings');
            $webhooks = $this->integrationsSettings->getWebhooks();
            
            $this->setData('webhooks', $webhooks);
            
        } catch (Exception $e) {
            // Задаване на стойности по подразбиране
            $this->setData('webhooks', [
                [
                    'id' => 1,
                    'name' => 'Нова поръчка',
                    'event' => 'order.created',
                    'url' => '',
                    'method' => 'POST',
                    'status' => 0,
                    'headers' => '{"Content-Type": "application/json"}'
                ],
                [
                    'id' => 2,
                    'name' => 'Промяна в статус',
                    'event' => 'order.status_changed',
                    'url' => '',
                    'method' => 'POST',
                    'status' => 0,
                    'headers' => '{"Content-Type": "application/json"}'
                ],
                [
                    'id' => 3,
                    'name' => 'Нов клиент',
                    'event' => 'customer.created',
                    'url' => '',
                    'method' => 'POST',
                    'status' => 0,
                    'headers' => '{"Content-Type": "application/json"}'
                ]
            ]);
        }

        return $this;
    }

    /**
     * Подготвя данните за marketplace интеграции
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareMarketplacesData() {
        try {
            $this->loadModelAs('setting/integrations_settings_model', 'integrationsSettings');
            $marketplaces = $this->integrationsSettings->getMarketplaces();
            
            $this->setData('marketplaces', $marketplaces);
            
        } catch (Exception $e) {
            // Задаване на стойности по подразбиране
            $this->setData('marketplaces', [
                'amazon' => [
                    'name' => 'Amazon',
                    'description' => 'Продажба в Amazon marketplace',
                    'status' => 0,
                    'seller_id' => '',
                    'access_key' => '',
                    'secret_key' => '',
                    'marketplace_id' => '',
                    'icon' => 'ri-amazon-line',
                    'color' => 'text-orange-600'
                ],
                'ebay' => [
                    'name' => 'eBay',
                    'description' => 'Продажба в eBay marketplace',
                    'status' => 0,
                    'app_id' => '',
                    'dev_id' => '',
                    'cert_id' => '',
                    'token' => '',
                    'icon' => 'ri-shopping-bag-line',
                    'color' => 'text-blue-600'
                ],
                'etsy' => [
                    'name' => 'Etsy',
                    'description' => 'Продажба в Etsy marketplace',
                    'status' => 0,
                    'api_key' => '',
                    'shared_secret' => '',
                    'shop_id' => '',
                    'icon' => 'ri-store-line',
                    'color' => 'text-orange-500'
                ]
            ]);
        }

        return $this;
    }

    /**
     * Подготвя URL-и за AJAX заявки и действия
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareUrlsAndActions() {
        $urls = [
            'save_url' => $this->getAdminLink('setting/setting/integrations_save'),
            'test_integration_url' => $this->getAdminLink('setting/setting/test_integration'),
            'toggle_integration_url' => $this->getAdminLink('setting/setting/toggle_integration'),
            'add_webhook_url' => $this->getAdminLink('setting/setting/add_webhook'),
            'remove_webhook_url' => $this->getAdminLink('setting/setting/remove_webhook'),
            'test_webhook_url' => $this->getAdminLink('setting/setting/test_webhook'),
            'generate_api_key_url' => $this->getAdminLink('setting/setting/generate_api_key'),
            'sync_marketplace_url' => $this->getAdminLink('setting/setting/sync_marketplace'),
            'export_integrations_url' => $this->getAdminLink('setting/setting/export_integrations')
        ];

        foreach ($urls as $key => $url) {
            $this->setData($key, $url);
        }

        return $this;
    }

    /**
     * Подготвя правила за валидация
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareValidationRules() {
        $validation_rules = [
            'google_analytics_tracking_id' => [
                'required' => false,
                'pattern' => '^(UA|G)-[0-9A-Z-]+$'
            ],
            'facebook_pixel_id' => [
                'required' => false,
                'type' => 'number'
            ],
            'api_rate_limit' => [
                'required' => false,
                'type' => 'number',
                'min' => 1,
                'max' => 10000
            ],
            'webhook_timeout' => [
                'required' => false,
                'type' => 'number',
                'min' => 5,
                'max' => 300
            ]
        ];

        $this->setData('validation_rules', $validation_rules);

        return $this;
    }

    /**
     * Получава статистики за интеграциите
     *
     * @return array
     */
    private function getIntegrationStatistics() {
        try {
            $this->loadModelAs('setting/integrations_settings_model', 'integrationsModel');
            
            return [
                'active_analytics' => $this->integrationsModel->getActiveAnalyticsCount(),
                'active_social' => $this->integrationsModel->getActiveSocialCount(),
                'active_apis' => $this->integrationsModel->getActiveAPIsCount(),
                'active_webhooks' => $this->integrationsModel->getActiveWebhooksCount(),
                'active_marketplaces' => $this->integrationsModel->getActiveMarketplacesCount(),
                'api_calls_today' => $this->integrationsModel->getAPICallsToday(),
                'webhook_calls_today' => $this->integrationsModel->getWebhookCallsToday()
            ];
            
        } catch (Exception $e) {
            return [
                'active_analytics' => 1,
                'active_social' => 0,
                'active_apis' => 1,
                'active_webhooks' => 0,
                'active_marketplaces' => 0,
                'api_calls_today' => 0,
                'webhook_calls_today' => 0
            ];
        }
    }

    /**
     * Проверява дали има предупреждения за интеграциите
     *
     * @return array
     */
    private function getIntegrationWarnings() {
        $warnings = [];
        
        // Проверка за analytics интеграции
        $analytics = $this->getData('analytics_integrations') ?: [];
        $active_analytics = array_filter($analytics, function($integration) {
            return !empty($integration['status']);
        });
        
        if (empty($active_analytics)) {
            $warnings[] = [
                'type' => 'info',
                'message' => 'Няма активни analytics интеграции. Препоръчваме да активирате Google Analytics.'
            ];
        }
        
        // Проверка за API настройки
        $api_integrations = $this->getData('api_integrations') ?: [];
        if (!empty($api_integrations['opencart_api']['status']) && empty($api_integrations['opencart_api']['api_key'])) {
            $warnings[] = [
                'type' => 'warning',
                'message' => 'OpenCart API е активиран, но няма зададен API ключ'
            ];
        }
        
        // Проверка за webhooks
        $webhooks = $this->getData('webhooks') ?: [];
        $active_webhooks = array_filter($webhooks, function($webhook) {
            return !empty($webhook['status']);
        });
        
        if (!empty($active_webhooks)) {
            foreach ($active_webhooks as $webhook) {
                if (empty($webhook['url'])) {
                    $warnings[] = [
                        'type' => 'warning',
                        'message' => 'Webhook "' . $webhook['name'] . '" е активиран, но няма зададен URL'
                    ];
                }
            }
        }
        
        return $warnings;
    }

    /**
     * Получава конфигурацията за JavaScript модула
     *
     * @return array
     */
    public function getJavaScriptConfig() {
        return [
            'userToken' => $this->getUserToken(),
            'urls' => [
                'save' => $this->getData('save_url'),
                'testIntegration' => $this->getData('test_integration_url'),
                'toggleIntegration' => $this->getData('toggle_integration_url'),
                'addWebhook' => $this->getData('add_webhook_url'),
                'removeWebhook' => $this->getData('remove_webhook_url'),
                'testWebhook' => $this->getData('test_webhook_url'),
                'generateApiKey' => $this->getData('generate_api_key_url'),
                'syncMarketplace' => $this->getData('sync_marketplace_url'),
                'exportIntegrations' => $this->getData('export_integrations_url')
            ],
            'validationRules' => $this->getData('validation_rules'),
            'statistics' => $this->getIntegrationStatistics()
        ];
    }
}
