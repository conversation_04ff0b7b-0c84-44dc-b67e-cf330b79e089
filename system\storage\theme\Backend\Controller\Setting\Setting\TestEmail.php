<?php

namespace Theme25\Backend\Controller\Setting\Setting;

/**
 * AJAX контролер за тестване на email настройки
 */
class TestEmail extends \Theme25\ControllerSubMethods {

    /**
     * Изпълнява тестването на email
     */
    public function execute() {
        $json = [];

        try {
            // Проверка на права за достъп
            if (!$this->hasPermission('modify', 'setting/setting')) {
                $json['error'] = 'Нямате права за тази операция';
                $this->outputJson($json);
                return;
            }

            // Получаване на email адреса за тест
            $test_email = $this->requestPost('test_email');
            if (empty($test_email) || !filter_var($test_email, FILTER_VALIDATE_EMAIL)) {
                $json['error'] = 'Моля, въведете валиден email адрес';
                $this->outputJson($json);
                return;
            }

            // Зареждане на mail библиотеката
            $this->loadLibraryAs('mail', 'mail');

            // Подготовка на тестовото съобщение
            $subject = 'Тестово съобщение от ' . $this->config->get('config_name');
            $message = "Здравейте,\n\n";
            $message .= "Това е тестово съобщение за проверка на email настройките.\n";
            $message .= "Ако получавате това съобщение, email настройките работят правилно.\n\n";
            $message .= "Изпратено от: " . $this->config->get('config_name') . "\n";
            $message .= "Дата: " . date('d.m.Y H:i:s') . "\n";

            // Конфигуриране на mail обекта
            $this->mail->setTo($test_email);
            $this->mail->setFrom($this->config->get('config_email'));
            $this->mail->setSender($this->config->get('config_name'));
            $this->mail->setSubject($subject);
            $this->mail->setText($message);

            // Изпращане на email
            if ($this->mail->send()) {
                $json['success'] = 'Тестовият email е изпратен успешно до ' . $test_email;
            } else {
                $json['error'] = 'Възникна грешка при изпращането на email';
            }

        } catch (Exception $e) {
            $json['error'] = 'Възникна грешка: ' . $e->getMessage();
        }

        $this->outputJson($json);
    }
}
