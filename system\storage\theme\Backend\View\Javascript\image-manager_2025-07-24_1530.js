/**
 * JavaScript модул за управление на изображения в продуктовата форма
 *
 * Включва система за ограничаване на едновременните AJAX заявки за избягване на
 * "Too many connections" грешки при зареждане на множество thumbnails.
 *
 * Функционалности:
 * - Максимум 10 едновременни AJAX заявки (конфигурируемо)
 * - Опашка за чакащи заявки
 * - Автоматично управление на състоянието на заявките
 * - Обработка на грешки без нарушаване на опашката
 */
(function() {
    'use strict';

    // Разширяване на основния модул
    document.addEventListener('DOMContentLoaded', function() {
        // Инициализиране на функционалността за изображения
        if (typeof BackendModule !== 'undefined') {
            BackendModule.initImageManager();
        }
    });

    // Добавяне на функционалност към основния модул
    if (typeof BackendModule !== 'undefined') {
        Object.assign(BackendModule, {
            // Конфигурация за мениджъра на изображения
            imageManager: {
                currentDirectory: '',
                selectedImages: [],
                isOpen: false,
                lastUploadDirectory: '',
                searchQuery: '',
                allItems: [],
                filteredItems: [],
                searchTimeout: null,
                baseUrl: '',
                createFolderUrl: '',
                singleSelectionMode: false,
                onSelectCallback: null, // Callback функция
                targetElement: null, // Елемент, за който се избира изображение (при single select)
                pagination: {
                    currentPage: 1,
                    limit: 30,
                    hasMore: false,
                    loading: false
                },
                // Система за ограничаване на паралелните AJAX заявки
                requestQueue: {
                    maxConcurrent: 10,        // Максимум 10 едновременни заявки
                    activeRequests: 0,        // Брой активни заявки в момента
                    pendingQueue: []          // Опашка с чакащи заявки
                }
            },

            /**
             * Инициализация на Image Manager-а
             */
            initImageManager: function() {
                this.loadImageManagerTemplate();
            },

            /**
             * Зареждане на template за мениджъра на изображения
             */
            loadImageManagerTemplate: function() {
                // Зареждаме template-а в DOM-а ако не е зареден
                if (!document.getElementById('image-manager-modal')) {
                    fetch('index.php?route=common/imagemanager/template&user_token=' + (BackendModule.config.userToken || ''), {
                        cache: 'no-store',
                        headers: { 'Cache-Control': 'no-cache, no-store, must-revalidate', 'Pragma': 'no-cache', 'Expires': '0' }
                    })
                        .then(response => response.text())
                        .then(html => {
                            document.body.insertAdjacentHTML('beforeend', html);
                            this.initImageManagerModal();
                        })
                        .catch(error => {
                            console.error('Грешка при зареждане на template:', error);
                        });
                }
            },

            /**
             * Инициализация на модалния прозорец за мениджъра на изображения
             */
            initImageManagerModal: function() {

                const modal = document.getElementById('image-manager-modal');

                // Event listeners за модала
                modal.addEventListener('click', (e) => {
                    if (!e.target.closest('.bg-white')) {
                        this.closeImageManager();
                        return;
                    }

                    const targetElement = e.target.closest('[id]') || e.target;
                    const targetId = targetElement.id;

                    switch (targetId) {
                        case 'close-image-manager':
                            this.closeImageManager();
                            break;
                        case 'navigate-up':
                            this.navigateUp();
                            break;
                        case 'refresh-manager':
                            this.refreshManager();
                            break;
                        case 'upload-files-btn':
                            document.getElementById('upload-files-input').click();
                            break;
                        case 'create-folder-btn':
                            this.showCreateFolderDialog();
                            break;
                        case 'clear-selection':
                            this.clearSelection();
                            break;
                        case 'add-selected':
                            this.executeCallback();
                            break;
                        case 'clear-search':
                            this.clearSearch();
                            break;
                    }
                });

                // Search functionality with debouncing
                const searchInput = document.getElementById('search-images');
                if (searchInput) {
                    searchInput.addEventListener('input', (e) => {
                        this.handleSearchWithDebounce(e.target.value);
                    });
                }

                // Infinite scroll functionality
                this.initInfiniteScroll();

                // Upload files input
                const uploadInput = document.getElementById('upload-files-input');
                if (uploadInput) {
                    uploadInput.addEventListener('change', (e) => {
                        const files = Array.from(e.target.files);
                        if (files.length > 0) {
                            this.uploadFilesToManager(files);
                        }
                        e.target.value = ''; // Reset input
                    });
                }

                // Create folder dialog event listeners
                this.initCreateFolderDialog();

                // Breadcrumb navigation
                document.addEventListener('click', (e) => {
                    if (e.target.closest('.breadcrumb-item')) {
                        const path = e.target.closest('.breadcrumb-item').dataset.path;
                        this.navigateToDirectory(path);
                    }
                });

                // Drag & Drop в мениджъра
                if (modal) {
                    modal.addEventListener('dragover', (e) => {
                        e.preventDefault();
                        document.getElementById('drop-zone-overlay').classList.remove('hidden');
                    });

                    modal.addEventListener('dragleave', (e) => {
                        if (!modal.contains(e.relatedTarget)) {
                            document.getElementById('drop-zone-overlay').classList.add('hidden');
                        }
                    });

                    modal.addEventListener('drop', (e) => {
                        e.preventDefault();
                        document.getElementById('drop-zone-overlay').classList.add('hidden');
                        
                        const files = Array.from(e.dataTransfer.files);
                        if (files.length > 0) {
                            this.uploadFilesToManager(files);
                        }
                    });
                }
            },

         
            /**
             * Отваря мениджъра на изображения
             * @param {object} options - Опции за отваряне
             * @param {boolean} options.singleSelection - Дали да работи в single selection режим
             * @param {function} options.callback - Функция, която да се изпълни при избор
             * @param {HTMLElement} options.target - Елементът, за който се избира
             * @param {string} options.startDirectory - Начална директория
             */
            openImageManager: function(options = {}) {
                const modal = document.getElementById('image-manager-modal');
                if (!modal) {
                    console.error('Image Manager modal not found!');
                    return;
                }
                
                const { singleSelection = false, callback = null, target = null, startDirectory = '' } = options;
                
                this.imageManager.singleSelectionMode = singleSelection;
                this.imageManager.onSelectCallback = callback;
                this.imageManager.targetElement = target;
                this.imageManager.currentDirectory = startDirectory || this.imageManager.lastUploadDirectory || '';
                this.imageManager.isOpen = true;
                this.imageManager.selectedImages = [];

                const userToken = BackendModule.config.userToken || '';
                this.imageManager.createFolderUrl = `index.php?route=common/imagemanager/createfolder&user_token=${userToken}`;

                modal.classList.remove('hidden');
                this.loadDirectoryContents(this.imageManager.currentDirectory);
                this.updateSelectionModeUI();
            },

            /**
             * Затваряне на мениджъра на изображения
             */
            closeImageManager: function() {
                const modal = document.getElementById('image-manager-modal');
                if (modal) {
                    modal.classList.add('hidden');
                }
                // Нулиране на състоянието
                this.imageManager.isOpen = false;
                this.imageManager.selectedImages = [];
                this.imageManager.singleSelectionMode = false;
                this.imageManager.onSelectCallback = null;
                this.imageManager.targetElement = null;

                // Изчистваме опашката от заявки
                this.clearRequestQueue();
            },

            /**
             * Изчистване на опашката от заявки
             */
            clearRequestQueue: function() {
                const queue = this.imageManager.requestQueue;
                queue.pendingQueue = [];
                // Забележка: активните заявки ще завършат естествено
                // но няма да стартират нови заявки от опашката
            },

            /**
             * Получава информация за състоянието на опашката (за debugging)
             */
            getQueueStatus: function() {
                const queue = this.imageManager.requestQueue;
                return {
                    maxConcurrent: queue.maxConcurrent,
                    activeRequests: queue.activeRequests,
                    pendingCount: queue.pendingQueue.length,
                    totalRequests: queue.activeRequests + queue.pendingQueue.length
                };
            },

            /**
             * Конфигурира максималния брой едновременни заявки
             */
            setMaxConcurrentRequests: function(maxConcurrent) {
                if (typeof maxConcurrent === 'number' && maxConcurrent > 0) {
                    this.imageManager.requestQueue.maxConcurrent = maxConcurrent;

                    // Ако новият лимит е по-висок и има чакащи заявки, стартираме ги
                    this.processQueueIfPossible();
                }
            },

            /**
             * Проверява дали може да стартира нови заявки от опашката
             */
            processQueueIfPossible: function() {
                const queue = this.imageManager.requestQueue;

                while (queue.pendingQueue.length > 0 && queue.activeRequests < queue.maxConcurrent) {
                    const nextRequest = queue.pendingQueue.shift();
                    this.executeRequest(nextRequest);
                }
            },

            /**
             * Изпълнява callback функцията с избраните изображения
             */
            executeCallback: function() {
                if (this.imageManager.onSelectCallback && typeof this.imageManager.onSelectCallback === 'function') {
                    this.imageManager.onSelectCallback(this.imageManager.selectedImages, this.imageManager.targetElement);
                }
                this.closeImageManager();
            },

            /**
             * Качване на файлове директно в мениджъра
             */
            uploadFilesToManager: function(files) {
                if (!files || files.length === 0) return;
                
                this.showUploadProgress();
                
                const userToken = BackendModule.config.userToken || '';
                const directory = this.imageManager.currentDirectory || '';
                
                const formData = new FormData();
                files.forEach(file => formData.append('files[]', file));

                fetch(`index.php?route=common/imagemanager/upload&user_token=${userToken}&directory=${encodeURIComponent(directory)}`, {
                    method: 'POST',
                    body: formData,
                    cache: 'no-store',
                    headers: { 'Cache-Control': 'no-cache, no-store, must-revalidate', 'Pragma': 'no-cache', 'Expires': '0' }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        this.showAlert('success', data.message || 'Файловете са качени успешно');
                        this.refreshManager();
                    } else {
                        this.showAlert('error', data.error || 'Грешка при качване на файловете');
                    }
                })
                .catch(error => {
                    console.error('Грешка при качване:', error);
                    this.showAlert('error', 'Възникна грешка при качване на файловете');
                })
                .finally(() => {
                    this.hideUploadProgress();
                });
            },

            /**
             * Актуализира UI според режима на селекция
             */
            updateSelectionModeUI: function() {
                const modal = document.getElementById('image-manager-modal');
                if (!modal) return;

                const selectButton = modal.querySelector('#select-images-btn');
                if (selectButton && this.imageManager.singleSelectionMode) {
                    selectButton.textContent = 'Избери изображение';
                } else if (selectButton) {
                    selectButton.textContent = 'Избери изображения';
                }
            },

     
            /**
             * Качване на файлове
             */
            uploadFiles: function(files) {
                if (!files || files.length === 0) return;

                // Валидация на файловете
                const validFiles = files.filter(file => {
                    const isImage = /image\/(jpeg|png|gif)/i.test(file.type);
                    const isValidSize = file.size <= 5 * 1024 * 1024; // 5MB
                    
                    if (!isImage) {
                        this.showAlert('error', `${file.name} не е валиден формат на изображение`);
                        return false;
                    }
                    if (!isValidSize) {
                        this.showAlert('error', `${file.name} е по-голям от 5MB`);
                        return false;
                    }
                    return true;
                });

                if (validFiles.length === 0) return;

                this.showAlert('info', 'Качване на изображения...', 0);

                // Качване на файловете
                const uploadPromises = validFiles.map(file => this.uploadSingleFile(file));

                Promise.all(uploadPromises)
                    .then(results => {
                        const successCount = results.filter(result => result.success).length;
                        if (successCount > 0) {
                            this.showAlert('success', `Успешно качени ${successCount} изображения`);
                            return results.filter(result => result.success);
                        }
                    })
                    .catch(error => {
                        console.error('Грешка при качване:', error);
                        this.showAlert('error', 'Възникна грешка при качване на изображенията');
                    });
            },

            /**
             * Качване на единичен файл
             */
            uploadSingleFile: function(file) {
                const formData = new FormData();
                formData.append('files[]', file);

                const userToken = BackendModule.config.userToken || '';
                const directory = this.imageManager.currentDirectory || '';

                return fetch(`index.php?route=common/imagemanager/upload&user_token=${userToken}&directory=${encodeURIComponent(directory)}`, {
                    method: 'POST',
                    body: formData,
                    cache: 'no-store',
                    headers: { 'Cache-Control': 'no-cache, no-store, must-revalidate', 'Pragma': 'no-cache', 'Expires': '0' }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.files && data.files.length > 0) {
                        // Запазваме директорията на последното качване
                        this.imageManager.lastUploadDirectory = directory;
                        const uploadedFile = data.files[0];
                        return {
                            success: true,
                            filename: uploadedFile.path,
                            thumb: uploadedFile.thumb
                        };
                    } else {
                        const errorMsg = data.error || data.errors?.[0] || `Грешка при качване на ${file.name}`;
                        this.showAlert('error', errorMsg);
                        return { success: false };
                    }
                })
                .catch(error => {
                    console.error('Грешка при качване на файл:', error);
                    this.showAlert('error', `Възникна грешка при качване на ${file.name}`);
                    return { success: false };
                });
            },

            /**
             * Получава размерите на браузера за изпращане към сървъра
             */
            getModalDimensions: function() {
                const itemsGrid = document.getElementById('items-grid').parentElement;

                // Използваме размерите на браузера вместо модала
                const viewportWidth = window.innerWidth;
                const viewportHeight = window.innerHeight;

                // За grid ширината използваме 80% от viewport ширината като приблизителна стойност
                // или реалната ширина на grid-а ако е наличен
                const gridWidth = itemsGrid ? (itemsGrid.offsetWidth - 32) : Math.floor(viewportWidth * 0.8);

                return {
                    width: viewportWidth,
                    height: viewportHeight,
                    gridWidth: gridWidth
                };
            },

            /**
             * Изчислява оптималния брой изображения за зареждане въз основа на размерите на модала
             */
            calculateOptimalImageCount: function() {
                const itemsGrid = document.getElementById('items-grid').parentElement;

                // Използваме размерите на браузера вместо модала
                const viewportWidth = window.innerWidth;
                const viewportHeight = window.innerHeight;

                // Изчисляваме приблизителната височина на модала (70% от viewport)
                const modalHeight = Math.floor(viewportHeight * 0.7);

                // Изчисляваме достъпната височина за grid (отчитаме modal header, breadcrumb, toolbar, footer)
                const availableHeight = modalHeight - 150; // по-консервативна оценка

                // Приблизителни размери на един grid item (включително gap)
                const itemHeight = 120; // височина на изображение + текст + padding
                const itemWidth = 100;  // ширина на изображение + padding
                const gridGap = 8;      // gap между елементите

                // За grid ширината използваме 80% от viewport ширината като приблизителна стойност
                const gridWidth = itemsGrid ? (itemsGrid.offsetWidth - 32) : Math.floor(viewportWidth * 0.8);

                // Изчисляваме колко колони се побират в ширината
                const columnsPerRow = Math.max(1, Math.floor(gridWidth / (itemWidth + gridGap)));

                // Изчисляваме колко реда се побират във височината
                const rowsVisible = Math.max(1, Math.floor(availableHeight / (itemHeight + gridGap)));

                // Общ брой видими изображения
                const visibleImages = columnsPerRow * rowsVisible;

                // Добавяме още 1-2 реда за да се създаде scrollbar и да се активира infinite scroll
                const optimalCount = visibleImages + (columnsPerRow * 2);

                // Минимум 12, максимум 60 изображения (увеличени лимити за по-голяма гъвкавост)
                return Math.max(12, Math.min(60, optimalCount));
            },

            /**
             * Зареждане на съдържанието на директория
             */
            loadDirectoryContents: function(directory = '', append = false) {

                const loadingIndicator = document.getElementById('loading-indicator');
                const itemsGrid = document.getElementById('items-grid');
                const emptyState = document.getElementById('empty-state');

                if (loadingIndicator) loadingIndicator.classList.remove('hidden');
                if (!append) {
                    if (itemsGrid) itemsGrid.classList.add('hidden');
                    if (emptyState) emptyState.classList.add('hidden');
                    // Нулираме pagination при нова директория
                    this.imageManager.pagination.currentPage = 1;
                }

                this.imageManager.pagination.loading = true;

                // Изчисляваме динамично броя изображения за зареждане
                const dynamicLimit = this.calculateOptimalImageCount();

                // Получаваме размерите на модала за изпращане към сървъра
                const modalDimensions = this.getModalDimensions();

                const userToken = BackendModule.config.userToken || '';
                const page = append ? this.imageManager.pagination.currentPage + 1 : 1;

                // Добавяме размерите на модала към URL параметрите
                const url = `index.php?route=common/imagemanager&user_token=${userToken}&directory=${encodeURIComponent(directory)}&page=${page}&limit=${dynamicLimit}&modal_width=${modalDimensions.width}&modal_height=${modalDimensions.height}&grid_width=${modalDimensions.gridWidth}`;

                // Запазваме следващата страница за актуализация
                const nextPage = page;

                fetch(url, {
                        cache: 'no-store',
                        headers: { 'Cache-Control': 'no-cache, no-store, must-revalidate', 'Pragma': 'no-cache', 'Expires': '0' }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            if (append) {
                                this.appendDirectoryContents(data);
                            } else {
                                this.renderDirectoryContents(data);
                                this.updateBreadcrumb(data.breadcrumb);
                                this.imageManager.currentDirectory = directory;
                            }

                            this.imageManager.baseUrl = data.base_url ?? '';

                            // Актуализираме pagination данните
                            if (data.pagination) {
                                // При append операция, използваме nextPage вместо data.pagination.page
                                this.imageManager.pagination.currentPage = append ? nextPage : data.pagination.page;
                                this.imageManager.pagination.hasMore = data.pagination.has_more;
                            }
                        } else {
                            this.showAlert('error', data.error || 'Грешка при зареждане на директорията');
                        }
                    })
                    .catch(error => {
                        console.error('Грешка при зареждане на директория:', error);
                        this.showAlert('error', 'Възникна грешка при зареждане на директорията');
                    })
                    .finally(() => {
                        this.imageManager.pagination.loading = false;
                        if (loadingIndicator) loadingIndicator.classList.add('hidden');
                    });
            },

            getBaseUrl: function() {
                return this.imageManager.baseUrl;
            },

            /**
             * Рендериране на съдържанието на директория
             */
            renderDirectoryContents: function(data) {
                const itemsGrid = document.getElementById('items-grid');

                if (!itemsGrid) return;

                const gridContainer = itemsGrid.querySelector('.grid');
                if (!gridContainer) return;

                // Запазваме всички елементи за търсене
                this.imageManager.allItems = data.items || [];
                this.imageManager.filteredItems = [...this.imageManager.allItems];

                this.renderItems(this.imageManager.filteredItems);
            },

            /**
             * Добавя нови елементи към съществуващите (за infinite scroll)
             */
            appendDirectoryContents: function(data) {
                if (!data.items || data.items.length === 0) return;

                const gridContainer = document.getElementById('items-grid').querySelector('.grid');
                if (!gridContainer) return;

                // Филтрираме само изображенията (папките вече са заредени)
                const newImages = data.items.filter(item => item.type === 'image');

                newImages.forEach(item => {
                    const itemElement = this.createItemElement(item);
                    gridContainer.appendChild(itemElement);

                    // Зареждане на thumbnail за новите изображения (cached или lazy loading)
                    if (item.type === 'image') {
                        // Малко забавяне за да се осигури че DOM елементът е готов
                        const self = this;
                        setTimeout(function() {
                            self.loadImageThumbnail(item, itemElement);
                        }, 10);
                    }
                });

                // Актуализираме списъка с всички елементи
                this.imageManager.allItems = [...this.imageManager.allItems, ...newImages];
                this.imageManager.filteredItems = [...this.imageManager.filteredItems, ...newImages];

                // Актуализираме списъка с image елементи
                setTimeout(() => {
                    this.updateImageElementsList();
                }, 100);
            },

            /**
             * Рендира елементите в grid-а
             */
            renderItems: function(items) {
                const itemsGrid = document.getElementById('items-grid');
                const emptyState = document.getElementById('empty-state');
                const gridContainer = itemsGrid.querySelector('.grid');

                if (!gridContainer) return;

                gridContainer.innerHTML = '';

                if (items && items.length > 0) {
                    items.forEach(item => {
                        const itemElement = this.createItemElement(item);
                        gridContainer.appendChild(itemElement);

                        // Зареждане на thumbnail за изображения (cached или lazy loading)
                        if (item.type === 'image') {
                            // Малко забавяне за да се осигури че DOM елементът е готов
                            const self = this;
                            setTimeout(function() {
                                self.loadImageThumbnail(item, itemElement);
                            }, 10);
                        }
                    });

                    itemsGrid.classList.remove('hidden');
                    if (emptyState) emptyState.classList.add('hidden');

                    // Актуализираме списъка с image елементи след рендиране
                    setTimeout(() => {
                        this.updateImageElementsList();
                    }, 100);
                } else {
                    itemsGrid.classList.add('hidden');
                    if (emptyState) emptyState.classList.remove('hidden');
                }
            },

            /**
             * Добавя заявка в опашката за изпълнение
             */
            enqueueRequest: function(requestFunction) {
                const queue = this.imageManager.requestQueue;

                if (queue.activeRequests < queue.maxConcurrent) {
                    // Има свободно място - изпълняваме директно
                    this.executeRequest(requestFunction);
                } else {
                    // Няма свободно място - добавяме в опашката
                    queue.pendingQueue.push(requestFunction);

                    // Логваме състоянието (за debugging)
                    if (window.console && console.debug) {
                        console.debug(`ImageManager: Заявка добавена в опашка. Активни: ${queue.activeRequests}, В опашка: ${queue.pendingQueue.length}`);
                    }
                }
            },

            /**
             * Изпълнява заявка и управлява състоянието на опашката
             */
            executeRequest: function(requestFunction) {
                const queue = this.imageManager.requestQueue;

                // Увеличаваме броя активни заявки
                queue.activeRequests++;

                // Логваме състоянието (за debugging)
                if (window.console && console.debug) {
                    console.debug(`ImageManager: Стартирана заявка. Активни: ${queue.activeRequests}, В опашка: ${queue.pendingQueue.length}`);
                }

                // Изпълняваме заявката
                const promise = requestFunction();

                // Когато заявката завърши (успешно или с грешка)
                promise.finally(() => {
                    // Намаляваме броя активни заявки
                    queue.activeRequests--;

                    // Логваме състоянието (за debugging)
                    if (window.console && console.debug) {
                        console.debug(`ImageManager: Завършена заявка. Активни: ${queue.activeRequests}, В опашка: ${queue.pendingQueue.length}`);
                    }

                    // Проверяваме дали има чакащи заявки в опашката
                    if (queue.pendingQueue.length > 0 && queue.activeRequests < queue.maxConcurrent) {
                        // Взимаме следващата заявка от опашката
                        const nextRequest = queue.pendingQueue.shift();
                        // Изпълняваме я
                        this.executeRequest(nextRequest);
                    }
                });

                return promise;
            },

            /**
             * Зарежда thumbnail за изображение (lazy loading) с ограничение на паралелните заявки
             */
            loadImageThumbnail: function(item, itemElement) {
                const img = itemElement.querySelector('img');
                if (!img) {
                    console.warn('Няма img елемент в itemElement за:', item.name);
                    return;
                }

                // Ако има cached thumbnail, показваме го директно
                if (item.has_cache && item.thumb) {
                    // console.log('Показвам cached thumbnail за:', item.name);
                    img.src = item.thumb;
                    img.alt = item.name;
                    img.classList.remove('loading');
                    return;
                }

                // За изображения без cache трябва да имаме thumb_url за AJAX заявка
                if (!item.thumb_url || item.thumb_url === '') {
                    console.warn('Няма thumb_url за изображение без cache:', item.name, 'has_cache:', item.has_cache);
                    img.alt = 'Няма URL за зареждане';
                    img.classList.remove('loading');
                    img.classList.add('error');
                    this.showWarningIcon(img);
                    return;
                }

                // Показваме placeholder докато се зарежда
                img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0zNSA0MEg2NVY2MEgzNVY0MFoiIGZpbGw9IiNEMUQ1REIiLz4KPC9zdmc+';
                img.classList.add('loading');
                img.alt = 'Зареждане...';

                // Запазваме контекста на this
                const self = this;

                // Създаваме функция за AJAX заявката, която ще бъде добавена в опашката
                const requestFunction = function() {
                    // Добавяме user_token към URL-а
                    const url = new URL(item.thumb_url, window.location.origin + '/admin/');
                    url.searchParams.set('user_token', self.getUserToken());

                    // AJAX заявка за thumbnail
                    return fetch(url.toString(), {
                        cache: 'no-store',
                        headers: { 'Cache-Control': 'no-cache, no-store, must-revalidate' }
                    })
                    .then(function(response) {
                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }
                        return response.json();
                    })
                    .then(function(data) {
                        if (data.success && data.thumbnail) {
                            img.src = data.thumbnail;
                            img.alt = item.name;
                            img.classList.remove('loading');
                        } else {
                            console.error('Грешка при зареждане на thumbnail:', data.error, 'show_warning_icon:', data.show_warning_icon);
                            img.alt = 'Грешка при зареждане';
                            img.classList.remove('loading');
                            img.classList.add('error');

                            // Показваме warning икона винаги при грешка
                            self.showWarningIcon(img);
                        }
                    })
                    .catch(function(error) {
                        console.error('AJAX грешка за', url.toString());
                        console.error('AJAX грешка при зареждане на thumbnail за', item.name, ':', error);
                        console.error(error);
                        img.alt = 'Грешка при зареждане';
                        img.classList.remove('loading');
                        img.classList.add('error');
                        self.showWarningIcon(img);
                    });
                };

                // Добавяме заявката в опашката за изпълнение
                this.enqueueRequest(requestFunction);
            },

            /**
             * Получава user_token от сесията
             */
            getUserToken: function() {
                // 1. Проверяваме в глобалния обект
                if (window.user_token) {
                    return window.user_token;
                }

                // 2. Проверяваме в meta тагове
                const tokenMeta = document.querySelector('meta[name="user_token"]');
                if (tokenMeta) {
                    return tokenMeta.getAttribute('content');
                }

                // 3. Проверяваме в скрити input полета
                const tokenInput = document.querySelector('input[name="user_token"]');
                if (tokenInput) {
                    return tokenInput.value;
                }

                // 4. Проверяваме в URL параметрите
                const urlParams = new URLSearchParams(window.location.search);
                if (urlParams.has('user_token')) {
                    return urlParams.get('user_token');
                }

                // Ако не намерим никъде токена
                return '';
            },


            /**
             * Показва warning икона за изображения, които не могат да се заредят
             */
            showWarningIcon: function(imgElement) {
                // Заменяме изображението с червена warning икона (триъгълник с удивителен знак)
                imgElement.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTMwIDVMNTUgNTBINUwzMCA1WiIgZmlsbD0iI0ZCQkYyNCIgc3Ryb2tlPSIjRjU5RTBCIiBzdHJva2Utd2lkdGg9IjIiLz4KPHBhdGggZD0iTTMwIDIwVjM1IiBzdHJva2U9IiNGNTlFMEIiIHN0cm9rZS13aWR0aD0iMyIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIi8+CjxjaXJjbGUgY3g9IjMwIiBjeT0iNDIiIHI9IjIiIGZpbGw9IiNGNTlFMEIiLz4KPC9zdmc+';
                imgElement.classList.add('warning-icon');
                imgElement.style.filter = 'none';
                imgElement.style.objectFit = 'contain';
                imgElement.style.padding = '10px';
            },

            /**
             * Създаване на елемент за файл/папка
             */
            createItemElement: function(item) {
                const element = document.createElement('div');

                if (item.type === 'directory') {
                    element.className = 'directory-item';
                    element.innerHTML = `
                        <div class="aspect-square rounded overflow-hidden bg-gray-100 flex items-center justify-center">
                            <i class="ri-folder-line text-5xl text-gray-400"></i>
                        </div>
                        <p class="text-xs text-gray-600 mt-1 truncate" title="${item.name}">${item.name}</p>
                    `;

                    element.addEventListener('click', () => {
                        // const newPath = this.imageManager.currentDirectory ?
                        //     `${this.imageManager.currentDirectory}/${item.path}` : item.path;

                        const newPath = `/${this.ltrim(item.path, '/')}`

                            // console.log('ImageManager: Навигация към папка:', newPath);

                        this.navigateToDirectory(newPath);
                    });
                } else {
                    element.className = 'image-item';

                    // За изображения без cache използваме placeholder, за cached - директно thumb URL
                    const imgSrc = item.has_cache ? item.thumb : 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0zNSA0MEg2NVY2MEgzNVY0MFoiIGZpbGw9IiNEMUQ1REIiLz4KPC9zdmc+';
                    const imgClass = item.has_cache ? 'w-full h-full object-cover' : 'w-full h-full object-cover loading';

                    // Проверяваме за предупреждения за формата
                    const formatWarning = this.getFormatWarningHtml(item);

                    element.innerHTML = `
                        <div class="aspect-square rounded overflow-hidden relative">
                            <img src="${imgSrc}" alt="${item.name}" class="${imgClass} rounded">
                            ${formatWarning}
                        </div>
                        <p class="text-xs text-gray-600 mt-1 truncate" title="${item.name}">${item.name}</p>
                    `;

                    // Добавяме data атрибути за селекция
                    element.dataset.path = item.path;
                    element.dataset.name = item.name;
                    element.dataset.thumb = item.thumb;

                    // Click event за селекция
                    element.addEventListener('click', (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        this.handleImageSelection(element, e);
                    });
                }

                return element;
            },

            ltrim: function(str, charToRemove) {
                while (str.startsWith(charToRemove)) {
                    str = str.slice(1);
                }
                return str;
            },

            rtrim: function(str, charToRemove) {
                while (str.endsWith(charToRemove)) {
                    str = str.slice(0, -1);
                }
                return str;
            },

            /**
             * Обработка на селекция на изображение
             */
            handleImageSelection: function(element, event) {
                const path = element.dataset.path;
                const name = element.dataset.name;
                const thumb = element.dataset.thumb;

                // Получаваме всички image елементи в текущия grid
                this.updateImageElementsList();

                // Проверяваме дали сме в single selection режим
                if (this.imageManager.singleSelectionMode) {
                    // В single selection режим - изчистваме всички селекции и селектираме само текущия
                    this.clearSelection();
                    this.selectSingleImage(element, path, name, thumb);
                } else {
                    // В multi-selection режим - използваме стандартната логика
                    if (event && event.shiftKey && this.imageManager.lastSelectedElement) {
                        // Shift+Click - range selection
                        this.handleRangeSelection(element);
                    } else if (event && event.ctrlKey) {
                        // Control+Click - toggle selection (запазваме останалите селектирани)
                        this.toggleImageSelection(element, path, name, thumb);
                    } else {
                        // Обикновен клик - toggle на текущия елемент
                        const isSelected = element.classList.contains('selected');

                        if (isSelected) {
                            // Ако е селектиран, го деселектираме
                            this.imageManager.selectedImages = this.imageManager.selectedImages.filter(img => img.path !== path);
                            element.classList.remove('selected');
                        } else {
                            // Ако не е селектиран, добавяме към селекцията (като Ctrl+Click)
                            this.toggleImageSelection(element, path, name, thumb);
                        }
                    }
                }

                // Запазваме последния селектиран елемент
                this.imageManager.lastSelectedElement = element;
                this.updateSelectionInfo();
            },

            /**
             * Актуализира списъка с всички image елементи
             */
            updateImageElementsList: function() {
                this.imageManager.allImageElements = Array.from(document.querySelectorAll('.image-item'));
            },

            /**
             * Обработва range selection (Shift+Click)
             */
            handleRangeSelection: function(currentElement) {
                const lastIndex = this.imageManager.allImageElements.indexOf(this.imageManager.lastSelectedElement);
                const currentIndex = this.imageManager.allImageElements.indexOf(currentElement);

                if (lastIndex === -1 || currentIndex === -1) return;

                const startIndex = Math.min(lastIndex, currentIndex);
                const endIndex = Math.max(lastIndex, currentIndex);

                // Селектираме всички елементи в диапазона
                for (let i = startIndex; i <= endIndex; i++) {
                    const element = this.imageManager.allImageElements[i];
                    const path = element.dataset.path;
                    const name = element.dataset.name;
                    const thumb = element.dataset.thumb;

                    if (!this.imageManager.selectedImages.find(img => img.path === path)) {
                        this.imageManager.selectedImages.push({ path, name, thumb });
                        element.classList.add('selected');
                    }
                }
            },

            /**
             * Toggle селекция на изображение (Control+Click)
             */
            toggleImageSelection: function(element, path, name, thumb) {
                const isSelected = element.classList.contains('selected');

                if (isSelected) {
                    // Премахваме от селекцията
                    this.imageManager.selectedImages = this.imageManager.selectedImages.filter(img => img.path !== path);
                    element.classList.remove('selected');
                } else {
                    // Добавяме към селекцията
                    this.imageManager.selectedImages.push({ path, name, thumb });
                    element.classList.add('selected');
                }
            },

            /**
             * Селектира единично изображение
             */
            selectSingleImage: function(element, path, name, thumb) {
                this.imageManager.selectedImages.push({ path, name, thumb });
                element.classList.add('selected');
            },

            /**
             * Обработва търсенето с debouncing
             */
            handleSearchWithDebounce: function(query) {
                // Изчистваме предишния timeout
                if (this.imageManager.searchTimeout) {
                    clearTimeout(this.imageManager.searchTimeout);
                }

                // Задаваме нов timeout за 500ms
                this.imageManager.searchTimeout = setTimeout(() => {
                    this.handleSearch(query);
                }, 500);
            },

            /**
             * Обработва търсенето
             */
            handleSearch: function(query) {
                this.imageManager.searchQuery = query.toLowerCase().trim();

                const clearButton = document.getElementById('clear-search');
                if (clearButton) {
                    if (this.imageManager.searchQuery) {
                        clearButton.classList.remove('hidden');
                    } else {
                        clearButton.classList.add('hidden');
                    }
                }

                if (!this.imageManager.searchQuery) {
                    // Показваме всички елементи
                    this.imageManager.filteredItems = [...this.imageManager.allItems];
                } else {
                    // Филтрираме елементите
                    this.imageManager.filteredItems = this.imageManager.allItems.filter(item => {
                        return item.name.toLowerCase().includes(this.imageManager.searchQuery);
                    });

                    // Добавяме рекурсивно търсене в подпапки
                    if (this.imageManager.searchQuery) {
                        this.performRecursiveSearch();
                    }
                }

                this.renderItems(this.imageManager.filteredItems);
            },

            /**
             * Изчиства търсенето
             */
            clearSearch: function() {
                // Изчистваме timeout ако има такъв
                if (this.imageManager.searchTimeout) {
                    clearTimeout(this.imageManager.searchTimeout);
                    this.imageManager.searchTimeout = null;
                }

                const searchInput = document.getElementById('search-images');
                const clearButton = document.getElementById('clear-search');

                if (searchInput) searchInput.value = '';
                if (clearButton) clearButton.classList.add('hidden');

                this.imageManager.searchQuery = '';
                this.imageManager.filteredItems = [...this.imageManager.allItems];
                this.renderItems(this.imageManager.filteredItems);
            },

            /**
             * Извършва рекурсивно търсене в подпапки
             */
            performRecursiveSearch: function() {
                // За рекурсивното търсене ще направим AJAX заявка към сървъра
                const userToken = BackendModule.config.userToken || '';
                const url = `index.php?route=common/imagemanager/search&user_token=${userToken}&query=${encodeURIComponent(this.imageManager.searchQuery)}&directory=${encodeURIComponent(this.imageManager.currentDirectory)}`;

                fetch(url, {
                        cache: 'no-store',
                        headers: { 'Cache-Control': 'no-cache, no-store, must-revalidate', 'Pragma': 'no-cache', 'Expires': '0' }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.items) {
                            // Комбинираме резултатите от рекурсивното търсене
                            const recursiveResults = data.items.filter(item => {
                                // Избягваме дублиране на елементи от текущата директория
                                return !this.imageManager.filteredItems.find(existing => existing.path === item.path);
                            });

                            this.imageManager.filteredItems = [...this.imageManager.filteredItems, ...recursiveResults];
                            this.renderItems(this.imageManager.filteredItems);
                        }
                    })
                    .catch(error => {
                        console.error('Грешка при рекурсивно търсене:', error);
                    });
            },

            /**
             * Актуализиране на информацията за селекцията
             */
            updateSelectionInfo: function() {
                const selectedCount = document.getElementById('selected-count');
                const addSelectedBtn = document.getElementById('add-selected');

                if (selectedCount) {
                    selectedCount.textContent = this.imageManager.selectedImages.length;
                }

                if (addSelectedBtn) {
                    addSelectedBtn.disabled = this.imageManager.selectedImages.length === 0;
                }
            },

            /**
             * Изчистване на селекцията
             */
            clearSelection: function() {
                this.imageManager.selectedImages = [];

                // Премахваме визуалната селекция
                document.querySelectorAll('.image-item.selected').forEach(item => {
                    item.classList.remove('selected');
                });

                this.updateSelectionInfo();
            },


            /**
             * Навигация нагоре в директорията
             */
            navigateUp: function() {
                const currentDir = this.imageManager.currentDirectory;

                console.log('ImageManager: navigateUp() извикан с currentDir:', currentDir);

                // Ако сме в root директорията, не можем да отидем по-нагоре
                if (!currentDir || currentDir === '') {
                    console.log('ImageManager: Вече сме в root директорията');
                    return;
                }

                const parts = currentDir.split('/').filter(part => part !== '');
                console.log('ImageManager: Части на пътя:', parts);

                if (parts.length > 1) {
                    // Премахваме последната част
                    parts.pop();
                    const parentDir = parts.join('/');
                    console.log('ImageManager: Навигация към родителска директория:', parentDir);
                    this.navigateToDirectory(parentDir);
                } else {
                    // Отиваме в root директорията
                    console.log('ImageManager: Навигация към root директория');
                    this.navigateToDirectory('');
                }
            },

            /**
             * Навигация към конкретна директория
             */
            navigateToDirectory: function(directory) {
                this.clearSelection();
                this.loadDirectoryContents(directory);
            },

            /**
             * Обновяване на мениджъра
             */
            refreshManager: function() {
                this.loadDirectoryContents(this.imageManager.currentDirectory);
            },

            /**
             * Актуализиране на breadcrumb навигацията
             */
            updateBreadcrumb: function(breadcrumbData) {
                const breadcrumbNav = document.getElementById('breadcrumb-nav');
                if (!breadcrumbNav || !breadcrumbData) return;

                breadcrumbNav.innerHTML = '';

                breadcrumbData.forEach((item, index) => {
                    const isLast = index === breadcrumbData.length - 1;

                    const breadcrumbItem = document.createElement('span');
                    breadcrumbItem.className = `breadcrumb-item ${isLast ? 'active' : ''}`;
                    breadcrumbItem.textContent = item.name;
                    breadcrumbItem.dataset.path = item.path;

                    // Всички breadcrumb елементи са кликаеми, включително последният
                    breadcrumbItem.addEventListener('click', () => {
                        this.navigateToDirectory(item.path);
                    });

                    breadcrumbNav.appendChild(breadcrumbItem);

                    // Добавяме разделител ако не е последният
                    if (!isLast) {
                        const separator = document.createElement('span');
                        separator.className = 'text-gray-400 mx-2';
                        separator.innerHTML = '<i class="ri-arrow-right-s-line"></i>';
                        breadcrumbNav.appendChild(separator);
                    }
                });
            },

            
            /**
             * Показване на progress за качване
             */
            showUploadProgress: function() {
                const modal = document.getElementById('upload-progress-modal');
                if (modal) {
                    modal.classList.remove('hidden');
                }
            },

            /**
             * Скриване на progress за качване
             */
            hideUploadProgress: function() {
                const modal = document.getElementById('upload-progress-modal');
                if (modal) {
                    modal.classList.add('hidden');
                }
            },

            /**
             * Инициализира infinite scroll функционалността
             */
            initInfiniteScroll: function() {
                const modal = document.getElementById('image-manager-modal');
                if (!modal) return;

                // Търсим правилния scroll контейнер
                const contentArea = modal.querySelector('.max-h-96.overflow-y-auto') ||
                                  modal.querySelector('[style*="max-height"]') ||
                                  modal.querySelector('.overflow-y-auto');

                if (!contentArea) {
                    console.warn('ImageManager: Не е намерен scroll контейнер за infinite scroll');
                    return;
                }

                contentArea.addEventListener('scroll', () => {
                    // Проверяваме дали сме близо до края на контейнера
                    const scrollTop = contentArea.scrollTop;
                    const scrollHeight = contentArea.scrollHeight;
                    const clientHeight = contentArea.clientHeight;

                    // Зареждаме нови елементи когато сме на 80% от скрола (по-рано)
                    const scrollPercentage = (scrollTop + clientHeight) / scrollHeight;

                    if (scrollPercentage >= 0.8 &&
                        this.imageManager.pagination.hasMore &&
                        !this.imageManager.pagination.loading &&
                        !this.imageManager.searchQuery) { // Не зареждаме при активно търсене

                        this.loadMoreImages();
                    }
                });
            },

            /**
             * Зарежда повече изображения (infinite scroll)
             */
            loadMoreImages: function() {
                if (this.imageManager.pagination.loading || !this.imageManager.pagination.hasMore) {
                    return;
                }

                this.loadDirectoryContents(this.imageManager.currentDirectory, true);
            },

            /**
             * Генерира HTML за предупреждение за формата на файла
             */
            getFormatWarningHtml: function(item) {
                if (!item.format_analysis || !item.format_analysis.format_mismatch) {
                    return '';
                }

                const analysis = item.format_analysis;
                const warningTitle = `Предупреждение: ${analysis.warning_message}. Препоръчително разширение: .${analysis.suggested_extension}`;

                return `
                    <div class="absolute top-0 left-0 right-0 bottom-0 bg-white flex items-center justify-center border border-gray-300 rounded">
                        <div class="absolute bg-yellow-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs cursor-help"
                            title="${warningTitle}">
                            <i class="ri-error-warning-line"></i>
                        </div>
                    </div>
                `;
            },

            /**
             * Инициализация на диалога за създаване на папка
             */
            initCreateFolderDialog: function() {
                const modal = document.getElementById('create-folder-modal');
                const folderNameInput = document.getElementById('folder-name-input');
                const cancelBtn = document.getElementById('cancel-create-folder');
                const confirmBtn = document.getElementById('confirm-create-folder');
                const errorDiv = document.getElementById('folder-name-error');

                if (!modal || !folderNameInput || !cancelBtn || !confirmBtn) {
                    return;
                }

                // Cancel button
                cancelBtn.addEventListener('click', () => {
                    this.hideCreateFolderDialog();
                });

                // Confirm button
                confirmBtn.addEventListener('click', () => {
                    this.createFolder();
                });

                // Enter key in input
                folderNameInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.createFolder();
                    } else if (e.key === 'Escape') {
                        this.hideCreateFolderDialog();
                    }
                });

                // Clear error on input
                folderNameInput.addEventListener('input', () => {
                    errorDiv.classList.add('hidden');
                    errorDiv.textContent = '';
                });

                // Close on overlay click
                modal.addEventListener('click', (e) => {
                    if (e.target === modal) {
                        this.hideCreateFolderDialog();
                    }
                });
            },

            /**
             * Показва диалога за създаване на папка
             */
            showCreateFolderDialog: function() {
                const modal = document.getElementById('create-folder-modal');
                const folderNameInput = document.getElementById('folder-name-input');
                const errorDiv = document.getElementById('folder-name-error');

                if (modal && folderNameInput) {
                    // Изчистваме полето и грешките
                    folderNameInput.value = '';
                    errorDiv.classList.add('hidden');
                    errorDiv.textContent = '';

                    // Показваме модала
                    modal.classList.remove('hidden');

                    // Фокусираме полето
                    setTimeout(() => {
                        folderNameInput.focus();
                    }, 100);
                }
            },

            /**
             * Скрива диалога за създаване на папка
             */
            hideCreateFolderDialog: function() {
                const modal = document.getElementById('create-folder-modal');
                if (modal) {
                    modal.classList.add('hidden');
                }
            },

            /**
             * Валидира името на папката
             */
            validateFolderName: function(name) {
                const errors = [];

                // Проверка за празно име
                if (!name || name.trim() === '') {
                    errors.push('Името на папката не може да бъде празно');
                    return errors;
                }

                name = name.trim();

                // Проверка за дължина
                if (name.length > 255) {
                    errors.push('Името на папката е твърде дълго (максимум 255 символа)');
                }

                // Забранени символи за Windows/Linux файлови системи
                const forbiddenChars = /[<>:"/\\|?*\x00-\x1f]/;
                if (forbiddenChars.test(name)) {
                    errors.push('Името съдържа забранени символи: < > : " / \\ | ? *');
                }

                // Забранени имена (Windows reserved names)
                const reservedNames = ['CON', 'PRN', 'AUX', 'NUL', 'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9', 'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'];
                if (reservedNames.includes(name.toUpperCase())) {
                    errors.push('Това име е запазено от системата');
                }

                // Проверка за точки в началото или края
                if (name.startsWith('.') || name.endsWith('.')) {
                    errors.push('Името не може да започва или завършва с точка');
                }

                // Проверка за интервали в началото или края
                if (name !== name.trim()) {
                    errors.push('Името не може да започва или завършва с интервал');
                }

                return errors;
            },

            /**
             * Създава нова папка
             */
            createFolder: function() {
                const folderNameInput = document.getElementById('folder-name-input');
                const errorDiv = document.getElementById('folder-name-error');
                const confirmBtn = document.getElementById('confirm-create-folder');

                if (!folderNameInput || !errorDiv || !confirmBtn) {
                    return;
                }

                const folderName = folderNameInput.value.trim();

                // Валидация
                const validationErrors = this.validateFolderName(folderName);
                if (validationErrors.length > 0) {
                    errorDiv.textContent = validationErrors[0];
                    errorDiv.classList.remove('hidden');
                    return;
                }

                // Деактивираме бутона по време на заявката
                confirmBtn.disabled = true;
                confirmBtn.textContent = 'Създаване...';

                // AJAX заявка за създаване на папката
                const formData = new FormData();
                formData.append('folder_name', folderName);
                formData.append('current_directory', this.imageManager.currentDirectory || '');

                fetch(this.imageManager.createFolderUrl, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Успешно създаване - затваряме диалога и обновяваме списъка
                        this.hideCreateFolderDialog();
                        this.refreshManager();

                        // Показваме съобщение за успех
                        this.showAlert('success', 'Папката беше създадена успешно');
                    } else {
                        // Грешка от сървъра
                        errorDiv.textContent = data.error || 'Грешка при създаване на папката';
                        errorDiv.classList.remove('hidden');
                    }
                })
                .catch(error => {
                    console.error('Грешка при създаване на папка:', error);
                    errorDiv.textContent = 'Мрежова грешка при създаване на папката';
                    errorDiv.classList.remove('hidden');
                })
                .finally(() => {
                    // Възстановяваме бутона
                    confirmBtn.disabled = false;
                    confirmBtn.textContent = 'Създай';
                });
            }
        });
    }
})();
