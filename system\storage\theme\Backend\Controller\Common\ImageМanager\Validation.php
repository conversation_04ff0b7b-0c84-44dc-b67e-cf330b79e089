<?php

namespace Theme25\Backend\Controller\Common\ImageManager;

class Validation extends \Theme25\ControllerSubMethods {

    /**
     * Получава целевата директория от заявката
     * 
     * @return string Път до директорията
     */
    public function getTargetDirectory() {
        $requestDirectory = ltrim(urldecode($this->requestGet('directory', '')), '/');

        if ($requestDirectory) {
            return rtrim( ThemeData()->getImageServerPath() . $requestDirectory, '/');
        }

        return ThemeData()->getImageServerPath();
    }
    
    /**
     * Проверява дали директорията е валидна и в рамките на image server папката
     *
     * @param string $directory Път до директорията
     * @return bool
     */
    public function isValidDirectory($directory) {
        if (!is_dir($directory)) {
            return false;
        }

        $realPath = realpath($directory);
        if ($realPath === false) {
            // Ако realpath() не работи, опитваме се с директна проверка
            $realPath = $directory;
        }

        $realPath = str_replace('\\', '/', $realPath);
        $serverPath = str_replace('\\', '/', ThemeData()->getImageServerPath());

        // Премахваме trailing slash за правилно сравнение
        $realPath = trim($realPath, '/');
        $serverPath = trim($serverPath, '/');

        return substr($realPath, 0, strlen($serverPath)) === $serverPath;
    }
    
    /**
     * Проверява права на достъп за операции с файлове
     * 
     * @return array Резултат от проверката
     */
    public function validatePermissions() {
        // if (!$this->hasPermission('modify', 'common/filemanager')) {
        //     return [
        //         'valid' => false,
        //         'error' => $this->getLanguageText('error_permission')
        //     ];
        // }
        
        return ['valid' => true];
    }
    
    /**
     * Валидира качен файл
     * 
     * @param array $file Данни за файла
     * @return array Резултат от валидацията
     */
    public function validateUploadedFile($file) {
        $filename = basename(html_entity_decode($file['name'], ENT_QUOTES, 'UTF-8'));
        
        // Проверка на дължината на името
        if ((utf8_strlen($filename) < 3) || (utf8_strlen($filename) > 255)) {
            return [
                'valid' => false,
                'error' => 'Невалидно име на файл: ' . $filename
            ];
        }
        
        // Проверка на разширението
        $allowedExtensions = ['jpg', 'jpeg', 'gif', 'png', 'webp', 'svg'];
        $extension = utf8_strtolower(utf8_substr(strrchr($filename, '.'), 1));

        if (!in_array($extension, $allowedExtensions)) {
            return [
                'valid' => false,
                'error' => 'Неподдържан формат на файл: ' . $filename
            ];
        }

        // Проверка на MIME типа
        $allowedMimes = ['image/jpeg', 'image/pjpeg', 'image/png', 'image/x-png', 'image/gif', 'image/webp', 'image/svg+xml'];
        
        if (!in_array($file['type'], $allowedMimes)) {
            return [
                'valid' => false,
                'error' => 'Невалиден MIME тип: ' . $filename
            ];
        }

        // Специална валидация за SVG файлове
        if ($extension === 'svg') {
            return $this->validateSvgFile($file, $filename);
        }

        // Проверка на размера (5MB лимит)
        if ($file['size'] > 5 * 1024 * 1024) {
            return [
                'valid' => false,
                'error' => 'Файлът е твърде голям: ' . $filename
            ];
        }

        return ['valid' => true];
    }
    
    /**
     * Валидира директория и права на достъп
     * 
     * @return array Резултат от валидацията
     */
    public function validateDirectoryAccess() {
        // Проверка за права на достъп
        $permissionCheck = $this->validatePermissions();
        if (!$permissionCheck['valid']) {
            return $permissionCheck;
        }
        
        // Получаване на директорията
        $directory = $this->getTargetDirectory();

        // Проверка за валидност на директорията
        if (!$this->isValidDirectory($directory)) {
            return [
                'valid' => false,
                'error' => 'Невалидна директория',
                'directory' => null
            ];
        }
        
        return [
            'valid' => true,
            'directory' => $directory
        ];
    }
    
    /**
     * Валидира множество файлове за качване
     * 
     * @param array $files Масив с файлове
     * @return array Резултат от валидацията
     */
    public function validateMultipleFiles($files) {
        $validFiles = [];
        $errors = [];
        
        if (empty($files)) {
            return [
                'valid' => false,
                'error' => 'Няма избрани файлове за качване',
                'validFiles' => [],
                'errors' => []
            ];
        }
        
        // Обработка на множество файлове
        if (is_array($files['name'])) {
            for ($i = 0; $i < count($files['name']); $i++) {
                if ($files['error'][$i] == UPLOAD_ERR_OK) {
                    $fileData = [
                        'name' => $files['name'][$i],
                        'type' => $files['type'][$i],
                        'tmp_name' => $files['tmp_name'][$i],
                        'error' => $files['error'][$i],
                        'size' => $files['size'][$i]
                    ];
                    
                    $validation = $this->validateUploadedFile($fileData);
                    if ($validation['valid']) {
                        $validFiles[] = $fileData;
                    } else {
                        $errors[] = $validation['error'];
                    }
                }
            }
        } else {
            // Обработка на единичен файл
            if ($files['error'] == UPLOAD_ERR_OK) {
                $validation = $this->validateUploadedFile($files);
                if ($validation['valid']) {
                    $validFiles[] = $files;
                } else {
                    $errors[] = $validation['error'];
                }
            }
        }
        
        return [
            'valid' => !empty($validFiles),
            'validFiles' => $validFiles,
            'errors' => $errors,
            'totalFiles' => count($validFiles),
            'errorCount' => count($errors)
        ];
    }

    /**
     * Валидира името на папка
     *
     * @param string $folderName Името на папката
     * @return array Резултат от валидацията
     */
    public function validateFolderName($folderName) {
        // Проверка за празно име
        if (empty($folderName) || trim($folderName) === '') {
            return [
                'valid' => false,
                'error' => 'Името на папката не може да бъде празно'
            ];
        }

        $folderName = trim($folderName);

        // Проверка за дължина
        if (strlen($folderName) > 255) {
            return [
                'valid' => false,
                'error' => 'Името на папката е твърде дълго (максимум 255 символа)'
            ];
        }

        // Забранени символи за Windows/Linux файлови системи
        // if (preg_match('#[<>:"/\\\\|?*\x00-\x1f]#', $folderName)) {
        //     return [
        //         'valid' => false,
        //         'error' => 'Името съдържа забранени символи: < > : " / \\ | ? *'
        //     ];
        // }

        $folderName = preg_replace('#[<>:"/\\\\|?*\x00-\x1f]#', '_', $folderName);


        // Забранени имена (Windows reserved names)
        $reservedNames = ['CON', 'PRN', 'AUX', 'NUL', 'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9', 'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'];
        if (in_array(strtoupper($folderName), $reservedNames)) {
            return [
                'valid' => false,
                'error' => 'Това име е запазено от системата'
            ];
        }

        // Проверка за точки в началото или края
        if (substr($folderName, 0, 1) === '.' || substr($folderName, -1) === '.') {
            return [
                'valid' => false,
                'error' => 'Името не може да започва или завършва с точка'
            ];
        }

        // Проверка за интервали в началото или края
        if ($folderName !== trim($folderName)) {
            return [
                'valid' => false,
                'error' => 'Името не може да започва или завършва с интервал'
            ];
        }

        return ['valid' => true];
    }

    /**
     * Специална валидация за SVG файлове
     *
     * @param array $file Данни за файла
     * @param string $filename Име на файла
     * @return array Резултат от валидацията
     */
    private function validateSvgFile($file, $filename) {
        // Проверка на размера (2MB лимит за SVG файлове)
        if ($file['size'] > 2 * 1024 * 1024) {
            return [
                'valid' => false,
                'error' => 'SVG файлът е твърде голям (максимум 2MB): ' . $filename
            ];
        }

        // Проверка на съдържанието на SVG файла за безопасност
        $svgContent = file_get_contents($file['tmp_name']);
        if ($svgContent === false) {
            return [
                'valid' => false,
                'error' => 'Не може да се прочете SVG файлът: ' . $filename
            ];
        }

        // Основна проверка дали файлът съдържа SVG елементи
        if (strpos($svgContent, '<svg') === false) {
            return [
                'valid' => false,
                'error' => 'Файлът не съдържа валидно SVG съдържание: ' . $filename
            ];
        }

        // Проверка за потенциално опасни елементи в SVG
        $dangerousElements = ['script', 'object', 'embed', 'iframe', 'link'];
        foreach ($dangerousElements as $element) {
            if (stripos($svgContent, '<' . $element) !== false) {
                return [
                    'valid' => false,
                    'error' => 'SVG файлът съдържа потенциално опасни елементи: ' . $filename
                ];
            }
        }

        return ['valid' => true];
    }

}
