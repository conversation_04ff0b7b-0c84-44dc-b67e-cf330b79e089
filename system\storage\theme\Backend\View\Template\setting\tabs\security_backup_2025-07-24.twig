<div class="mb-6">
    <h1 class="text-2xl font-bold text-gray-800">Настройки за сигурност</h1>
    <p class="text-gray-600 mt-1">Управлявайте сигурността на вашия административен панел</p>
</div>

<form id="security-settings-form" class="space-y-6">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Security Settings -->
        <div class="lg:col-span-2">
            <!-- IP Restrictions -->
            <div class="bg-white rounded shadow p-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="ri-shield-line mr-2"></i>
                    IP ограничения
                </h2>
                
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <label for="ip_restriction_enabled" class="text-sm font-medium text-gray-700">
                                Активиране на IP ограничения
                            </label>
                            <p class="text-xs text-gray-500">Разрешава достъп само от определени IP адреси</p>
                        </div>
                        <input type="checkbox" 
                               id="ip_restriction_enabled" 
                               name="ip_restriction_enabled"
                               class="toggle-switch"
                               {{ ip_restriction_enabled ? 'checked' : '' }}>
                    </div>

                    <div id="ip-restriction-fields" class="{{ ip_restriction_enabled ? '' : 'hidden' }}">
                        <div>
                            <label for="allowed_ips_text" class="block text-sm font-medium text-gray-700 mb-1">
                                Разрешени IP адреси
                            </label>
                            <textarea id="allowed_ips_text" 
                                      name="allowed_ips_text"
                                      rows="5" 
                                      class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
                                      placeholder="***********&#10;********&#10;***********">{{ allowed_ips_text ?? '' }}</textarea>
                            <p class="text-xs text-gray-500 mt-1">
                                Въведете по един IP адрес на ред. Вашият текущ IP: <strong>{{ current_ip }}</strong>
                                {% if not is_current_ip_allowed %}
                                    <span class="text-red-500 font-medium">⚠ Не е в списъка!</span>
                                {% endif %}
                            </p>
                        </div>

                        <div class="flex space-x-2">
                            <button type="button" 
                                    id="test-ip-restriction"
                                    class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors">
                                <i class="ri-shield-check-line mr-2"></i>
                                Тест IP
                            </button>
                            <button type="button" 
                                    onclick="document.getElementById('allowed_ips_text').value += '{{ current_ip }}\n'"
                                    class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors">
                                <i class="ri-add-line mr-2"></i>
                                Добави текущия IP
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Session Security -->
            <div class="bg-white rounded shadow p-6 mt-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="ri-time-line mr-2"></i>
                    Сигурност на сесиите
                </h2>
                
                <div class="space-y-4">
                    <div>
                        <label for="session_timeout" class="block text-sm font-medium text-gray-700 mb-1">
                            Timeout на сесия
                        </label>
                        <select id="session_timeout" 
                                name="session_timeout"
                                class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary">
                            {% for value, label in session_timeout_options %}
                            <option value="{{ value }}" {{ session_timeout == value ? 'selected' : '' }}>
                                {{ label }}
                            </option>
                            {% endfor %}
                        </select>
                        <p class="text-xs text-gray-500 mt-1">Време след което сесията изтича при неактивност</p>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="force_ssl" class="text-sm font-medium text-gray-700">
                                Принудително SSL (HTTPS)
                            </label>
                            <p class="text-xs text-gray-500">Пренасочва всички HTTP заявки към HTTPS</p>
                        </div>
                        <input type="checkbox" 
                               id="force_ssl" 
                               name="force_ssl"
                               class="toggle-switch"
                               {{ force_ssl ? 'checked' : '' }}>
                    </div>
                </div>
            </div>

            <!-- Two-Factor Authentication -->
            <div class="bg-white rounded shadow p-6 mt-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="ri-smartphone-line mr-2"></i>
                    Двуфакторна автентикация
                </h2>
                
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <label for="two_factor_enabled" class="text-sm font-medium text-gray-700">
                                Активиране на 2FA
                            </label>
                            <p class="text-xs text-gray-500">Изисква допълнителен код от мобилно приложение</p>
                        </div>
                        <input type="checkbox" 
                               id="two_factor_enabled" 
                               name="two_factor_enabled"
                               class="toggle-switch"
                               {{ two_factor_enabled ? 'checked' : '' }}>
                    </div>

                    {% if two_factor_enabled %}
                    <div class="bg-blue-50 border border-blue-200 rounded p-4">
                        <div class="flex items-center">
                            <i class="ri-information-line text-blue-500 mr-2"></i>
                            <div>
                                <p class="text-sm text-blue-800 font-medium">Двуфакторната автентикация е активна</p>
                                <p class="text-xs text-blue-600">Използвайте приложение като Google Authenticator или Authy</p>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Password Policy -->
            <div class="bg-white rounded shadow p-6 mt-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="ri-lock-password-line mr-2"></i>
                    Политика за пароли
                </h2>
                
                <div class="space-y-4">
                    <div>
                        <label for="password_min_length" class="block text-sm font-medium text-gray-700 mb-1">
                            Минимална дължина на паролата
                        </label>
                        <select id="password_min_length" 
                                name="password_min_length"
                                class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary">
                            {% for value, label in password_length_options %}
                            <option value="{{ value }}" {{ password_min_length == value ? 'selected' : '' }}>
                                {{ label }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="grid grid-cols-2 gap-4">
                        <div class="flex items-center justify-between">
                            <label for="password_require_uppercase" class="text-sm font-medium text-gray-700">
                                Главни букви
                            </label>
                            <input type="checkbox" 
                                   id="password_require_uppercase" 
                                   name="password_require_uppercase"
                                   class="toggle-switch"
                                   {{ password_require_uppercase ? 'checked' : '' }}>
                        </div>

                        <div class="flex items-center justify-between">
                            <label for="password_require_lowercase" class="text-sm font-medium text-gray-700">
                                Малки букви
                            </label>
                            <input type="checkbox" 
                                   id="password_require_lowercase" 
                                   name="password_require_lowercase"
                                   class="toggle-switch"
                                   {{ password_require_lowercase ? 'checked' : '' }}>
                        </div>

                        <div class="flex items-center justify-between">
                            <label for="password_require_numbers" class="text-sm font-medium text-gray-700">
                                Цифри
                            </label>
                            <input type="checkbox" 
                                   id="password_require_numbers" 
                                   name="password_require_numbers"
                                   class="toggle-switch"
                                   {{ password_require_numbers ? 'checked' : '' }}>
                        </div>

                        <div class="flex items-center justify-between">
                            <label for="password_require_symbols" class="text-sm font-medium text-gray-700">
                                Специални символи
                            </label>
                            <input type="checkbox" 
                                   id="password_require_symbols" 
                                   name="password_require_symbols"
                                   class="toggle-switch"
                                   {{ password_require_symbols ? 'checked' : '' }}>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Login Security -->
            <div class="bg-white rounded shadow p-6 mt-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="ri-login-box-line mr-2"></i>
                    Сигурност при вход
                </h2>
                
                <div class="space-y-4">
                    <div>
                        <label for="login_attempts_limit" class="block text-sm font-medium text-gray-700 mb-1">
                            Лимит за опити за вход
                        </label>
                        <select id="login_attempts_limit" 
                                name="login_attempts_limit"
                                class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary">
                            {% for value, label in login_attempts_options %}
                            <option value="{{ value }}" {{ login_attempts_limit == value ? 'selected' : '' }}>
                                {{ label }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>

                    <div>
                        <label for="login_lockout_duration" class="block text-sm font-medium text-gray-700 mb-1">
                            Продължителност на блокиране
                        </label>
                        <select id="login_lockout_duration" 
                                name="login_lockout_duration"
                                class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary">
                            {% for value, label in lockout_duration_options %}
                            <option value="{{ value }}" {{ login_lockout_duration == value ? 'selected' : '' }}>
                                {{ label }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
            </div>

            <!-- Logging -->
            <div class="bg-white rounded shadow p-6 mt-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="ri-file-list-line mr-2"></i>
                    Логиране
                </h2>
                
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <label for="admin_activity_log" class="text-sm font-medium text-gray-700">
                                Лог на активността на администраторите
                            </label>
                            <p class="text-xs text-gray-500">Записва всички действия на администраторите</p>
                        </div>
                        <input type="checkbox" 
                               id="admin_activity_log" 
                               name="admin_activity_log"
                               class="toggle-switch"
                               {{ admin_activity_log ? 'checked' : '' }}>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="failed_login_log" class="text-sm font-medium text-gray-700">
                                Лог на неуспешни опити за вход
                            </label>
                            <p class="text-xs text-gray-500">Записва всички неуспешни опити за вход</p>
                        </div>
                        <input type="checkbox" 
                               id="failed_login_log" 
                               name="failed_login_log"
                               class="toggle-switch"
                               {{ failed_login_log ? 'checked' : '' }}>
                    </div>
                </div>
            </div>
        </div>

        <!-- Security Panel -->
        <div class="lg:col-span-1">
            <!-- Security Score -->
            <div class="bg-white rounded shadow p-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="ri-shield-check-line mr-2"></i>
                    Оценка за сигурност
                </h2>
                
                <div class="text-center">
                    <div class="relative inline-flex items-center justify-center w-24 h-24">
                        <svg class="w-24 h-24 transform -rotate-90">
                            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="transparent" class="text-gray-200" transform="translate(36, 36)"></circle>
                            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="transparent" 
                                    stroke-dasharray="62.83" stroke-dashoffset="{{ 62.83 - (62.83 * (security_score ?? 50) / 100) }}"
                                    class="{{ (security_score ?? 50) >= 80 ? 'text-green-500' : ((security_score ?? 50) >= 60 ? 'text-yellow-500' : 'text-red-500') }}"
                                    transform="translate(36, 36)"></circle>
                        </svg>
                        <span class="absolute text-xl font-bold {{ (security_score ?? 50) >= 80 ? 'text-green-500' : ((security_score ?? 50) >= 60 ? 'text-yellow-500' : 'text-red-500') }}">
                            {{ security_score ?? 50 }}%
                        </span>
                    </div>
                    <p class="text-sm text-gray-600 mt-2">
                        {% if (security_score ?? 50) >= 80 %}
                            Отлична сигурност
                        {% elseif (security_score ?? 50) >= 60 %}
                            Добра сигурност
                        {% else %}
                            Нужни подобрения
                        {% endif %}
                    </p>
                </div>
            </div>

            <!-- Security Statistics -->
            <div class="bg-white rounded shadow p-6 mt-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="ri-bar-chart-line mr-2"></i>
                    Статистики
                </h2>
                
                <div class="space-y-3">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Неуспешни опити за вход:</span>
                        <span id="failed-logins-count" class="text-sm font-medium text-red-600">
                            {{ failed_login_attempts ?? 0 }}
                        </span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Активни сесии:</span>
                        <span class="text-sm font-medium text-green-600">
                            {{ active_sessions ?? 1 }}
                        </span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Блокирани IP адреси:</span>
                        <span class="text-sm font-medium text-orange-600">
                            {{ blocked_ips ?? 0 }}
                        </span>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="bg-white rounded shadow p-6 mt-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="ri-tools-line mr-2"></i>
                    Действия
                </h2>
                
                <div class="space-y-3">
                    <button type="submit" 
                            id="save-security-settings"
                            class="w-full px-4 py-2 bg-primary text-white rounded hover:bg-primary/90 transition-colors">
                        <i class="ri-save-line mr-2"></i>
                        Запази настройки
                    </button>

                    <button type="button" 
                            id="clear-failed-logins"
                            class="w-full px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors">
                        <i class="ri-delete-bin-line mr-2"></i>
                        Изчисти неуспешни опити
                    </button>

                    <button type="button" 
                            onclick="location.reload()"
                            class="w-full px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors">
                        <i class="ri-refresh-line mr-2"></i>
                        Презареди
                    </button>
                </div>
            </div>
        </div>
    </div>
</form>
