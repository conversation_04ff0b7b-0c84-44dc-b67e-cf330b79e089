/**
 * JavaScript модул за настройки
 * Следва BackendModule pattern на темата Rakla.bg
 */
(function() {
    'use strict';

    // Функция за инициализация на settings модула
    function initSettingsModule() {
        if (typeof BackendModule !== 'undefined' && typeof BackendModule === 'object') {
            // Добавяне на функционалност към основния модул
            extendBackendModule();
            // Инициализация на settings
            BackendModule.initSettings();
        } else {
            console.error('BackendModule not found');
        }
    }

    // Разширяване на основния модул
    function extendBackendModule() {
        Object.assign(BackendModule, {
            // Конфигурация за настройки
            settings: {
                currentTab: 'basic',
                modules: {},
                autoSaveTimeout: null,
                config: {
                    userToken: new URLSearchParams(window.location.search).get('user_token') || '',
                    baseUrl: '',
                    ajaxUrls: {}
                }
            },

            /**
             * Инициализация на модула за настройки
             */
            initSettings: function() {
                this.loadSettingsConfig();
                this.bindSettingsEvents();
                this.initSettingsComponents();
                this.logDev && this.logDev('Settings модул инициализиран');
            },

            /**
             * Зареждане на конфигурация за настройки
             */
            loadSettingsConfig: function() {
                // Зареждане на конфигурация от глобални променливи
                if (window.settingsConfig) {
                    this.settings.config = { ...this.settings.config, ...window.settingsConfig };
                }

                // Извличане на user_token от URL
                const urlParams = new URLSearchParams(window.location.search);
                if (urlParams.has('user_token')) {
                    this.settings.config.userToken = urlParams.get('user_token');
                }

                // Определяне на текущия таб
                this.settings.currentTab = urlParams.get('tab') || 'basic';
            },

            /**
             * Свързване на събития за настройки
             */
            bindSettingsEvents: function() {
                // Tab навигация
                this.bindSettingsTabEvents();

                // Форми за настройки
                this.bindSettingsFormEvents();

                // Известия
                this.initSettingsNotifications();
            },

            /**
             * Свързване на събития за табове
             */
            bindSettingsTabEvents: function() {
                const self = this;
                const tabButtons = document.querySelectorAll('.tab-button');
                const tabContents = document.querySelectorAll('.tab-content');

                this.logDev && this.logDev('bindSettingsTabEvents called');
                this.logDev && this.logDev('Found tab buttons: ' + tabButtons.length);
                this.logDev && this.logDev('Found tab contents: ' + tabContents.length);

                // Initialize tab cache and form data storage
                if (!this.settings.tabCache) {
                    this.settings.tabCache = new Map();
                }
                if (!this.settings.tabFormData) {
                    this.settings.tabFormData = new Map();
                }

                tabButtons.forEach((button, index) => {
                    self.logDev && self.logDev('Adding event listener to button ' + index + ': ' + button.id);
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        const tabId = this.getAttribute('data-tab') || this.id.replace('tab-', '');
                        self.logDev && self.logDev('Tab button clicked: ' + tabId);

                        // Don't reload if clicking on active tab
                        if (this.classList.contains('active')) {
                            self.logDev && self.logDev('Tab already active, skipping: ' + tabId);
                            return;
                        }

                        self.switchSettingsTab(tabId);
                    });
                });

                // Auto-save functionality to prevent data loss
                if (!this.settings.autoSaveTimeout) {
                    document.addEventListener('input', function(e) {
                        if (e.target.closest('.tab-content')) {
                            clearTimeout(self.settings.autoSaveTimeout);
                            self.settings.autoSaveTimeout = setTimeout(() => {
                                self.saveCurrentTabFormData();
                            }, 1000); // Save after 1 second of inactivity
                        }
                    });
                }

                // Initialize the current tab
                const currentTab = this.settings.config.currentTab || this.settings.currentTab || 'basic';
                this.logDev && this.logDev('Initializing tab: ' + currentTab);
                this.switchSettingsTab(currentTab);
            },

            /**
             * Превключване на таб в настройките
             */
            switchSettingsTab: function(tabId) {
                this.logDev && this.logDev('Switching to tab: ' + tabId);

                // Save current form data before switching
                this.saveCurrentTabFormData();

                // Update current tab
                this.settings.currentTab = tabId;

                // Update visual state of tab buttons
                const tabButtons = document.querySelectorAll('.tab-button');
                this.logDev && this.logDev('Found tab buttons: ' + tabButtons.length);

                tabButtons.forEach(btn => {
                    const btnTabId = btn.getAttribute('data-tab') || btn.id.replace('tab-', '');
                    if (btnTabId === tabId) {
                        btn.classList.add('active', 'text-primary');
                        btn.classList.remove('text-gray-600');
                        this.logDev && this.logDev('Activated tab button: ' + btnTabId);
                    } else {
                        btn.classList.remove('active', 'text-primary');
                        btn.classList.add('text-gray-600');
                    }
                });

                // Hide all tab contents
                const tabContents = document.querySelectorAll('.tab-content');
                this.logDev && this.logDev('Found tab contents: ' + tabContents.length);

                tabContents.forEach(content => {
                    content.classList.add('hidden');
                });

                // Show target tab content
                const targetContent = document.getElementById('content-' + tabId);
                if (targetContent) {
                    targetContent.classList.remove('hidden');
                    this.logDev && this.logDev('Showed content for tab: ' + tabId);

                    // Load content via AJAX if needed
                    this.loadTabContent(tabId, targetContent);
                } else {
                    this.logDev && this.logDev('Content element not found for tab: ' + tabId);
                }

                // Load module for current tab
                this.loadSettingsModule(tabId);

                // Update URL without reload
                this.updateSettingsUrl(tabId);

                this.logDev && this.logDev('Settings tab switched to: ' + tabId);
            },

            /**
             * Зареждане на модул за конкретен таб
             */
            loadSettingsModule: function(tabName) {
                // Проверка дали модулът вече е зареден
                if (this.settings.modules[tabName]) {
                    // Ако модулът има метод за активиране, го извикваме
                    if (typeof this.settings.modules[tabName].activate === 'function') {
                        this.settings.modules[tabName].activate();
                    }
                    return;
                }

                // Опит за намиране на модула в BackendModule
                const moduleMethodName = 'init' + tabName.charAt(0).toUpperCase() + tabName.slice(1) + 'Settings';

                if (typeof this[moduleMethodName] === 'function') {
                    this.settings.modules[tabName] = { init: this[moduleMethodName].bind(this) };
                    this.settings.modules[tabName].init();
                    this.logDev && this.logDev('Settings module loaded: ' + tabName);
                } else {
                    this.logDev && this.logDev('Settings module not found: ' + moduleMethodName);
                }
            },

            /**
             * Запазване на данни от текущия таб
             */
            saveCurrentTabFormData: function() {
                const activeTab = document.querySelector('.tab-button.active');
                if (activeTab) {
                    const tabId = activeTab.getAttribute('data-tab');
                    const activeContent = document.getElementById('content-' + tabId);
                    if (activeContent) {
                        const forms = activeContent.querySelectorAll('form');
                        forms.forEach(form => {
                            const formData = new FormData(form);
                            const formDataObj = {};
                            for (let [key, value] of formData.entries()) {
                                formDataObj[key] = value;
                            }
                            this.settings.tabFormData.set(tabId, formDataObj);
                        });
                    }
                }
            },

            /**
             * Възстановяване на данни в таб
             */
            restoreTabFormData: function(tabId, content) {
                const savedData = this.settings.tabFormData.get(tabId);
                if (savedData) {
                    const forms = content.querySelectorAll('form');
                    forms.forEach(form => {
                        Object.keys(savedData).forEach(key => {
                            const field = form.querySelector(`[name="${key}"]`);
                            if (field) {
                                if (field.type === 'checkbox' || field.type === 'radio') {
                                    field.checked = savedData[key] === field.value;
                                } else if (field.type === 'file') {
                                    // File input полета не могат да бъдат задавани програмно по security причини
                                    // Пропускаме ги при възстановяване на данни
                                    this.logDev && this.logDev('Skipping file input field: ' + key);
                                } else {
                                    field.value = savedData[key];
                                }
                            }
                        });
                    });
                }
            },

            /**
             * Зареждане на съдържание на таб чрез AJAX
             */
            loadTabContent: function(tabId, contentElement) {
                // Check if content is already loaded and cached
                if (this.settings.tabCache.has(tabId)) {
                    contentElement.innerHTML = this.settings.tabCache.get(tabId);
                    this.restoreTabFormData(tabId, contentElement);
                    this.initializeTabSpecificFunctionality(tabId);
                    return;
                }

                // Check if content is already loaded (not placeholder)
                const placeholder = contentElement.querySelector('.text-center.py-8');
                if (!placeholder) {
                    // Content is already loaded, cache it
                    this.settings.tabCache.set(tabId, contentElement.innerHTML);
                    this.restoreTabFormData(tabId, contentElement);
                    this.initializeTabSpecificFunctionality(tabId);
                    return;
                }

                // Show loading state
                contentElement.innerHTML = `
                    <div class="bg-white rounded shadow p-6">
                        <div class="text-center py-8">
                            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">Зареждане...</h3>
                            <p class="text-gray-500">Моля, изчакайте</p>
                        </div>
                    </div>
                `;

                // Load content via AJAX
                const url = this.settings.config.baseUrl + '&tab=' + tabId;

                fetch(url, {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.text())
                .then(html => {
                    // Extract the tab content from the response
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(html, 'text/html');
                    const newContent = doc.getElementById('content-' + tabId);

                    if (newContent) {
                        contentElement.innerHTML = newContent.innerHTML;
                        // Cache the loaded content
                        this.settings.tabCache.set(tabId, newContent.innerHTML);
                        this.restoreTabFormData(tabId, contentElement);
                        this.initializeTabSpecificFunctionality(tabId);
                    } else {
                        // Fallback if content not found
                        this.showTabError(contentElement, 'Грешка при зареждане', 'Не можа да се зареди съдържанието');
                    }
                })
                .catch(error => {
                    console.error('Error loading tab content:', error);
                    this.showTabError(contentElement, 'Грешка в мрежата', 'Проверете интернет връзката');
                });
            },

            /**
             * Показване на грешка в таб
             */
            showTabError: function(contentElement, title, message) {
                contentElement.innerHTML = `
                    <div class="bg-white rounded shadow p-6">
                        <div class="text-center py-8">
                            <i class="ri-error-warning-line text-4xl text-red-400 mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">${title}</h3>
                            <p class="text-gray-500">${message}</p>
                            <button onclick="location.reload()" class="mt-4 px-4 py-2 bg-primary text-white rounded hover:bg-primary/90">
                                Опитай отново
                            </button>
                        </div>
                    </div>
                `;
            },

            /**
             * Инициализиране на специфична функционалност за таб
             */
            initializeTabSpecificFunctionality: function(tabId) {
                // Use BackendModule's loadSettingsModule method if available
                if (typeof this.loadSettingsModule === 'function') {
                    this.loadSettingsModule(tabId);
                } else {
                    // Fallback to direct method calls
                    const methodName = 'init' + tabId.charAt(0).toUpperCase() + tabId.slice(1).replace('_', '') + 'Settings';
                    if (typeof this[methodName] === 'function') {
                        this[methodName]();
                    }
                }
            },

            /**
             * Актуализиране на URL за настройки
             */
            updateSettingsUrl: function(tabId) {
                const url = new URL(window.location);
                url.searchParams.set('tab', tabId);
                window.history.replaceState({}, '', url);
            },

            /**
             * Инициализиране на компоненти за настройки
             */
            initSettingsComponents: function() {
                // Инициализиране на системата за известия
                this.initSettingsNotifications();

                // Инициализиране на общи форми
                this.initSettingsForms();
            },

            /**
             * Инициализиране на известия за настройки
             */
            initSettingsNotifications: function() {
                // Използваме вградената система за известия от BackendModule
                // Проверяваме дали showAlert методът е наличен
                if (typeof this.showAlert !== 'function') {
                    this.logDev && this.logDev('showAlert method not found in BackendModule', 'warning');
                }
            },

            /**
             * Показване на нотификация за настройки
             * Използва BackendModule.showAlert метода
             */
            showSettingsNotification: function(message, type = 'info', duration = 3) {
                if (typeof this.showAlert === 'function') {
                    this.showAlert(type, message, duration);
                } else {
                    // Fallback към browser alert ако showAlert не е наличен
                    alert(message);
                    this.logDev && this.logDev('Fallback to browser alert: ' + message, 'warning');
                }
            },

            /**
             * Показване на потвърждение за настройки
             * Използва browser confirm за простота
             */
            showSettingsConfirm: function(message, callback) {
                if (confirm(message)) {
                    if (typeof callback === 'function') {
                        callback();
                    }
                }
            },

            /**
             * Инициализиране на форми за настройки
             */
            initSettingsForms: function() {
                // Общи event listeners за всички форми в настройките
                const forms = document.querySelectorAll('form[id*="settings"]');

                forms.forEach(form => {
                    // Предотвратяване на стандартното submit поведение
                    form.addEventListener('submit', function(e) {
                        e.preventDefault();
                    });
                });
            },

            /**
             * Свързване на събития за форми
             */
            bindSettingsFormEvents: function() {
                // Автоматично запазване при промяна на checkbox-и
                const checkboxes = document.querySelectorAll('input[type="checkbox"][name*="setting"]');

                checkboxes.forEach(checkbox => {
                    checkbox.addEventListener('change', () => {
                        this.autoSaveSettings();
                    });
                });
            },

            /**
             * Автоматично запазване на настройки
             */
            autoSaveSettings: function() {
                // Debounce за автоматично запазване
                clearTimeout(this.settings.autoSaveTimeout);
                this.settings.autoSaveTimeout = setTimeout(() => {
                    this.logDev && this.logDev('Auto-saving settings...');
                    // Тук може да се добави логика за автоматично запазване
                }, 1000);
            },

            /**
             * Инициализиране на основни настройки
             */
            initBasicSettings: function() {
                this.bindBasicSettingsEvents();
                this.logDev && this.logDev('Basic settings module initialized');
            },

            /**
             * Свързване на събития за основни настройки
             */
            bindBasicSettingsEvents: function() {
                const self = this;

                // Форма за основни настройки
                const basicForm = document.getElementById('basic-settings-form');
                if (basicForm) {
                    basicForm.addEventListener('submit', function(e) {
                        e.preventDefault();
                        self.saveBasicSettings();
                    });
                }

                // Бутон за запазване
                const saveButton = document.getElementById('save-basic-settings');
                if (saveButton) {
                    saveButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.saveBasicSettings();
                    });
                }

                // Качване на лого
                const logoUpload = document.getElementById('logo-upload');
                if (logoUpload) {
                    logoUpload.addEventListener('change', function(e) {
                        self.handleLogoUpload(e);
                    });
                }

                // Тест на имейл
                const testEmailButton = document.getElementById('test-email');
                if (testEmailButton) {
                    testEmailButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.testEmail();
                    });
                }
            },

            /**
             * Запазване на основни настройки
             */
            saveBasicSettings: function() {
                const self = this;
                const form = document.getElementById('basic-settings-form');

                if (!form) {
                    self.showSettingsNotification('Формата не е намерена', 'error');
                    return;
                }

                const formData = new FormData(form);
                formData.append('user_token', self.settings.config.userToken);

                // Показване на loading състояние
                const saveButton = document.getElementById('save-basic-settings');
                if (saveButton) {
                    saveButton.disabled = true;
                    saveButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Запазване...';
                }

                fetch(self.settings.config.ajaxUrls.basic_save || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');

                        // Актуализиране на данните ако са предоставени
                        if (data.updated_data) {
                            self.updateBasicSettingsDisplay(data.updated_data);
                        }
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');

                        // Показване на грешки при валидация
                        if (data.validation_errors) {
                            self.showValidationErrors(data.validation_errors);
                        }
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error saving basic settings:', error);
                    self.showSettingsNotification('Възникна грешка при запазването', 'error');
                })
                .finally(() => {
                    // Възстановяване на бутона
                    if (saveButton) {
                        saveButton.disabled = false;
                        saveButton.innerHTML = '<i class="ri-save-line mr-2"></i>Запази';
                    }
                });
            },

            /**
             * Показване на грешки при валидация
             */
            showValidationErrors: function(errors) {
                // Изчистване на предишни грешки
                const errorElements = document.querySelectorAll('.field-error');
                errorElements.forEach(el => el.remove());

                // Показване на нови грешки
                Object.keys(errors).forEach(fieldName => {
                    const field = document.querySelector(`[name="${fieldName}"]`);
                    if (field) {
                        const errorDiv = document.createElement('div');
                        errorDiv.className = 'field-error text-red-500 text-sm mt-1';
                        errorDiv.textContent = errors[fieldName];
                        field.parentNode.appendChild(errorDiv);

                        // Добавяне на червена граница
                        field.classList.add('border-red-500');

                        // Премахване на грешката при промяна
                        field.addEventListener('input', function() {
                            errorDiv.remove();
                            field.classList.remove('border-red-500');
                        }, { once: true });
                    }
                });
            },

            /**
             * Тестване на имейл настройки
             */
            testEmail: function() {
                const self = this;
                const testButton = document.getElementById('test-email');

                if (testButton) {
                    testButton.disabled = true;
                    testButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Тестване...';
                }

                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);

                fetch(self.settings.config.ajaxUrls.test_email || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error testing email:', error);
                    self.showSettingsNotification('Възникна грешка при тестването на имейла', 'error');
                })
                .finally(() => {
                    if (testButton) {
                        testButton.disabled = false;
                        testButton.innerHTML = '<i class="ri-mail-send-line mr-2"></i>Тест имейл';
                    }
                });
            },

            /**
             * Инициализиране на настройки за сигурност
             */
            initSecuritySettings: function() {
                this.bindSecuritySettingsEvents();
                this.logDev && this.logDev('Security settings module initialized');
            },

            /**
             * Свързване на събития за настройки за сигурност
             */
            bindSecuritySettingsEvents: function() {
                const self = this;

                // Форма за настройки за сигурност
                const securityForm = document.getElementById('security-settings-form');
                if (securityForm) {
                    securityForm.addEventListener('submit', function(e) {
                        e.preventDefault();
                        self.saveSecuritySettings();
                    });
                }

                // Бутон за запазване
                const saveButton = document.getElementById('save-security-settings');
                if (saveButton) {
                    saveButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.saveSecuritySettings();
                    });
                }

                // IP ограничения checkbox
                const ipRestrictionCheckbox = document.getElementById('ip_restriction_enabled');
                if (ipRestrictionCheckbox) {
                    ipRestrictionCheckbox.addEventListener('change', function() {
                        self.toggleIPRestrictionFields(this.checked);
                    });

                    // Инициализиране на състоянието
                    self.toggleIPRestrictionFields(ipRestrictionCheckbox.checked);
                }

                // Тест на IP ограничения
                const testIPButton = document.getElementById('test-ip-restriction');
                if (testIPButton) {
                    testIPButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.testIPRestriction();
                    });
                }

                // Изчистване на неуспешни опити за вход
                const clearFailedLoginsButton = document.getElementById('clear-failed-logins');
                if (clearFailedLoginsButton) {
                    clearFailedLoginsButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.clearFailedLogins();
                    });
                }
            },

            /**
             * Запазване на настройки за сигурност
             */
            saveSecuritySettings: function() {
                const self = this;
                const form = document.getElementById('security-settings-form');

                if (!form) {
                    self.showSettingsNotification('Формата не е намерена', 'error');
                    return;
                }

                // Проверка за IP ограничения предупреждение
                const ipRestrictionEnabled = document.getElementById('ip_restriction_enabled');
                const allowedIPsText = document.getElementById('allowed_ips_text');

                if (ipRestrictionEnabled && ipRestrictionEnabled.checked && allowedIPsText) {
                    const currentIP = self.settings.config.currentIP || '';
                    const allowedIPs = allowedIPsText.value.split('\n').map(ip => ip.trim()).filter(ip => ip);

                    if (allowedIPs.length > 0 && !allowedIPs.includes(currentIP)) {
                        if (!self.showSettingsConfirm('ВНИМАНИЕ: Вашият текущ IP адрес (' + currentIP + ') не е в списъка с разрешени IP адреси. Може да загубите достъп до административния панел! Сигурни ли сте, че искате да продължите?')) {
                            return;
                        }
                    }
                }

                const formData = new FormData(form);
                formData.append('user_token', self.settings.config.userToken);

                // Показване на loading състояние
                const saveButton = document.getElementById('save-security-settings');
                if (saveButton) {
                    saveButton.disabled = true;
                    saveButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Запазване...';
                }

                fetch(self.settings.config.ajaxUrls.security_update || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');

                        // Показване на предупреждения ако има
                        if (data.warnings && data.warnings.length > 0) {
                            data.warnings.forEach(warning => {
                                setTimeout(() => {
                                    self.showSettingsNotification(warning.message, warning.type);
                                }, 1000);
                            });
                        }

                        // Актуализиране на данните ако са предоставени
                        if (data.updated_data) {
                            self.updateSecuritySettingsDisplay(data.updated_data);
                        }
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');

                        // Показване на грешки при валидация
                        if (data.validation_errors) {
                            self.showValidationErrors(data.validation_errors);
                        }
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error saving security settings:', error);
                    self.showSettingsNotification('Възникна грешка при запазването', 'error');
                })
                .finally(() => {
                    // Възстановяване на бутона
                    if (saveButton) {
                        saveButton.disabled = false;
                        saveButton.innerHTML = '<i class="ri-save-line mr-2"></i>Запази';
                    }
                });
            },

            /**
             * Превключване на полетата за IP ограничения
             */
            toggleIPRestrictionFields: function(enabled) {
                const ipFieldsContainer = document.getElementById('ip-restriction-fields');
                if (ipFieldsContainer) {
                    if (enabled) {
                        ipFieldsContainer.classList.remove('hidden');
                    } else {
                        ipFieldsContainer.classList.add('hidden');
                    }
                }
            },

            /**
             * Тестване на IP ограничения
             */
            testIPRestriction: function() {
                const self = this;
                const testButton = document.getElementById('test-ip-restriction');

                if (testButton) {
                    testButton.disabled = true;
                    testButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Тестване...';
                }

                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);

                const allowedIPsText = document.getElementById('allowed_ips_text');
                if (allowedIPsText) {
                    formData.append('allowed_ips_text', allowedIPsText.value);
                }

                fetch(self.settings.config.ajaxUrls.test_ip || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');
                    } else if (data.warning) {
                        self.showSettingsNotification(data.warning, 'warning');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error testing IP restriction:', error);
                    self.showSettingsNotification('Възникна грешка при тестването', 'error');
                })
                .finally(() => {
                    if (testButton) {
                        testButton.disabled = false;
                        testButton.innerHTML = '<i class="ri-shield-check-line mr-2"></i>Тест IP';
                    }
                });
            },

            /**
             * Изчистване на неуспешни опити за вход
             */
            clearFailedLogins: function() {
                const self = this;

                if (!self.showSettingsConfirm('Сигурни ли сте, че искате да изчистите всички записи за неуспешни опити за вход?')) {
                    return;
                }

                const clearButton = document.getElementById('clear-failed-logins');

                if (clearButton) {
                    clearButton.disabled = true;
                    clearButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Изчистване...';
                }

                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);

                fetch(self.settings.config.ajaxUrls.clear_failed_logins || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');

                        // Актуализиране на брояча ако съществува
                        const failedLoginsCount = document.getElementById('failed-logins-count');
                        if (failedLoginsCount) {
                            failedLoginsCount.textContent = '0';
                        }
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error clearing failed logins:', error);
                    self.showSettingsNotification('Възникна грешка при изчистването', 'error');
                })
                .finally(() => {
                    if (clearButton) {
                        clearButton.disabled = false;
                        clearButton.innerHTML = '<i class="ri-delete-bin-line mr-2"></i>Изчисти';
                    }
                });
            },

            /**
             * Инициализиране на настройки за плащания
             */
            initPaymentSettings: function() {
                this.bindPaymentSettingsEvents();
                this.initPaymentMethodsSortable();
                this.logDev && this.logDev('Payment settings module initialized');
            },

            /**
             * Свързване на събития за настройки за плащания
             */
            bindPaymentSettingsEvents: function() {
                const self = this;

                // Форма за настройки за плащания
                const paymentForm = document.getElementById('payment-settings-form');
                if (paymentForm) {
                    paymentForm.addEventListener('submit', function(e) {
                        e.preventDefault();
                        self.savePaymentSettings();
                    });
                }

                // Бутон за запазване
                const saveButton = document.getElementById('save-payment-settings');
                if (saveButton) {
                    saveButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.savePaymentSettings();
                    });
                }

                // Toggle за payment методи
                const paymentMethodToggles = document.querySelectorAll('.payment-method-toggle');
                paymentMethodToggles.forEach(toggle => {
                    toggle.addEventListener('change', function() {
                        self.togglePaymentMethod(this.dataset.method, this.checked);
                    });
                });

                // Актуализиране на валути
                const updateCurrenciesButton = document.getElementById('update-currencies');
                if (updateCurrenciesButton) {
                    updateCurrenciesButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.updateCurrencies();
                    });
                }

                // Тест на payment метод
                const testPaymentButtons = document.querySelectorAll('.test-payment-method');
                testPaymentButtons.forEach(button => {
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.testPaymentMethod(this.dataset.method);
                    });
                });

                // Промяна на комисионен тип
                const commissionTypeSelects = document.querySelectorAll('.commission-type-select');
                commissionTypeSelects.forEach(select => {
                    select.addEventListener('change', function() {
                        self.updateCommissionFields(this.dataset.method, this.value);
                    });
                });
            },

            /**
             * Запазване на настройки за плащания
             */
            savePaymentSettings: function() {
                const self = this;
                const form = document.getElementById('payment-settings-form');

                if (!form) {
                    self.showSettingsNotification('Формата не е намерена', 'error');
                    return;
                }

                // Валидация преди запазване
                const validationErrors = self.validatePaymentForm(form);
                if (validationErrors.length > 0) {
                    validationErrors.forEach(error => {
                        self.showSettingsNotification(error, 'error');
                    });
                    return;
                }

                const formData = new FormData(form);
                formData.append('user_token', self.settings.config.userToken);

                // Показване на loading състояние
                const saveButton = document.getElementById('save-payment-settings');
                if (saveButton) {
                    saveButton.disabled = true;
                    saveButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Запазване...';
                }

                fetch(self.settings.config.ajaxUrls.payment_save || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');

                        // Показване на предупреждения ако има
                        if (data.warnings && data.warnings.length > 0) {
                            data.warnings.forEach(warning => {
                                setTimeout(() => {
                                    self.showSettingsNotification(warning.message, warning.type);
                                }, 1000);
                            });
                        }

                        // Актуализиране на данните ако са предоставени
                        if (data.updated_data) {
                            self.updatePaymentSettingsDisplay(data.updated_data);
                        }
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');

                        // Показване на грешки при валидация
                        if (data.validation_errors) {
                            self.showValidationErrors(data.validation_errors);
                        }
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error saving payment settings:', error);
                    self.showSettingsNotification('Възникна грешка при запазването', 'error');
                })
                .finally(() => {
                    // Възстановяване на бутона
                    if (saveButton) {
                        saveButton.disabled = false;
                        saveButton.innerHTML = '<i class="ri-save-line mr-2"></i>Запази';
                    }
                });
            },

            /**
             * Валидация на формата за плащания
             */
            validatePaymentForm: function(form) {
                const errors = [];

                // Валидация на минимална/максимална сума
                const minOrder = parseFloat(form.querySelector('[name="payment_minimum_order"]')?.value || 0);
                const maxOrder = parseFloat(form.querySelector('[name="payment_maximum_order"]')?.value || 0);

                if (minOrder < 0) {
                    errors.push('Минималната сума за поръчка не може да бъде отрицателна');
                }

                if (maxOrder < 0) {
                    errors.push('Максималната сума за поръчка не може да бъде отрицателна');
                }

                if (maxOrder > 0 && maxOrder <= minOrder) {
                    errors.push('Максималната сума трябва да бъде по-голяма от минималната');
                }

                // Проверка за активни payment методи
                const activePaymentMethods = form.querySelectorAll('.payment-method-toggle:checked');
                if (activePaymentMethods.length === 0) {
                    errors.push('Трябва да активирате поне един метод за плащане');
                }

                return errors;
            },

            /**
             * Toggle на payment метод
             */
            togglePaymentMethod: function(methodCode, enabled) {
                const self = this;

                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);
                formData.append('method_code', methodCode);
                formData.append('enabled', enabled ? '1' : '0');

                fetch(self.settings.config.ajaxUrls.toggle_payment_method || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');

                        // Актуализиране на визуалното състояние
                        const methodRow = document.querySelector(`[data-method="${methodCode}"]`);
                        if (methodRow) {
                            if (enabled) {
                                methodRow.classList.remove('opacity-50');
                                methodRow.classList.add('bg-green-50');
                            } else {
                                methodRow.classList.add('opacity-50');
                                methodRow.classList.remove('bg-green-50');
                            }
                        }
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');

                        // Връщане на toggle-а в предишното състояние
                        const toggle = document.querySelector(`[data-method="${methodCode}"].payment-method-toggle`);
                        if (toggle) {
                            toggle.checked = !enabled;
                        }
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error toggling payment method:', error);
                    self.showSettingsNotification('Възникна грешка при промяната', 'error');
                });
            },

            /**
             * Тестване на payment метод
             */
            testPaymentMethod: function(methodCode) {
                const self = this;
                const testButton = document.querySelector(`[data-method="${methodCode}"].test-payment-method`);

                if (testButton) {
                    testButton.disabled = true;
                    testButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Тестване...';
                }

                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);
                formData.append('method_code', methodCode);

                fetch(self.settings.config.ajaxUrls.test_payment || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');
                    } else if (data.warning) {
                        self.showSettingsNotification(data.warning, 'warning');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error testing payment method:', error);
                    self.showSettingsNotification('Възникна грешка при тестването', 'error');
                })
                .finally(() => {
                    if (testButton) {
                        testButton.disabled = false;
                        testButton.innerHTML = '<i class="ri-test-tube-line mr-2"></i>Тест';
                    }
                });
            },

            /**
             * Актуализиране на валути
             */
            updateCurrencies: function() {
                const self = this;
                const updateButton = document.getElementById('update-currencies');

                if (updateButton) {
                    updateButton.disabled = true;
                    updateButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Актуализиране...';
                }

                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);

                fetch(self.settings.config.ajaxUrls.update_currencies || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');

                        // Актуализиране на курсовете в таблицата
                        if (data.updated_currencies) {
                            self.updateCurrencyRatesDisplay(data.updated_currencies);
                        }
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error updating currencies:', error);
                    self.showSettingsNotification('Възникна грешка при актуализирането', 'error');
                })
                .finally(() => {
                    if (updateButton) {
                        updateButton.disabled = false;
                        updateButton.innerHTML = '<i class="ri-refresh-line mr-2"></i>Актуализирай валути';
                    }
                });
            },

            /**
             * Актуализиране на полетата за комисионни
             */
            updateCommissionFields: function(methodCode, commissionType) {
                const commissionValueField = document.querySelector(`[name="payment_methods[${methodCode}][commission_value]"]`);
                const commissionLabel = document.querySelector(`label[for="commission_value_${methodCode}"]`);

                if (commissionValueField && commissionLabel) {
                    switch (commissionType) {
                        case 'percentage':
                            commissionLabel.textContent = 'Процент (%)';
                            commissionValueField.placeholder = '0.00';
                            commissionValueField.max = '100';
                            break;
                        case 'fixed':
                            commissionLabel.textContent = 'Фиксирана сума (лв.)';
                            commissionValueField.placeholder = '0.00';
                            commissionValueField.removeAttribute('max');
                            break;
                        case 'mixed':
                            commissionLabel.textContent = 'Фиксирана сума + % (лв.)';
                            commissionValueField.placeholder = '2.50';
                            commissionValueField.removeAttribute('max');
                            break;
                    }
                }
            },

            /**
             * Инициализиране на sortable за payment методи
             */
            initPaymentMethodsSortable: function() {
                const paymentMethodsList = document.getElementById('payment-methods-list');
                if (paymentMethodsList && typeof Sortable !== 'undefined') {
                    new Sortable(paymentMethodsList, {
                        animation: 150,
                        ghostClass: 'sortable-ghost',
                        onEnd: (evt) => {
                            this.reorderPaymentMethods();
                        }
                    });
                }
            },

            /**
             * Преподреждане на payment методи
             */
            reorderPaymentMethods: function() {
                const self = this;
                const paymentMethodsList = document.getElementById('payment-methods-list');

                if (!paymentMethodsList) return;

                const methodCodes = [];
                const methodRows = paymentMethodsList.querySelectorAll('[data-method]');

                methodRows.forEach((row, index) => {
                    const methodCode = row.dataset.method;
                    if (methodCode) {
                        methodCodes.push({
                            code: methodCode,
                            sort_order: index + 1
                        });
                    }
                });

                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);
                formData.append('method_order', JSON.stringify(methodCodes));

                fetch(self.settings.config.ajaxUrls.reorder_payment_methods || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification('Подредбата е запазена', 'success');
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error reordering payment methods:', error);
                    self.showSettingsNotification('Възникна грешка при преподреждането', 'error');
                });
            },

            /**
             * Актуализиране на показването на валутни курсове
             */
            updateCurrencyRatesDisplay: function(currencies) {
                currencies.forEach(currency => {
                    const rateElement = document.querySelector(`#currency-rate-${currency.code}`);
                    if (rateElement) {
                        rateElement.textContent = currency.value;
                    }

                    const lastUpdateElement = document.querySelector(`#currency-update-${currency.code}`);
                    if (lastUpdateElement) {
                        lastUpdateElement.textContent = currency.date_modified;
                    }
                });
            },

            /**
             * Актуализиране на показването на настройки за плащания
             */
            updatePaymentSettingsDisplay: function(data) {
                // Актуализиране на статистики ако има
                if (data.statistics) {
                    const activeMethodsElement = document.getElementById('active-payment-methods-count');
                    if (activeMethodsElement && data.statistics.active_payment_methods !== undefined) {
                        activeMethodsElement.textContent = data.statistics.active_payment_methods;
                    }
                }

                // Актуализиране на статуса на payment методи
                if (data.payment_methods) {
                    Object.keys(data.payment_methods).forEach(methodCode => {
                        const methodData = data.payment_methods[methodCode];
                        const methodRow = document.querySelector(`[data-method="${methodCode}"]`);

                        if (methodRow) {
                            if (methodData.status) {
                                methodRow.classList.remove('opacity-50');
                                methodRow.classList.add('bg-green-50');
                            } else {
                                methodRow.classList.add('opacity-50');
                                methodRow.classList.remove('bg-green-50');
                            }
                        }
                    });
                }
            },

            /**
             * Актуализиране на показването на основни настройки
             */
            updateBasicSettingsDisplay: function(data) {
                // Актуализиране на полетата с новите стойности
                Object.keys(data).forEach(key => {
                    const field = document.querySelector(`[name="${key}"]`);
                    if (field) {
                        if (field.type === 'checkbox') {
                            field.checked = !!data[key];
                        } else {
                            field.value = data[key] || '';
                        }
                    }
                });
            },

            /**
             * Актуализиране на показването на настройки за сигурност
             */
            updateSecuritySettingsDisplay: function(data) {
                // Актуализиране на полетата с новите стойности
                Object.keys(data).forEach(key => {
                    const field = document.querySelector(`[name="${key}"]`);
                    if (field) {
                        if (field.type === 'checkbox') {
                            field.checked = !!data[key];
                        } else if (key === 'allowed_ips' && Array.isArray(data[key])) {
                            // Специална обработка за IP адресите
                            const textareaField = document.querySelector('[name="allowed_ips_text"]');
                            if (textareaField) {
                                textareaField.value = data[key].join('\n');
                            }
                        } else {
                            field.value = data[key] || '';
                        }
                    }
                });

                // Актуализиране на IP ограниченията
                if (data.ip_restriction_enabled !== undefined) {
                    this.toggleIPRestrictionFields(!!data.ip_restriction_enabled);
                }
            },

            /**
             * Инициализиране на настройки за доставка
             */
            initDeliverySettings: function() {
                this.bindDeliverySettingsEvents();
                this.initShippingMethodsSortable();
                this.logDev && this.logDev('Delivery settings module initialized');
            },

            /**
             * Свързване на събития за настройки за доставка
             */
            bindDeliverySettingsEvents: function() {
                const self = this;

                // Форма за настройки за доставка
                const deliveryForm = document.getElementById('delivery-settings-form');
                if (deliveryForm) {
                    deliveryForm.addEventListener('submit', function(e) {
                        e.preventDefault();
                        self.saveDeliverySettings();
                    });
                }

                // Бутон за запазване
                const saveButton = document.getElementById('save-delivery-settings');
                if (saveButton) {
                    saveButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.saveDeliverySettings();
                    });
                }

                // Toggle за shipping методи
                const shippingMethodToggles = document.querySelectorAll('.shipping-method-toggle');
                shippingMethodToggles.forEach(toggle => {
                    toggle.addEventListener('change', function() {
                        self.toggleShippingMethod(this.dataset.method, this.checked);
                    });
                });

                // Тест на shipping метод
                const testShippingButtons = document.querySelectorAll('.test-shipping-method');
                testShippingButtons.forEach(button => {
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.testShippingMethod(this.dataset.method);
                    });
                });

                // Калкулатор за доставка
                const calculateShippingButton = document.getElementById('calculate-shipping');
                if (calculateShippingButton) {
                    calculateShippingButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.calculateShipping();
                    });
                }

                // Добавяне на shipping rate
                const addRateButtons = document.querySelectorAll('.add-shipping-rate');
                addRateButtons.forEach(button => {
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.addShippingRate(this.dataset.method);
                    });
                });

                // Премахване на shipping rate
                document.addEventListener('click', function(e) {
                    if (e.target.classList.contains('remove-shipping-rate')) {
                        e.preventDefault();
                        self.removeShippingRate(e.target.dataset.method, e.target.dataset.index);
                    }
                });
            },

            /**
             * Запазване на настройки за доставка
             */
            saveDeliverySettings: function() {
                const self = this;
                const form = document.getElementById('delivery-settings-form');

                if (!form) {
                    self.showSettingsNotification('Формата не е намерена', 'error');
                    return;
                }

                // Валидация преди запазване
                const validationErrors = self.validateDeliveryForm(form);
                if (validationErrors.length > 0) {
                    validationErrors.forEach(error => {
                        self.showSettingsNotification(error, 'error');
                    });
                    return;
                }

                const formData = new FormData(form);
                formData.append('user_token', self.settings.config.userToken);

                // Показване на loading състояние
                const saveButton = document.getElementById('save-delivery-settings');
                if (saveButton) {
                    saveButton.disabled = true;
                    saveButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Запазване...';
                }

                fetch(self.settings.config.ajaxUrls.delivery_save || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');

                        // Показване на предупреждения ако има
                        if (data.warnings && data.warnings.length > 0) {
                            data.warnings.forEach(warning => {
                                setTimeout(() => {
                                    self.showSettingsNotification(warning.message, warning.type);
                                }, 1000);
                            });
                        }

                        // Актуализиране на данните ако са предоставени
                        if (data.updated_data) {
                            self.updateDeliverySettingsDisplay(data.updated_data);
                        }
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');

                        // Показване на грешки при валидация
                        if (data.validation_errors) {
                            self.showValidationErrors(data.validation_errors);
                        }
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error saving delivery settings:', error);
                    self.showSettingsNotification('Възникна грешка при запазването', 'error');
                })
                .finally(() => {
                    // Възстановяване на бутона
                    if (saveButton) {
                        saveButton.disabled = false;
                        saveButton.innerHTML = '<i class="ri-save-line mr-2"></i>Запази';
                    }
                });
            },

            /**
             * Валидация на формата за доставка
             */
            validateDeliveryForm: function(form) {
                const errors = [];

                // Валидация на минимално/максимално време
                const minTime = parseInt(form.querySelector('[name="delivery_time_min"]')?.value || 0);
                const maxTime = parseInt(form.querySelector('[name="delivery_time_max"]')?.value || 0);

                if (minTime < 0) {
                    errors.push('Минималното време за доставка не може да бъде отрицателно');
                }

                if (maxTime < 1) {
                    errors.push('Максималното време за доставка трябва да бъде поне 1 ден');
                }

                if (maxTime <= minTime) {
                    errors.push('Максималното време трябва да бъде по-голямо от минималното');
                }

                // Проверка за активни shipping методи
                const activeShippingMethods = form.querySelectorAll('.shipping-method-toggle:checked');
                if (activeShippingMethods.length === 0) {
                    errors.push('Трябва да активирате поне един метод за доставка');
                }

                return errors;
            },

            /**
             * Toggle на shipping метод
             */
            toggleShippingMethod: function(methodCode, enabled) {
                const self = this;

                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);
                formData.append('method_code', methodCode);
                formData.append('enabled', enabled ? '1' : '0');

                fetch(self.settings.config.ajaxUrls.toggle_shipping_method || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');

                        // Актуализиране на визуалното състояние
                        const methodRow = document.querySelector(`[data-method="${methodCode}"]`);
                        if (methodRow) {
                            if (enabled) {
                                methodRow.classList.remove('opacity-50');
                                methodRow.classList.add('bg-green-50');
                            } else {
                                methodRow.classList.add('opacity-50');
                                methodRow.classList.remove('bg-green-50');
                            }
                        }
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');

                        // Връщане на toggle-а в предишното състояние
                        const toggle = document.querySelector(`[data-method="${methodCode}"].shipping-method-toggle`);
                        if (toggle) {
                            toggle.checked = !enabled;
                        }
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error toggling shipping method:', error);
                    self.showSettingsNotification('Възникна грешка при промяната', 'error');
                });
            },

            /**
             * Тестване на shipping метод
             */
            testShippingMethod: function(methodCode) {
                const self = this;
                const testButton = document.querySelector(`[data-method="${methodCode}"].test-shipping-method`);

                if (testButton) {
                    testButton.disabled = true;
                    testButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Тестване...';
                }

                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);
                formData.append('method_code', methodCode);

                fetch(self.settings.config.ajaxUrls.test_shipping || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');
                    } else if (data.warning) {
                        self.showSettingsNotification(data.warning, 'warning');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error testing shipping method:', error);
                    self.showSettingsNotification('Възникна грешка при тестването', 'error');
                })
                .finally(() => {
                    if (testButton) {
                        testButton.disabled = false;
                        testButton.innerHTML = '<i class="ri-test-tube-line mr-2"></i>Тест';
                    }
                });
            },

            /**
             * Калкулиране на цена за доставка
             */
            calculateShipping: function() {
                const self = this;
                const calculateButton = document.getElementById('calculate-shipping');

                // Получаване на данни от формата за калкулация
                const weight = document.getElementById('calc-weight')?.value || 0;
                const total = document.getElementById('calc-total')?.value || 0;
                const country = document.getElementById('calc-country')?.value || '';
                const zone = document.getElementById('calc-zone')?.value || '';

                if (!weight && !total) {
                    self.showSettingsNotification('Въведете тегло или стойност за калкулация', 'warning');
                    return;
                }

                if (calculateButton) {
                    calculateButton.disabled = true;
                    calculateButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Калкулиране...';
                }

                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);
                formData.append('weight', weight);
                formData.append('total', total);
                formData.append('country', country);
                formData.append('zone', zone);

                fetch(self.settings.config.ajaxUrls.calculate_shipping || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showShippingCalculationResults(data.results);
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error calculating shipping:', error);
                    self.showSettingsNotification('Възникна грешка при калкулирането', 'error');
                })
                .finally(() => {
                    if (calculateButton) {
                        calculateButton.disabled = false;
                        calculateButton.innerHTML = '<i class="ri-calculator-line mr-2"></i>Калкулирай';
                    }
                });
            },

            /**
             * Показване на резултатите от калкулацията
             */
            showShippingCalculationResults: function(results) {
                const resultsContainer = document.getElementById('shipping-calculation-results');
                if (!resultsContainer) return;

                let html = '<div class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded">';
                html += '<h4 class="font-medium text-blue-800 mb-3">Резултати от калкулацията:</h4>';
                html += '<div class="space-y-2">';

                if (results && results.length > 0) {
                    results.forEach(result => {
                        html += `<div class="flex justify-between items-center">`;
                        html += `<span class="text-sm text-blue-700">${result.title}:</span>`;
                        html += `<span class="font-medium text-blue-900">${result.cost} лв.</span>`;
                        html += `</div>`;
                    });
                } else {
                    html += '<p class="text-sm text-blue-600">Няма налични методи за доставка за тези параметри</p>';
                }

                html += '</div></div>';
                resultsContainer.innerHTML = html;
            },

            /**
             * Добавяне на shipping rate
             */
            addShippingRate: function(methodCode) {
                const ratesContainer = document.getElementById(`${methodCode}-rates-container`);
                if (!ratesContainer) return;

                const rateIndex = ratesContainer.children.length;
                const rateHtml = this.createShippingRateRow(methodCode, rateIndex);

                ratesContainer.insertAdjacentHTML('beforeend', rateHtml);

                this.showSettingsNotification('Добавен нов ред за цена', 'success');
            },

            /**
             * Премахване на shipping rate
             */
            removeShippingRate: function(methodCode, rateIndex) {
                const rateRow = document.getElementById(`${methodCode}-rate-${rateIndex}`);
                if (rateRow) {
                    rateRow.remove();
                    this.showSettingsNotification('Редът е премахнат', 'success');
                }
            },

            /**
             * Създаване на ред за shipping rate
             */
            createShippingRateRow: function(methodCode, index) {
                return `
                    <div id="${methodCode}-rate-${index}" class="grid grid-cols-4 gap-2 items-center p-2 border border-gray-200 rounded">
                        <input type="number"
                               name="shipping_methods[${methodCode}][rates][${index}][weight_from]"
                               class="text-sm border border-gray-300 rounded px-2 py-1"
                               placeholder="От (кг)"
                               step="0.01"
                               min="0">
                        <input type="number"
                               name="shipping_methods[${methodCode}][rates][${index}][weight_to]"
                               class="text-sm border border-gray-300 rounded px-2 py-1"
                               placeholder="До (кг)"
                               step="0.01"
                               min="0">
                        <input type="number"
                               name="shipping_methods[${methodCode}][rates][${index}][cost]"
                               class="text-sm border border-gray-300 rounded px-2 py-1"
                               placeholder="Цена (лв.)"
                               step="0.01"
                               min="0">
                        <button type="button"
                                class="remove-shipping-rate px-2 py-1 bg-red-500 text-white text-sm rounded hover:bg-red-600"
                                data-method="${methodCode}"
                                data-index="${index}">
                            <i class="ri-delete-bin-line"></i>
                        </button>
                    </div>
                `;
            },

            /**
             * Инициализиране на sortable за shipping методи
             */
            initShippingMethodsSortable: function() {
                const shippingMethodsList = document.getElementById('shipping-methods-list');
                if (shippingMethodsList && typeof Sortable !== 'undefined') {
                    new Sortable(shippingMethodsList, {
                        animation: 150,
                        ghostClass: 'sortable-ghost',
                        onEnd: () => {
                            this.reorderShippingMethods();
                        }
                    });
                }
            },

            /**
             * Преподреждане на shipping методи
             */
            reorderShippingMethods: function() {
                const self = this;
                const shippingMethodsList = document.getElementById('shipping-methods-list');

                if (!shippingMethodsList) return;

                const methodCodes = [];
                const methodRows = shippingMethodsList.querySelectorAll('[data-method]');

                methodRows.forEach((row, index) => {
                    const methodCode = row.dataset.method;
                    if (methodCode) {
                        methodCodes.push({
                            code: methodCode,
                            sort_order: index + 1
                        });
                    }
                });

                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);
                formData.append('method_order', JSON.stringify(methodCodes));

                fetch(self.settings.config.ajaxUrls.reorder_shipping_methods || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification('Подредбата е запазена', 'success');
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error reordering shipping methods:', error);
                    self.showSettingsNotification('Възникна грешка при преподреждането', 'error');
                });
            },

            /**
             * Актуализиране на показването на настройки за доставка
             */
            updateDeliverySettingsDisplay: function(data) {
                // Актуализиране на статистики ако има
                if (data.statistics) {
                    const activeMethodsElement = document.getElementById('active-shipping-methods-count');
                    if (activeMethodsElement && data.statistics.active_shipping_methods !== undefined) {
                        activeMethodsElement.textContent = data.statistics.active_shipping_methods;
                    }
                }

                // Актуализиране на статуса на shipping методи
                if (data.shipping_methods) {
                    Object.keys(data.shipping_methods).forEach(methodCode => {
                        const methodData = data.shipping_methods[methodCode];
                        const methodRow = document.querySelector(`[data-method="${methodCode}"]`);

                        if (methodRow) {
                            if (methodData.status) {
                                methodRow.classList.remove('opacity-50');
                                methodRow.classList.add('bg-green-50');
                            } else {
                                methodRow.classList.add('opacity-50');
                                methodRow.classList.remove('bg-green-50');
                            }
                        }
                    });
                }

                // Актуализиране на полетата с новите стойности
                Object.keys(data).forEach(key => {
                    if (key !== 'shipping_methods' && key !== 'statistics') {
                        const field = document.querySelector(`[name="${key}"]`);
                        if (field) {
                            if (field.type === 'checkbox') {
                                field.checked = !!data[key];
                            } else {
                                field.value = data[key] || '';
                            }
                        }
                    }
                });
            },

            /**
             * Инициализиране на настройки за известия
             */
            initNotificationSettings: function() {
                this.bindNotificationSettingsEvents();
                this.initEmailTemplateEditor();
                this.logDev && this.logDev('Notification settings module initialized');
            },

            /**
             * Свързване на събития за настройки за известия
             */
            bindNotificationSettingsEvents: function() {
                const self = this;

                // Форма за настройки за известия
                const notificationForm = document.getElementById('notification-settings-form');
                if (notificationForm) {
                    notificationForm.addEventListener('submit', function(e) {
                        e.preventDefault();
                        self.saveNotificationSettings();
                    });
                }

                // Бутон за запазване
                const saveButton = document.getElementById('save-notification-settings');
                if (saveButton) {
                    saveButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.saveNotificationSettings();
                    });
                }

                // Промяна на mail protocol
                const mailProtocolSelect = document.getElementById('config_mail_protocol');
                if (mailProtocolSelect) {
                    mailProtocolSelect.addEventListener('change', function() {
                        self.toggleSMTPFields(this.value === 'smtp');
                    });

                    // Инициализиране на състоянието
                    self.toggleSMTPFields(mailProtocolSelect.value === 'smtp');
                }

                // Тест на email настройки
                const testEmailButton = document.getElementById('test-email-settings');
                if (testEmailButton) {
                    testEmailButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.testEmailSettings();
                    });
                }

                // Тест на SMS настройки
                const testSMSButton = document.getElementById('test-sms-settings');
                if (testSMSButton) {
                    testSMSButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.testSMSSettings();
                    });
                }

                // Preview на email шаблон
                const previewButtons = document.querySelectorAll('.preview-email-template');
                previewButtons.forEach(button => {
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.previewEmailTemplate(this.dataset.template);
                    });
                });

                // Reset на email шаблон
                const resetButtons = document.querySelectorAll('.reset-email-template');
                resetButtons.forEach(button => {
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.resetEmailTemplate(this.dataset.template);
                    });
                });

                // Toggle за SMS настройки
                const smsEnabledToggle = document.getElementById('notification_sms_enabled');
                if (smsEnabledToggle) {
                    smsEnabledToggle.addEventListener('change', function() {
                        self.toggleSMSFields(this.checked);
                    });

                    // Инициализиране на състоянието
                    self.toggleSMSFields(smsEnabledToggle.checked);
                }

                // Изпращане на тестово известие
                const sendTestButton = document.getElementById('send-test-notification');
                if (sendTestButton) {
                    sendTestButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.sendTestNotification();
                    });
                }
            },

            /**
             * Запазване на настройки за известия
             */
            saveNotificationSettings: function() {
                const self = this;
                const form = document.getElementById('notification-settings-form');

                if (!form) {
                    self.showSettingsNotification('Формата не е намерена', 'error');
                    return;
                }

                // Валидация преди запазване
                const validationErrors = self.validateNotificationForm(form);
                if (validationErrors.length > 0) {
                    validationErrors.forEach(error => {
                        self.showSettingsNotification(error, 'error');
                    });
                    return;
                }

                const formData = new FormData(form);
                formData.append('user_token', self.settings.config.userToken);

                // Показване на loading състояние
                const saveButton = document.getElementById('save-notification-settings');
                if (saveButton) {
                    saveButton.disabled = true;
                    saveButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Запазване...';
                }

                fetch(self.settings.config.ajaxUrls.notifications_save || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');

                        // Показване на предупреждения ако има
                        if (data.warnings && data.warnings.length > 0) {
                            data.warnings.forEach(warning => {
                                setTimeout(() => {
                                    self.showSettingsNotification(warning.message, warning.type);
                                }, 1000);
                            });
                        }

                        // Актуализиране на данните ако са предоставени
                        if (data.updated_data) {
                            self.updateNotificationSettingsDisplay(data.updated_data);
                        }
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');

                        // Показване на грешки при валидация
                        if (data.validation_errors) {
                            self.showValidationErrors(data.validation_errors);
                        }
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error saving notification settings:', error);
                    self.showSettingsNotification('Възникна грешка при запазването', 'error');
                })
                .finally(() => {
                    // Възстановяване на бутона
                    if (saveButton) {
                        saveButton.disabled = false;
                        saveButton.innerHTML = '<i class="ri-save-line mr-2"></i>Запази';
                    }
                });
            },

            /**
             * Валидация на формата за известия
             */
            validateNotificationForm: function(form) {
                const errors = [];

                // Валидация на email адрес
                const email = form.querySelector('[name="config_email"]')?.value || '';
                if (!email) {
                    errors.push('Email адресът е задължителен');
                } else if (!this.isValidEmail(email)) {
                    errors.push('Невалиден email адрес');
                }

                // Валидация на име на магазина
                const storeName = form.querySelector('[name="config_name"]')?.value || '';
                if (!storeName) {
                    errors.push('Името на магазина е задължително');
                }

                // Валидация на SMTP настройки ако е избран SMTP
                const mailProtocol = form.querySelector('[name="config_mail_protocol"]')?.value || '';
                if (mailProtocol === 'smtp') {
                    const smtpHost = form.querySelector('[name="config_mail_smtp_hostname"]')?.value || '';
                    const smtpUser = form.querySelector('[name="config_mail_smtp_username"]')?.value || '';
                    const smtpPass = form.querySelector('[name="config_mail_smtp_password"]')?.value || '';

                    if (!smtpHost) {
                        errors.push('SMTP hostname е задължителен при SMTP протокол');
                    }
                    if (!smtpUser) {
                        errors.push('SMTP username е задължителен при SMTP протокол');
                    }
                    if (!smtpPass) {
                        errors.push('SMTP password е задължителна при SMTP протокол');
                    }
                }

                return errors;
            },

            /**
             * Toggle на SMTP полета
             */
            toggleSMTPFields: function(enabled) {
                const smtpFields = document.querySelectorAll('.smtp-field');
                smtpFields.forEach(field => {
                    if (enabled) {
                        field.style.display = 'block';
                        field.classList.remove('hidden');
                    } else {
                        field.style.display = 'none';
                        field.classList.add('hidden');
                    }
                });
            },

            /**
             * Toggle на SMS полета
             */
            toggleSMSFields: function(enabled) {
                const smsFields = document.querySelectorAll('.sms-field');
                smsFields.forEach(field => {
                    if (enabled) {
                        field.style.display = 'block';
                        field.classList.remove('hidden');
                    } else {
                        field.style.display = 'none';
                        field.classList.add('hidden');
                    }
                });
            },

            /**
             * Тестване на email настройки
             */
            testEmailSettings: function() {
                const self = this;
                const testButton = document.getElementById('test-email-settings');

                if (testButton) {
                    testButton.disabled = true;
                    testButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Тестване...';
                }

                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);

                // Добавяне на email настройки за тестване
                const form = document.getElementById('notification-settings-form');
                if (form) {
                    const emailFields = ['config_mail_protocol', 'config_mail_smtp_hostname', 'config_mail_smtp_username', 'config_mail_smtp_password', 'config_mail_smtp_port', 'config_email'];
                    emailFields.forEach(fieldName => {
                        const field = form.querySelector(`[name="${fieldName}"]`);
                        if (field) {
                            formData.append(fieldName, field.value);
                        }
                    });
                }

                fetch(self.settings.config.ajaxUrls.test_email || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');
                    } else if (data.warning) {
                        self.showSettingsNotification(data.warning, 'warning');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error testing email settings:', error);
                    self.showSettingsNotification('Възникна грешка при тестването', 'error');
                })
                .finally(() => {
                    if (testButton) {
                        testButton.disabled = false;
                        testButton.innerHTML = '<i class="ri-mail-send-line mr-2"></i>Тест Email';
                    }
                });
            },

            /**
             * Тестване на SMS настройки
             */
            testSMSSettings: function() {
                const self = this;
                const testButton = document.getElementById('test-sms-settings');

                if (testButton) {
                    testButton.disabled = true;
                    testButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Тестване...';
                }

                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);

                // Добавяне на SMS настройки за тестване
                const form = document.getElementById('notification-settings-form');
                if (form) {
                    const smsFields = ['sms_provider', 'sms_api_key', 'sms_api_secret', 'sms_sender_name'];
                    smsFields.forEach(fieldName => {
                        const field = form.querySelector(`[name="${fieldName}"]`);
                        if (field) {
                            formData.append(fieldName, field.value);
                        }
                    });
                }

                fetch(self.settings.config.ajaxUrls.test_sms || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');
                    } else if (data.warning) {
                        self.showSettingsNotification(data.warning, 'warning');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error testing SMS settings:', error);
                    self.showSettingsNotification('Възникна грешка при тестването', 'error');
                })
                .finally(() => {
                    if (testButton) {
                        testButton.disabled = false;
                        testButton.innerHTML = '<i class="ri-message-2-line mr-2"></i>Тест SMS';
                    }
                });
            },

            /**
             * Preview на email шаблон
             */
            previewEmailTemplate: function(templateCode) {
                const self = this;

                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);
                formData.append('template_code', templateCode);

                // Получаване на данните от шаблона
                const subjectField = document.querySelector(`[name="email_templates[${templateCode}][subject]"]`);
                const contentField = document.querySelector(`[name="email_templates[${templateCode}][content]"]`);

                if (subjectField && contentField) {
                    formData.append('subject', subjectField.value);
                    formData.append('content', contentField.value);
                }

                fetch(self.settings.config.ajaxUrls.preview_template || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.preview) {
                        self.showEmailPreviewModal(data.preview);
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error previewing email template:', error);
                    self.showSettingsNotification('Възникна грешка при preview', 'error');
                });
            },

            /**
             * Показване на preview modal за email
             */
            showEmailPreviewModal: function(preview) {
                // Създаване на modal за preview
                const modal = document.createElement('div');
                modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                modal.innerHTML = `
                    <div class="bg-white rounded-lg max-w-2xl w-full mx-4 max-h-[80vh] overflow-hidden">
                        <div class="flex justify-between items-center p-4 border-b">
                            <h3 class="text-lg font-semibold">Preview на email шаблон</h3>
                            <button class="close-preview text-gray-500 hover:text-gray-700">
                                <i class="ri-close-line text-xl"></i>
                            </button>
                        </div>
                        <div class="p-4 overflow-y-auto max-h-[60vh]">
                            <div class="mb-4">
                                <strong>Тема:</strong> ${preview.subject || ''}
                            </div>
                            <div class="border rounded p-4 bg-gray-50">
                                <pre class="whitespace-pre-wrap">${preview.content || ''}</pre>
                            </div>
                        </div>
                        <div class="flex justify-end p-4 border-t">
                            <button class="close-preview px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
                                Затвори
                            </button>
                        </div>
                    </div>
                `;

                // Добавяне на modal към страницата
                document.body.appendChild(modal);

                // Затваряне на modal
                modal.querySelectorAll('.close-preview').forEach(button => {
                    button.addEventListener('click', () => {
                        document.body.removeChild(modal);
                    });
                });

                // Затваряне при клик извън modal
                modal.addEventListener('click', (e) => {
                    if (e.target === modal) {
                        document.body.removeChild(modal);
                    }
                });
            },

            /**
             * Reset на email шаблон
             */
            resetEmailTemplate: function(templateCode) {
                const self = this;

                if (!self.showSettingsConfirm('Сигурни ли сте, че искате да възстановите шаблона до оригиналните настройки?')) {
                    return;
                }

                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);
                formData.append('template_code', templateCode);

                fetch(self.settings.config.ajaxUrls.reset_template || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');

                        // Актуализиране на полетата с новите стойности
                        if (data.template) {
                            const subjectField = document.querySelector(`[name="email_templates[${templateCode}][subject]"]`);
                            const contentField = document.querySelector(`[name="email_templates[${templateCode}][content]"]`);

                            if (subjectField) subjectField.value = data.template.subject || '';
                            if (contentField) contentField.value = data.template.content || '';
                        }
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error resetting email template:', error);
                    self.showSettingsNotification('Възникна грешка при възстановяването', 'error');
                });
            },

            /**
             * Изпращане на тестово известие
             */
            sendTestNotification: function() {
                const self = this;
                const sendButton = document.getElementById('send-test-notification');

                const testEmail = document.getElementById('test-notification-email')?.value || '';
                const testType = document.getElementById('test-notification-type')?.value || '';

                if (!testEmail) {
                    self.showSettingsNotification('Въведете email адрес за тестване', 'warning');
                    return;
                }

                if (!testType) {
                    self.showSettingsNotification('Изберете тип известие за тестване', 'warning');
                    return;
                }

                if (sendButton) {
                    sendButton.disabled = true;
                    sendButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Изпращане...';
                }

                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);
                formData.append('test_email', testEmail);
                formData.append('test_type', testType);

                fetch(self.settings.config.ajaxUrls.send_test_notification || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error sending test notification:', error);
                    self.showSettingsNotification('Възникна грешка при изпращането', 'error');
                })
                .finally(() => {
                    if (sendButton) {
                        sendButton.disabled = false;
                        sendButton.innerHTML = '<i class="ri-send-plane-line mr-2"></i>Изпрати тест';
                    }
                });
            },

            /**
             * Инициализиране на email template editor
             */
            initEmailTemplateEditor: function() {
                // Добавяне на функционалност за автоматично resize на textarea
                const textareas = document.querySelectorAll('.email-template-content');
                textareas.forEach(textarea => {
                    textarea.addEventListener('input', function() {
                        this.style.height = 'auto';
                        this.style.height = this.scrollHeight + 'px';
                    });

                    // Инициализиране на размера
                    textarea.style.height = 'auto';
                    textarea.style.height = textarea.scrollHeight + 'px';
                });
            },

            /**
             * Актуализиране на показването на настройки за известия
             */
            updateNotificationSettingsDisplay: function(data) {
                // Актуализиране на статистики ако има
                if (data.statistics) {
                    const activeTemplatesElement = document.getElementById('active-email-templates-count');
                    if (activeTemplatesElement && data.statistics.active_email_templates !== undefined) {
                        activeTemplatesElement.textContent = data.statistics.active_email_templates;
                    }

                    const emailsSentElement = document.getElementById('emails-sent-today-count');
                    if (emailsSentElement && data.statistics.emails_sent_today !== undefined) {
                        emailsSentElement.textContent = data.statistics.emails_sent_today;
                    }
                }

                // Актуализиране на полетата с новите стойности
                Object.keys(data).forEach(key => {
                    if (key !== 'email_templates' && key !== 'statistics') {
                        const field = document.querySelector(`[name="${key}"]`);
                        if (field) {
                            if (field.type === 'checkbox') {
                                field.checked = !!data[key];
                            } else {
                                field.value = data[key] || '';
                            }
                        }
                    }
                });

                // Актуализиране на email шаблони
                if (data.email_templates) {
                    Object.keys(data.email_templates).forEach(templateCode => {
                        const templateData = data.email_templates[templateCode];

                        const subjectField = document.querySelector(`[name="email_templates[${templateCode}][subject]"]`);
                        const contentField = document.querySelector(`[name="email_templates[${templateCode}][content]"]`);
                        const statusField = document.querySelector(`[name="email_templates[${templateCode}][status]"]`);

                        if (subjectField) subjectField.value = templateData.subject || '';
                        if (contentField) contentField.value = templateData.content || '';
                        if (statusField) statusField.checked = !!templateData.status;
                    });
                }

                // Актуализиране на toggle състояния
                this.toggleSMTPFields(data.config_mail_protocol === 'smtp');
                this.toggleSMSFields(!!data.notification_sms_enabled);
            },

            /**
             * Инициализиране на настройки за интеграции
             */
            initIntegrationSettings: function() {
                this.bindIntegrationSettingsEvents();
                this.initWebhookManager();
                this.logDev && this.logDev('Integration settings module initialized');
            },

            /**
             * Свързване на събития за настройки за интеграции
             */
            bindIntegrationSettingsEvents: function() {
                const self = this;

                // Форма за настройки за интеграции
                const integrationForm = document.getElementById('integration-settings-form');
                if (integrationForm) {
                    integrationForm.addEventListener('submit', function(e) {
                        e.preventDefault();
                        self.saveIntegrationSettings();
                    });
                }

                // Бутон за запазване
                const saveButton = document.getElementById('save-integration-settings');
                if (saveButton) {
                    saveButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.saveIntegrationSettings();
                    });
                }

                // Toggle за интеграции
                const integrationToggles = document.querySelectorAll('.integration-toggle');
                integrationToggles.forEach(toggle => {
                    toggle.addEventListener('change', function() {
                        self.toggleIntegration(this.dataset.integration, this.dataset.type, this.checked);
                    });
                });

                // Тест на интеграции
                const testButtons = document.querySelectorAll('.test-integration');
                testButtons.forEach(button => {
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.testIntegration(this.dataset.integration, this.dataset.type);
                    });
                });

                // Генериране на API ключове
                const generateApiButtons = document.querySelectorAll('.generate-api-key');
                generateApiButtons.forEach(button => {
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.generateApiKey(this.dataset.api);
                    });
                });

                // Синхронизиране на marketplace
                const syncButtons = document.querySelectorAll('.sync-marketplace');
                syncButtons.forEach(button => {
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.syncMarketplace(this.dataset.marketplace);
                    });
                });

                // Добавяне на webhook
                const addWebhookButton = document.getElementById('add-webhook');
                if (addWebhookButton) {
                    addWebhookButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.addWebhook();
                    });
                }

                // Премахване на webhook
                document.addEventListener('click', function(e) {
                    if (e.target.classList.contains('remove-webhook')) {
                        e.preventDefault();
                        self.removeWebhook(e.target.dataset.index);
                    }
                });

                // Тест на webhook
                document.addEventListener('click', function(e) {
                    if (e.target.classList.contains('test-webhook')) {
                        e.preventDefault();
                        self.testWebhook(e.target.dataset.index);
                    }
                });
            },

            /**
             * Запазване на настройки за интеграции
             */
            saveIntegrationSettings: function() {
                const self = this;
                const form = document.getElementById('integration-settings-form');

                if (!form) {
                    self.showSettingsNotification('Формата не е намерена', 'error');
                    return;
                }

                // Валидация преди запазване
                const validationErrors = self.validateIntegrationForm(form);
                if (validationErrors.length > 0) {
                    validationErrors.forEach(error => {
                        self.showSettingsNotification(error, 'error');
                    });
                    return;
                }

                const formData = new FormData(form);
                formData.append('user_token', self.settings.config.userToken);

                // Показване на loading състояние
                const saveButton = document.getElementById('save-integration-settings');
                if (saveButton) {
                    saveButton.disabled = true;
                    saveButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Запазване...';
                }

                fetch(self.settings.config.ajaxUrls.integrations_save || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');

                        // Показване на предупреждения ако има
                        if (data.warnings && data.warnings.length > 0) {
                            data.warnings.forEach(warning => {
                                setTimeout(() => {
                                    self.showSettingsNotification(warning.message, warning.type);
                                }, 1000);
                            });
                        }

                        // Актуализиране на данните ако са предоставени
                        if (data.updated_data) {
                            self.updateIntegrationSettingsDisplay(data.updated_data);
                        }
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');

                        // Показване на грешки при валидация
                        if (data.validation_errors) {
                            self.showValidationErrors(data.validation_errors);
                        }
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error saving integration settings:', error);
                    self.showSettingsNotification('Възникна грешка при запазването', 'error');
                })
                .finally(() => {
                    // Възстановяване на бутона
                    if (saveButton) {
                        saveButton.disabled = false;
                        saveButton.innerHTML = '<i class="ri-save-line mr-2"></i>Запази';
                    }
                });
            },

            /**
             * Валидация на формата за интеграции
             */
            validateIntegrationForm: function(form) {
                const errors = [];

                // Валидация на Google Analytics
                const gaEnabled = form.querySelector('[name="analytics_integrations[google_analytics][status]"]')?.checked;
                if (gaEnabled) {
                    const trackingId = form.querySelector('[name="analytics_integrations[google_analytics][tracking_id]"]')?.value || '';
                    if (!trackingId) {
                        errors.push('Google Analytics Tracking ID е задължителен');
                    } else if (!/^(UA|G)-[0-9A-Z-]+$/.test(trackingId)) {
                        errors.push('Невалиден формат на Google Analytics Tracking ID');
                    }
                }

                // Валидация на Facebook Pixel
                const fbEnabled = form.querySelector('[name="analytics_integrations[facebook_pixel][status]"]')?.checked;
                if (fbEnabled) {
                    const pixelId = form.querySelector('[name="analytics_integrations[facebook_pixel][pixel_id]"]')?.value || '';
                    if (!pixelId) {
                        errors.push('Facebook Pixel ID е задължителен');
                    } else if (!/^\d+$/.test(pixelId)) {
                        errors.push('Facebook Pixel ID трябва да бъде число');
                    }
                }

                // Валидация на активни webhooks
                const activeWebhooks = form.querySelectorAll('.webhook-item input[name*="[status]"]:checked');
                activeWebhooks.forEach((webhook, index) => {
                    const webhookContainer = webhook.closest('.webhook-item');
                    const urlField = webhookContainer.querySelector('input[name*="[url]"]');

                    if (!urlField || !urlField.value) {
                        errors.push(`Webhook ${index + 1}: URL е задължителен`);
                    } else if (!this.isValidUrl(urlField.value)) {
                        errors.push(`Webhook ${index + 1}: Невалиден URL формат`);
                    }
                });

                return errors;
            },

            /**
             * Toggle на интеграция
             */
            toggleIntegration: function(integrationCode, integrationType, enabled) {
                const self = this;

                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);
                formData.append('integration_code', integrationCode);
                formData.append('integration_type', integrationType);
                formData.append('enabled', enabled ? '1' : '0');

                fetch(self.settings.config.ajaxUrls.toggle_integration || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');

                        // Актуализиране на визуалното състояние
                        const integrationCard = document.querySelector(`[data-integration="${integrationCode}"][data-type="${integrationType}"]`);
                        if (integrationCard) {
                            const card = integrationCard.closest('.integration-card');
                            if (enabled) {
                                card.classList.remove('opacity-50');
                                card.classList.add('border-green-200', 'bg-green-50');
                            } else {
                                card.classList.add('opacity-50');
                                card.classList.remove('border-green-200', 'bg-green-50');
                            }
                        }
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');

                        // Връщане на toggle-а в предишното състояние
                        const toggle = document.querySelector(`[data-integration="${integrationCode}"][data-type="${integrationType}"].integration-toggle`);
                        if (toggle) {
                            toggle.checked = !enabled;
                        }
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error toggling integration:', error);
                    self.showSettingsNotification('Възникна грешка при промяната', 'error');
                });
            },

            /**
             * Тестване на интеграция
             */
            testIntegration: function(integrationCode, integrationType) {
                const self = this;
                const testButton = document.querySelector(`[data-integration="${integrationCode}"][data-type="${integrationType}"].test-integration`);

                if (testButton) {
                    testButton.disabled = true;
                    testButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Тестване...';
                }

                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);
                formData.append('integration_code', integrationCode);
                formData.append('integration_type', integrationType);

                fetch(self.settings.config.ajaxUrls.test_integration || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');
                    } else if (data.warning) {
                        self.showSettingsNotification(data.warning, 'warning');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error testing integration:', error);
                    self.showSettingsNotification('Възникна грешка при тестването', 'error');
                })
                .finally(() => {
                    if (testButton) {
                        testButton.disabled = false;
                        testButton.innerHTML = '<i class="ri-test-tube-line mr-2"></i>Тест';
                    }
                });
            },

            /**
             * Генериране на API ключ
             */
            generateApiKey: function(apiCode) {
                const self = this;
                const generateButton = document.querySelector(`[data-api="${apiCode}"].generate-api-key`);

                if (generateButton) {
                    generateButton.disabled = true;
                    generateButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Генериране...';
                }

                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);
                formData.append('api_code', apiCode);

                fetch(self.settings.config.ajaxUrls.generate_api_key || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');

                        // Актуализиране на полето с новия API ключ
                        if (data.api_key) {
                            const apiKeyField = document.querySelector(`[name="api_integrations[${apiCode}][api_key]"]`);
                            if (apiKeyField) {
                                apiKeyField.value = data.api_key;
                            }
                        }
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error generating API key:', error);
                    self.showSettingsNotification('Възникна грешка при генерирането', 'error');
                })
                .finally(() => {
                    if (generateButton) {
                        generateButton.disabled = false;
                        generateButton.innerHTML = '<i class="ri-key-line mr-2"></i>Генерирай';
                    }
                });
            },

            /**
             * Синхронизиране на marketplace
             */
            syncMarketplace: function(marketplaceCode) {
                const self = this;
                const syncButton = document.querySelector(`[data-marketplace="${marketplaceCode}"].sync-marketplace`);

                if (syncButton) {
                    syncButton.disabled = true;
                    syncButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Синхронизиране...';
                }

                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);
                formData.append('marketplace_code', marketplaceCode);

                fetch(self.settings.config.ajaxUrls.sync_marketplace || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');

                        // Показване на резултатите от синхронизацията
                        if (data.sync_results) {
                            self.showSyncResults(data.sync_results);
                        }
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error syncing marketplace:', error);
                    self.showSettingsNotification('Възникна грешка при синхронизирането', 'error');
                })
                .finally(() => {
                    if (syncButton) {
                        syncButton.disabled = false;
                        syncButton.innerHTML = '<i class="ri-refresh-line mr-2"></i>Синхронизирай';
                    }
                });
            },

            /**
             * Инициализиране на webhook manager
             */
            initWebhookManager: function() {
                // Инициализиране на webhook функционалност
                this.logDev && this.logDev('Webhook manager initialized');
            },

            /**
             * Добавяне на webhook
             */
            addWebhook: function() {
                const webhooksContainer = document.getElementById('webhooks-container');
                if (!webhooksContainer) return;

                const webhookIndex = webhooksContainer.children.length;
                const webhookHtml = this.createWebhookRow(webhookIndex);

                webhooksContainer.insertAdjacentHTML('beforeend', webhookHtml);

                this.showSettingsNotification('Добавен нов webhook', 'success');
            },

            /**
             * Премахване на webhook
             */
            removeWebhook: function(webhookIndex) {
                const webhookRow = document.getElementById(`webhook-${webhookIndex}`);
                if (webhookRow) {
                    webhookRow.remove();
                    this.showSettingsNotification('Webhook е премахнат', 'success');
                }
            },

            /**
             * Тестване на webhook
             */
            testWebhook: function(webhookIndex) {
                const self = this;
                const testButton = document.querySelector(`[data-index="${webhookIndex}"].test-webhook`);

                if (testButton) {
                    testButton.disabled = true;
                    testButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Тестване...';
                }

                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);
                formData.append('webhook_index', webhookIndex);

                fetch(self.settings.config.ajaxUrls.test_webhook || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error testing webhook:', error);
                    self.showSettingsNotification('Възникна грешка при тестването', 'error');
                })
                .finally(() => {
                    if (testButton) {
                        testButton.disabled = false;
                        testButton.innerHTML = '<i class="ri-test-tube-line mr-2"></i>Тест';
                    }
                });
            },

            /**
             * Създаване на ред за webhook
             */
            createWebhookRow: function(index) {
                return `
                    <div id="webhook-${index}" class="webhook-item border border-gray-200 rounded p-4">
                        <div class="grid grid-cols-2 gap-4 mb-3">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    Име на webhook
                                </label>
                                <input type="text"
                                       name="webhooks[${index}][name]"
                                       class="w-full px-3 py-2 border border-gray-300 rounded text-sm"
                                       placeholder="Име на webhook...">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    Събитие
                                </label>
                                <select name="webhooks[${index}][event]"
                                        class="w-full px-3 py-2 border border-gray-300 rounded text-sm">
                                    <option value="order.created">Нова поръчка</option>
                                    <option value="order.status_changed">Промяна в статус</option>
                                    <option value="customer.created">Нов клиент</option>
                                    <option value="product.updated">Актуализиран продукт</option>
                                </select>
                            </div>
                        </div>

                        <div class="grid grid-cols-3 gap-4 mb-3">
                            <div class="col-span-2">
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    URL
                                </label>
                                <input type="url"
                                       name="webhooks[${index}][url]"
                                       class="w-full px-3 py-2 border border-gray-300 rounded text-sm"
                                       placeholder="https://example.com/webhook">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    Метод
                                </label>
                                <select name="webhooks[${index}][method]"
                                        class="w-full px-3 py-2 border border-gray-300 rounded text-sm">
                                    <option value="POST">POST</option>
                                    <option value="PUT">PUT</option>
                                    <option value="PATCH">PATCH</option>
                                </select>
                            </div>
                        </div>

                        <div class="flex items-center justify-between">
                            <label class="flex items-center cursor-pointer">
                                <input type="checkbox"
                                       name="webhooks[${index}][status]"
                                       class="toggle-switch">
                                <span class="ml-2 text-sm text-gray-700">Активен</span>
                            </label>

                            <div class="flex space-x-2">
                                <button type="button"
                                        data-index="${index}"
                                        class="test-webhook px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600">
                                    <i class="ri-test-tube-line mr-1"></i>
                                    Тест
                                </button>
                                <button type="button"
                                        data-index="${index}"
                                        class="remove-webhook px-3 py-1 bg-red-500 text-white text-sm rounded hover:bg-red-600">
                                    <i class="ri-delete-bin-line mr-1"></i>
                                    Премахни
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            },

            /**
             * Показване на резултатите от синхронизацията
             */
            showSyncResults: function(results) {
                // Създаване на modal за резултатите
                const modal = document.createElement('div');
                modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                modal.innerHTML = `
                    <div class="bg-white rounded-lg max-w-lg w-full mx-4">
                        <div class="flex justify-between items-center p-4 border-b">
                            <h3 class="text-lg font-semibold">Резултати от синхронизацията</h3>
                            <button class="close-sync-results text-gray-500 hover:text-gray-700">
                                <i class="ri-close-line text-xl"></i>
                            </button>
                        </div>
                        <div class="p-4">
                            <div class="space-y-2">
                                <div class="flex justify-between">
                                    <span>Синхронизирани продукти:</span>
                                    <span class="font-medium text-green-600">${results.synced_products || 0}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>Актуализирани цени:</span>
                                    <span class="font-medium text-blue-600">${results.updated_prices || 0}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>Грешки:</span>
                                    <span class="font-medium text-red-600">${results.errors || 0}</span>
                                </div>
                            </div>
                        </div>
                        <div class="flex justify-end p-4 border-t">
                            <button class="close-sync-results px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
                                Затвори
                            </button>
                        </div>
                    </div>
                `;

                // Добавяне на modal към страницата
                document.body.appendChild(modal);

                // Затваряне на modal
                modal.querySelectorAll('.close-sync-results').forEach(button => {
                    button.addEventListener('click', () => {
                        document.body.removeChild(modal);
                    });
                });
            },

            /**
             * Актуализиране на показването на настройки за интеграции
             */
            updateIntegrationSettingsDisplay: function(data) {
                // Актуализиране на статистики ако има
                if (data.statistics) {
                    const activeAnalyticsElement = document.getElementById('active-analytics-count');
                    if (activeAnalyticsElement && data.statistics.active_analytics !== undefined) {
                        activeAnalyticsElement.textContent = data.statistics.active_analytics;
                    }

                    const apiCallsElement = document.getElementById('api-calls-today-count');
                    if (apiCallsElement && data.statistics.api_calls_today !== undefined) {
                        apiCallsElement.textContent = data.statistics.api_calls_today;
                    }
                }

                // Актуализиране на полетата с новите стойности
                Object.keys(data).forEach(key => {
                    if (key !== 'statistics') {
                        // Обработка на вложени обекти
                        if (typeof data[key] === 'object' && data[key] !== null) {
                            Object.keys(data[key]).forEach(subKey => {
                                if (typeof data[key][subKey] === 'object' && data[key][subKey] !== null) {
                                    Object.keys(data[key][subKey]).forEach(fieldKey => {
                                        const field = document.querySelector(`[name="${key}[${subKey}][${fieldKey}]"]`);
                                        if (field) {
                                            if (field.type === 'checkbox') {
                                                field.checked = !!data[key][subKey][fieldKey];
                                            } else {
                                                field.value = data[key][subKey][fieldKey] || '';
                                            }
                                        }
                                    });
                                }
                            });
                        }
                    }
                });
            },

            /**
             * Инициализиране на настройки за админ потребители
             */
            initAdminUsersSettings: function() {
                this.bindAdminUsersEvents();
                this.initUserTable();
                this.initUserGroupsTable();
                this.logDev && this.logDev('Admin users settings module initialized');
            },

            /**
             * Свързване на събития за настройки за админ потребители
             */
            bindAdminUsersEvents: function() {
                const self = this;

                // Форма за настройки за админ потребители
                const adminUsersForm = document.getElementById('admin-users-settings-form');
                if (adminUsersForm) {
                    adminUsersForm.addEventListener('submit', function(e) {
                        e.preventDefault();
                        self.saveAdminUsersSettings();
                    });
                }

                // Бутон за запазване
                const saveButton = document.getElementById('save-admin-users-settings');
                if (saveButton) {
                    saveButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.saveAdminUsersSettings();
                    });
                }

                // Добавяне на потребител
                const addUserButton = document.getElementById('add-admin-user');
                if (addUserButton) {
                    addUserButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.showAddUserModal();
                    });
                }

                // Добавяне на група потребители
                const addGroupButton = document.getElementById('add-user-group');
                if (addGroupButton) {
                    addGroupButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.showAddUserGroupModal();
                    });
                }

                // Действия с потребители
                document.addEventListener('click', function(e) {
                    if (e.target.classList.contains('edit-user')) {
                        e.preventDefault();
                        self.editUser(e.target.dataset.userId);
                    } else if (e.target.classList.contains('delete-user')) {
                        e.preventDefault();
                        self.deleteUser(e.target.dataset.userId);
                    } else if (e.target.classList.contains('toggle-user-status')) {
                        e.preventDefault();
                        self.toggleUserStatus(e.target.dataset.userId);
                    } else if (e.target.classList.contains('reset-user-password')) {
                        e.preventDefault();
                        self.resetUserPassword(e.target.dataset.userId);
                    } else if (e.target.classList.contains('unlock-user')) {
                        e.preventDefault();
                        self.unlockUser(e.target.dataset.userId);
                    }
                });

                // Действия с групи потребители
                document.addEventListener('click', function(e) {
                    if (e.target.classList.contains('edit-user-group')) {
                        e.preventDefault();
                        self.editUserGroup(e.target.dataset.groupId);
                    } else if (e.target.classList.contains('delete-user-group')) {
                        e.preventDefault();
                        self.deleteUserGroup(e.target.dataset.groupId);
                    } else if (e.target.classList.contains('manage-permissions')) {
                        e.preventDefault();
                        self.managePermissions(e.target.dataset.groupId);
                    }
                });

                // Тест на сигурност
                const testSecurityButton = document.getElementById('test-security-settings');
                if (testSecurityButton) {
                    testSecurityButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.testSecuritySettings();
                    });
                }

                // Експорт на логове
                const exportLogsButton = document.getElementById('export-activity-logs');
                if (exportLogsButton) {
                    exportLogsButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.exportActivityLogs();
                    });
                }

                // Изчистване на логове
                const clearLogsButton = document.getElementById('clear-activity-logs');
                if (clearLogsButton) {
                    clearLogsButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        self.clearActivityLogs();
                    });
                }
            },

            /**
             * Запазване на настройки за админ потребители
             */
            saveAdminUsersSettings: function() {
                const self = this;
                const form = document.getElementById('admin-users-settings-form');

                if (!form) {
                    self.showSettingsNotification('Формата не е намерена', 'error');
                    return;
                }

                // Валидация преди запазване
                const validationErrors = self.validateAdminUsersForm(form);
                if (validationErrors.length > 0) {
                    validationErrors.forEach(error => {
                        self.showSettingsNotification(error, 'error');
                    });
                    return;
                }

                const formData = new FormData(form);
                formData.append('user_token', self.settings.config.userToken);

                // Показване на loading състояние
                const saveButton = document.getElementById('save-admin-users-settings');
                if (saveButton) {
                    saveButton.disabled = true;
                    saveButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Запазване...';
                }

                fetch(self.settings.config.ajaxUrls.admin_users_save || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');

                        // Показване на предупреждения ако има
                        if (data.warnings && data.warnings.length > 0) {
                            data.warnings.forEach(warning => {
                                setTimeout(() => {
                                    self.showSettingsNotification(warning.message, warning.type);
                                }, 1000);
                            });
                        }

                        // Актуализиране на данните ако са предоставени
                        if (data.updated_data) {
                            self.updateAdminUsersSettingsDisplay(data.updated_data);
                        }
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');

                        // Показване на грешки при валидация
                        if (data.validation_errors) {
                            self.showValidationErrors(data.validation_errors);
                        }
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error saving admin users settings:', error);
                    self.showSettingsNotification('Възникна грешка при запазването', 'error');
                })
                .finally(() => {
                    // Възстановяване на бутона
                    if (saveButton) {
                        saveButton.disabled = false;
                        saveButton.innerHTML = '<i class="ri-save-line mr-2"></i>Запази';
                    }
                });
            },

            /**
             * Валидация на формата за админ потребители
             */
            validateAdminUsersForm: function(form) {
                const errors = [];

                // Валидация на настройки за сигурност
                const minLength = form.querySelector('[name="security_settings[password_min_length]"]')?.value;
                if (minLength && (parseInt(minLength) < 6 || parseInt(minLength) > 32)) {
                    errors.push('Минималната дължина на паролата трябва да бъде между 6 и 32 символа');
                }

                const maxAttempts = form.querySelector('[name="security_settings[max_login_attempts]"]')?.value;
                if (maxAttempts && (parseInt(maxAttempts) < 3 || parseInt(maxAttempts) > 20)) {
                    errors.push('Максималните опити за вход трябва да бъдат между 3 и 20');
                }

                const sessionTimeout = form.querySelector('[name="security_settings[session_timeout]"]')?.value;
                if (sessionTimeout && (parseInt(sessionTimeout) < 300 || parseInt(sessionTimeout) > 86400)) {
                    errors.push('Timeout на сесията трябва да бъде между 300 и 86400 секунди');
                }

                // Валидация на IP whitelist ако е активиран
                const ipWhitelistEnabled = form.querySelector('[name="security_settings[ip_whitelist_enabled]"]')?.checked;
                const ipWhitelist = form.querySelector('[name="security_settings[ip_whitelist]"]')?.value;

                if (ipWhitelistEnabled && !ipWhitelist) {
                    errors.push('IP whitelist е активиран, но няма зададени IP адреси');
                } else if (ipWhitelist) {
                    const ips = ipWhitelist.split(',');
                    for (let ip of ips) {
                        ip = ip.trim();
                        if (ip && !this.isValidIP(ip)) {
                            errors.push(`Невалиден IP адрес: ${ip}`);
                            break;
                        }
                    }
                }

                return errors;
            },

            /**
             * Инициализиране на таблицата с потребители
             */
            initUserTable: function() {
                // Инициализиране на функционалност за таблицата с потребители
                this.logDev && this.logDev('User table initialized');
            },

            /**
             * Инициализиране на таблицата с групи потребители
             */
            initUserGroupsTable: function() {
                // Инициализиране на функционалност за таблицата с групи
                this.logDev && this.logDev('User groups table initialized');
            },

            /**
             * Показване на modal за добавяне на потребител
             */
            showAddUserModal: function() {
                const self = this;

                // Създаване на modal за добавяне на потребител
                const modal = document.createElement('div');
                modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                modal.innerHTML = `
                    <div class="bg-white rounded-lg max-w-md w-full mx-4">
                        <div class="flex justify-between items-center p-4 border-b">
                            <h3 class="text-lg font-semibold">Добавяне на потребител</h3>
                            <button class="close-modal text-gray-500 hover:text-gray-700">
                                <i class="ri-close-line text-xl"></i>
                            </button>
                        </div>
                        <form id="add-user-form" class="p-4 space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    Потребителско име <span class="text-red-500">*</span>
                                </label>
                                <input type="text" name="username" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded"
                                       placeholder="Въведете потребителско име">
                            </div>

                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">
                                        Име <span class="text-red-500">*</span>
                                    </label>
                                    <input type="text" name="firstname" required
                                           class="w-full px-3 py-2 border border-gray-300 rounded"
                                           placeholder="Име">
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">
                                        Фамилия <span class="text-red-500">*</span>
                                    </label>
                                    <input type="text" name="lastname" required
                                           class="w-full px-3 py-2 border border-gray-300 rounded"
                                           placeholder="Фамилия">
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    Email <span class="text-red-500">*</span>
                                </label>
                                <input type="email" name="email" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded"
                                       placeholder="<EMAIL>">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    Парола <span class="text-red-500">*</span>
                                </label>
                                <input type="password" name="password" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded"
                                       placeholder="Въведете парола">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    Група потребители
                                </label>
                                <select name="user_group_id" required
                                        class="w-full px-3 py-2 border border-gray-300 rounded">
                                    <option value="">Изберете група</option>
                                    <option value="1">Администратори</option>
                                    <option value="2">Мениджъри</option>
                                    <option value="3">Оператори</option>
                                </select>
                            </div>

                            <div class="flex items-center">
                                <input type="checkbox" name="status" id="user-status" class="mr-2">
                                <label for="user-status" class="text-sm text-gray-700">Активен</label>
                            </div>
                        </form>
                        <div class="flex justify-end p-4 border-t space-x-2">
                            <button class="close-modal px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
                                Отказ
                            </button>
                            <button id="save-new-user" class="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90">
                                Запази
                            </button>
                        </div>
                    </div>
                `;

                // Добавяне на modal към страницата
                document.body.appendChild(modal);

                // Затваряне на modal
                modal.querySelectorAll('.close-modal').forEach(button => {
                    button.addEventListener('click', () => {
                        document.body.removeChild(modal);
                    });
                });

                // Запазване на нов потребител
                modal.querySelector('#save-new-user').addEventListener('click', () => {
                    self.saveNewUser(modal.querySelector('#add-user-form'), modal);
                });
            },

            /**
             * Запазване на нов потребител
             */
            saveNewUser: function(form, modal) {
                const self = this;
                const formData = new FormData(form);
                formData.append('user_token', self.settings.config.userToken);

                fetch(self.settings.config.ajaxUrls.add_user || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');
                        document.body.removeChild(modal);

                        // Презареждане на таблицата с потребители
                        self.refreshUsersTable();
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error adding user:', error);
                    self.showSettingsNotification('Възникна грешка при добавянето', 'error');
                });
            },

            /**
             * Редактиране на потребител
             */
            editUser: function(userId) {
                const self = this;

                // Тук ще се покаже modal за редактиране подобен на добавянето
                self.showSettingsNotification('Функционалността за редактиране ще бъде добавена скоро', 'info');
            },

            /**
             * Изтриване на потребител
             */
            deleteUser: function(userId) {
                const self = this;

                if (!self.showSettingsConfirm('Сигурни ли сте, че искате да изтриете този потребител?')) {
                    return;
                }

                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);
                formData.append('user_id', userId);

                fetch(self.settings.config.ajaxUrls.delete_user || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');
                        self.refreshUsersTable();
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error deleting user:', error);
                    self.showSettingsNotification('Възникна грешка при изтриването', 'error');
                });
            },

            /**
             * Toggle на статуса на потребител
             */
            toggleUserStatus: function(userId) {
                const self = this;

                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);
                formData.append('user_id', userId);

                fetch(self.settings.config.ajaxUrls.toggle_user_status || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');
                        self.refreshUsersTable();
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error toggling user status:', error);
                    self.showSettingsNotification('Възникна грешка при промяната', 'error');
                });
            },

            /**
             * Възстановяване на парола на потребител
             */
            resetUserPassword: function(userId) {
                const self = this;

                if (!self.showSettingsConfirm('Сигурни ли сте, че искате да възстановите паролата на този потребител?')) {
                    return;
                }

                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);
                formData.append('user_id', userId);

                fetch(self.settings.config.ajaxUrls.reset_password || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');

                        if (data.new_password) {
                            self.showPasswordModal(data.new_password);
                        }
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error resetting password:', error);
                    self.showSettingsNotification('Възникна грешка при възстановяването', 'error');
                });
            },

            /**
             * Отключване на потребител
             */
            unlockUser: function(userId) {
                const self = this;

                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);
                formData.append('user_id', userId);

                fetch(self.settings.config.ajaxUrls.unlock_user || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');
                        self.refreshUsersTable();
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error unlocking user:', error);
                    self.showSettingsNotification('Възникна грешка при отключването', 'error');
                });
            },

            /**
             * Показване на modal за добавяне на група потребители
             */
            showAddUserGroupModal: function() {
                const self = this;

                // Създаване на modal за добавяне на група
                const modal = document.createElement('div');
                modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                modal.innerHTML = `
                    <div class="bg-white rounded-lg max-w-md w-full mx-4">
                        <div class="flex justify-between items-center p-4 border-b">
                            <h3 class="text-lg font-semibold">Добавяне на група потребители</h3>
                            <button class="close-modal text-gray-500 hover:text-gray-700">
                                <i class="ri-close-line text-xl"></i>
                            </button>
                        </div>
                        <form id="add-group-form" class="p-4 space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    Име на групата <span class="text-red-500">*</span>
                                </label>
                                <input type="text" name="name" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded"
                                       placeholder="Въведете име на групата">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    Описание
                                </label>
                                <textarea name="description"
                                          class="w-full px-3 py-2 border border-gray-300 rounded"
                                          rows="3"
                                          placeholder="Описание на групата..."></textarea>
                            </div>
                        </form>
                        <div class="flex justify-end p-4 border-t space-x-2">
                            <button class="close-modal px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
                                Отказ
                            </button>
                            <button id="save-new-group" class="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90">
                                Запази
                            </button>
                        </div>
                    </div>
                `;

                // Добавяне на modal към страницата
                document.body.appendChild(modal);

                // Затваряне на modal
                modal.querySelectorAll('.close-modal').forEach(button => {
                    button.addEventListener('click', () => {
                        document.body.removeChild(modal);
                    });
                });

                // Запазване на нова група
                modal.querySelector('#save-new-group').addEventListener('click', () => {
                    self.saveNewUserGroup(modal.querySelector('#add-group-form'), modal);
                });
            },

            /**
             * Запазване на нова група потребители
             */
            saveNewUserGroup: function(form, modal) {
                const self = this;
                const formData = new FormData(form);
                formData.append('user_token', self.settings.config.userToken);

                fetch(self.settings.config.ajaxUrls.add_user_group || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');
                        document.body.removeChild(modal);

                        // Презареждане на таблицата с групи
                        self.refreshUserGroupsTable();
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error adding user group:', error);
                    self.showSettingsNotification('Възникна грешка при добавянето', 'error');
                });
            },

            /**
             * Редактиране на група потребители
             */
            editUserGroup: function(groupId) {
                const self = this;

                // Тук ще се покаже modal за редактиране подобен на добавянето
                self.showSettingsNotification('Функционалността за редактиране ще бъде добавена скоро', 'info');
            },

            /**
             * Изтриване на група потребители
             */
            deleteUserGroup: function(groupId) {
                const self = this;

                if (!self.showSettingsConfirm('Сигурни ли сте, че искате да изтриете тази група?')) {
                    return;
                }

                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);
                formData.append('group_id', groupId);

                fetch(self.settings.config.ajaxUrls.delete_user_group || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');
                        self.refreshUserGroupsTable();
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error deleting user group:', error);
                    self.showSettingsNotification('Възникна грешка при изтриването', 'error');
                });
            },

            /**
             * Управление на права на достъп
             */
            managePermissions: function(groupId) {
                const self = this;

                // Тук ще се покаже modal за управление на права
                self.showSettingsNotification('Функционалността за управление на права ще бъде добавена скоро', 'info');
            },

            /**
             * Тестване на настройки за сигурност
             */
            testSecuritySettings: function() {
                const self = this;
                const testButton = document.getElementById('test-security-settings');

                if (testButton) {
                    testButton.disabled = true;
                    testButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Тестване...';
                }

                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);

                fetch(self.settings.config.ajaxUrls.test_security || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error testing security settings:', error);
                    self.showSettingsNotification('Възникна грешка при тестването', 'error');
                })
                .finally(() => {
                    if (testButton) {
                        testButton.disabled = false;
                        testButton.innerHTML = '<i class="ri-shield-check-line mr-2"></i>Тест сигурност';
                    }
                });
            },

            /**
             * Експорт на логове за активност
             */
            exportActivityLogs: function() {
                const self = this;

                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);

                fetch(self.settings.config.ajaxUrls.export_activity_logs || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.blob())
                .then(blob => {
                    // Създаване на download link
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.style.display = 'none';
                    a.href = url;
                    a.download = 'activity_logs_' + new Date().toISOString().slice(0, 10) + '.csv';
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);

                    self.showSettingsNotification('Логовете са експортирани успешно', 'success');
                })
                .catch(error => {
                    self.logDev && self.logDev('Error exporting activity logs:', error);
                    self.showSettingsNotification('Възникна грешка при експортирането', 'error');
                });
            },

            /**
             * Изчистване на логове за активност
             */
            clearActivityLogs: function() {
                const self = this;

                if (!self.showSettingsConfirm('Сигурни ли сте, че искате да изчистите всички логове за активност?')) {
                    return;
                }

                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);

                fetch(self.settings.config.ajaxUrls.clear_activity_logs || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');
                        self.refreshActivityLogs();
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error clearing activity logs:', error);
                    self.showSettingsNotification('Възникна грешка при изчистването', 'error');
                });
            },

            /**
             * Презареждане на таблицата с потребители
             */
            refreshUsersTable: function() {
                // Презареждане на таблицата с потребители
                this.logDev && this.logDev('Refreshing users table');
                // Тук ще се имплементира презареждането на таблицата
            },

            /**
             * Презареждане на таблицата с групи потребители
             */
            refreshUserGroupsTable: function() {
                // Презареждане на таблицата с групи
                this.logDev && this.logDev('Refreshing user groups table');
                // Тук ще се имплементира презареждането на таблицата
            },

            /**
             * Презареждане на логовете за активност
             */
            refreshActivityLogs: function() {
                // Презареждане на логовете
                this.logDev && this.logDev('Refreshing activity logs');
                // Тук ще се имплементира презареждането на логовете
            },

            /**
             * Показване на modal с нова парола
             */
            showPasswordModal: function(newPassword) {
                const modal = document.createElement('div');
                modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                modal.innerHTML = `
                    <div class="bg-white rounded-lg max-w-md w-full mx-4">
                        <div class="flex justify-between items-center p-4 border-b">
                            <h3 class="text-lg font-semibold">Нова парола</h3>
                            <button class="close-modal text-gray-500 hover:text-gray-700">
                                <i class="ri-close-line text-xl"></i>
                            </button>
                        </div>
                        <div class="p-4">
                            <p class="text-gray-700 mb-4">Новата парола за потребителя е:</p>
                            <div class="bg-gray-100 p-3 rounded border">
                                <code class="text-lg font-mono">${newPassword}</code>
                            </div>
                            <p class="text-sm text-gray-500 mt-2">
                                Моля, запишете тази парола и я предайте на потребителя.
                            </p>
                        </div>
                        <div class="flex justify-end p-4 border-t">
                            <button class="close-modal px-4 py-2 bg-primary text-white rounded hover:bg-primary/90">
                                Разбрах
                            </button>
                        </div>
                    </div>
                `;

                // Добавяне на modal към страницата
                document.body.appendChild(modal);

                // Затваряне на modal
                modal.querySelectorAll('.close-modal').forEach(button => {
                    button.addEventListener('click', () => {
                        document.body.removeChild(modal);
                    });
                });
            },

            /**
             * Валидация на IP адрес
             */
            isValidIP: function(ip) {
                const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
                return ipRegex.test(ip);
            },

            /**
             * Актуализиране на показването на настройки за админ потребители
             */
            updateAdminUsersSettingsDisplay: function(data) {
                // Актуализиране на статистики ако има
                if (data.statistics) {
                    const totalUsersElement = document.getElementById('total-users-count');
                    if (totalUsersElement && data.statistics.total_users !== undefined) {
                        totalUsersElement.textContent = data.statistics.total_users;
                    }

                    const activeUsersElement = document.getElementById('active-users-count');
                    if (activeUsersElement && data.statistics.active_users !== undefined) {
                        activeUsersElement.textContent = data.statistics.active_users;
                    }

                    const onlineUsersElement = document.getElementById('online-users-count');
                    if (onlineUsersElement && data.statistics.online_users !== undefined) {
                        onlineUsersElement.textContent = data.statistics.online_users;
                    }
                }

                // Актуализиране на настройки за сигурност
                if (data.security_settings) {
                    Object.keys(data.security_settings).forEach(settingKey => {
                        const field = document.querySelector(`[name="security_settings[${settingKey}]"]`);
                        if (field) {
                            if (field.type === 'checkbox') {
                                field.checked = !!data.security_settings[settingKey];
                            } else {
                                field.value = data.security_settings[settingKey] || '';
                            }
                        }
                    });
                }

                // Актуализиране на потребители ако има промени
                if (data.users) {
                    // Тук ще се актуализира таблицата с потребители
                    this.refreshUsersTable();
                }

                // Актуализиране на групи потребители ако има промени
                if (data.user_groups) {
                    // Тук ще се актуализира таблицата с групи
                    this.refreshUserGroupsTable();
                }
            }

        });
    }

    // Image Manager функционалност за store logo
    function initStoreLogoImageManager() {
        // Инициализация на image manager за store logo
        document.addEventListener('click', function(e) {
            const target = e.target.closest('[data-action="select-store-logo"]');
            if (target) {
                e.preventDefault();
                openStoreLogoImageManager();
            }

            const removeTarget = e.target.closest('[data-action="remove-store-logo"]');
            if (removeTarget) {
                e.preventDefault();
                removeStoreLogo();
            }
        });
    }

    function openStoreLogoImageManager() {
        if (typeof BackendModule !== 'undefined' && BackendModule.openImageManager) {
            const targetContainer = document.getElementById('store-logo-container');

            BackendModule.openImageManager({
                singleSelection: true,
                startDirectory: 'catalog/',
                target: targetContainer,
                callback: (selectedImages, target) => {
                    if (selectedImages && selectedImages.length > 0) {
                        const image = selectedImages[0];
                        setStoreLogo(image.path, image.thumb);
                    }
                }
            });
        } else {
            console.error('BackendModule.openImageManager не е достъпен');
        }
    }

    function setStoreLogo(imagePath, thumbPath) {
        // Актуализиране на скритото поле
        const hiddenInput = document.getElementById('input-store-logo');
        if (hiddenInput) {
            hiddenInput.value = imagePath;
        }

        // Актуализиране на контейнера
        const container = document.getElementById('store-logo-container');
        if (container) {
            // Създаване на нов HTML за показване на изображението
            const imageHtml = `
                <div class="relative group">
                    <div class="aspect-auto rounded-lg overflow-hidden border border-gray-200" style="width: 192px; height: auto; min-height: 96px;">
                        <img src="${thumbPath || imagePath}" alt="Лого на магазина" id="store-logo-preview" class="w-full h-full object-contain">
                    </div>
                    <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                        <button type="button" class="p-2 bg-white rounded-full text-gray-700 hover:text-primary" title="Промени лого" data-action="select-store-logo">
                            <div class="w-5 h-5 flex items-center justify-center">
                                <i class="ri-folder-image-line"></i>
                            </div>
                        </button>
                        <button type="button" class="p-2 bg-white rounded-full text-red-500 hover:text-red-600" title="Премахни лого" data-action="remove-store-logo">
                            <div class="w-5 h-5 flex items-center justify-center">
                                <i class="ri-delete-bin-line"></i>
                            </div>
                        </button>
                    </div>
                </div>
            `;
            container.innerHTML = imageHtml;
        }

        console.log('Store logo set:', imagePath);
    }

    function removeStoreLogo() {
        // Изчистване на скритото поле
        const hiddenInput = document.getElementById('input-store-logo');
        if (hiddenInput) {
            hiddenInput.value = '';
        }

        // Актуализиране на контейнера с placeholder
        const container = document.getElementById('store-logo-container');
        if (container) {
            const placeholderHtml = `
                <div class="border border-dashed border-gray-300 rounded-lg p-4 flex flex-col items-center justify-center cursor-pointer hover:border-primary hover:bg-primary/5 transition-colors" style="width: 192px; height: 96px;">
                    <div class="flex items-center space-x-2 mb-2">
                        <button type="button" data-action="select-store-logo" class="w-8 h-8 flex items-center justify-center text-gray-400 hover:text-primary transition-colors" title="Избери от библиотеката">
                            <i class="ri-folder-image-line ri-lg"></i>
                        </button>
                    </div>
                    <p class="text-xs text-gray-400 text-center">Няма лого</p>
                </div>
            `;
            container.innerHTML = placeholderHtml;
        }

        console.log('Store logo removed');
    }

    // Инициализация при зареждане на DOM
    document.addEventListener('DOMContentLoaded', function() {
        initSettingsModule();
        initStoreLogoImageManager();
    });

})();
