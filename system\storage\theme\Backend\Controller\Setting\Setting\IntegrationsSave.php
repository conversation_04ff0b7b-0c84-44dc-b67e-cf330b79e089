<?php

namespace Theme25\Backend\Controller\Setting\Setting;

/**
 * Sub-контролер за AJAX запазване на настройки за интеграции
 */
class IntegrationsSave extends \Theme25\ControllerSubMethods {

    /**
     * Изпълнява AJAX запазване на настройки за интеграции
     */
    public function execute() {
        $json = [];

        // Проверка за права на достъп
        if (!$this->hasPermission('modify', 'setting/setting')) {
            $json['error'] = 'Нямате права за редактиране на настройки за интеграции';
            $this->outputJson($json);
            return;
        }

        // Проверка за POST заявка
        if (!$this->isPostRequest()) {
            $json['error'] = 'Невалидна заявка';
            $this->outputJson($json);
            return;
        }

        try {
            // Получаване на данните от POST
            $postData = $this->requestPost();
            
            // Валидация на данните
            $validation_errors = $this->validateIntegrationSettings($postData);
            if (!empty($validation_errors)) {
                $json['error'] = 'Грешки при валидацията';
                $json['validation_errors'] = $validation_errors;
                $this->outputJson($json);
                return;
            }

            $this->loadModelAs('setting/integrations_settings_model', 'integrationsSettings');
            
            // Запазване на настройките
            $result = $this->integrationsSettings->saveIntegrationSettings($postData);
            
            if ($result === true) {
                $json['success'] = 'Настройките за интеграции са запазени успешно';
                $json['timestamp'] = date('d.m.Y H:i:s');
                
                // Добавяне на актуализирани данни
                $json['updated_data'] = $this->getUpdatedIntegrationData($postData);
                
                // Предупреждения ако има
                $warnings = $this->checkIntegrationWarnings($postData);
                if (!empty($warnings)) {
                    $json['warnings'] = $warnings;
                }
            } else {
                // Има грешки при валидацията от модела
                $json['error'] = 'Грешки при валидацията';
                $json['validation_errors'] = $result;
            }

        } catch (Exception $e) {
            $json['error'] = 'Грешка при запазване: ' . $e->getMessage();
        }

        $this->outputJson($json);
    }

    /**
     * Валидира настройките за интеграции
     *
     * @param array $data Данни за валидация
     * @return array Масив с грешки
     */
    private function validateIntegrationSettings($data) {
        $errors = [];

        // Валидация на Google Analytics
        if (!empty($data['analytics_integrations']['google_analytics']['status'])) {
            $tracking_id = $data['analytics_integrations']['google_analytics']['tracking_id'] ?? '';
            if (empty($tracking_id)) {
                $errors['analytics_integrations']['google_analytics']['tracking_id'] = 'Tracking ID е задължителен при активиран Google Analytics';
            } elseif (!preg_match('/^(UA|G)-[0-9A-Z-]+$/', $tracking_id)) {
                $errors['analytics_integrations']['google_analytics']['tracking_id'] = 'Невалиден формат на Tracking ID';
            }
        }

        // Валидация на Google Tag Manager
        if (!empty($data['analytics_integrations']['google_tag_manager']['status'])) {
            $container_id = $data['analytics_integrations']['google_tag_manager']['container_id'] ?? '';
            if (empty($container_id)) {
                $errors['analytics_integrations']['google_tag_manager']['container_id'] = 'Container ID е задължителен при активиран GTM';
            } elseif (!preg_match('/^GTM-[0-9A-Z]+$/', $container_id)) {
                $errors['analytics_integrations']['google_tag_manager']['container_id'] = 'Невалиден формат на Container ID';
            }
        }

        // Валидация на Facebook Pixel
        if (!empty($data['analytics_integrations']['facebook_pixel']['status'])) {
            $pixel_id = $data['analytics_integrations']['facebook_pixel']['pixel_id'] ?? '';
            if (empty($pixel_id)) {
                $errors['analytics_integrations']['facebook_pixel']['pixel_id'] = 'Pixel ID е задължителен при активиран Facebook Pixel';
            } elseif (!is_numeric($pixel_id)) {
                $errors['analytics_integrations']['facebook_pixel']['pixel_id'] = 'Pixel ID трябва да бъде число';
            }
        }

        // Валидация на API интеграции
        if (isset($data['api_integrations']) && is_array($data['api_integrations'])) {
            foreach ($data['api_integrations'] as $api_code => $api_data) {
                if (!empty($api_data['status'])) {
                    // Проверка за API ключ
                    if (empty($api_data['api_key'])) {
                        $errors['api_integrations'][$api_code]['api_key'] = 'API ключът е задължителен при активиран API';
                    }
                    
                    // Валидация на rate limit
                    if (isset($api_data['rate_limit'])) {
                        $rate_limit = (int)$api_data['rate_limit'];
                        if ($rate_limit < 1 || $rate_limit > 10000) {
                            $errors['api_integrations'][$api_code]['rate_limit'] = 'Rate limit трябва да бъде между 1 и 10000';
                        }
                    }
                    
                    // Валидация на timeout за webhooks
                    if ($api_code === 'webhook_api' && isset($api_data['timeout'])) {
                        $timeout = (int)$api_data['timeout'];
                        if ($timeout < 5 || $timeout > 300) {
                            $errors['api_integrations'][$api_code]['timeout'] = 'Timeout трябва да бъде между 5 и 300 секунди';
                        }
                    }
                }
            }
        }

        // Валидация на webhooks
        if (isset($data['webhooks']) && is_array($data['webhooks'])) {
            foreach ($data['webhooks'] as $webhook_index => $webhook) {
                if (!empty($webhook['status'])) {
                    if (empty($webhook['url'])) {
                        $errors['webhooks'][$webhook_index]['url'] = 'URL е задължителен при активиран webhook';
                    } elseif (!filter_var($webhook['url'], FILTER_VALIDATE_URL)) {
                        $errors['webhooks'][$webhook_index]['url'] = 'Невалиден URL формат';
                    }
                    
                    if (empty($webhook['method'])) {
                        $errors['webhooks'][$webhook_index]['method'] = 'HTTP методът е задължителен';
                    } elseif (!in_array($webhook['method'], ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'])) {
                        $errors['webhooks'][$webhook_index]['method'] = 'Невалиден HTTP метод';
                    }
                }
            }
        }

        // Валидация на social media интеграции
        if (isset($data['social_integrations']) && is_array($data['social_integrations'])) {
            foreach ($data['social_integrations'] as $social_code => $social_data) {
                if (!empty($social_data['status'])) {
                    // Facebook Login валидация
                    if ($social_code === 'facebook_login') {
                        if (empty($social_data['app_id'])) {
                            $errors['social_integrations'][$social_code]['app_id'] = 'App ID е задължителен за Facebook Login';
                        }
                        if (empty($social_data['app_secret'])) {
                            $errors['social_integrations'][$social_code]['app_secret'] = 'App Secret е задължителен за Facebook Login';
                        }
                    }
                    
                    // Google Login валидация
                    if ($social_code === 'google_login') {
                        if (empty($social_data['client_id'])) {
                            $errors['social_integrations'][$social_code]['client_id'] = 'Client ID е задължителен за Google Login';
                        }
                        if (empty($social_data['client_secret'])) {
                            $errors['social_integrations'][$social_code]['client_secret'] = 'Client Secret е задължителен за Google Login';
                        }
                    }
                }
            }
        }

        // Валидация на marketplace интеграции
        if (isset($data['marketplaces']) && is_array($data['marketplaces'])) {
            foreach ($data['marketplaces'] as $marketplace_code => $marketplace_data) {
                if (!empty($marketplace_data['status'])) {
                    // Amazon валидация
                    if ($marketplace_code === 'amazon') {
                        if (empty($marketplace_data['seller_id'])) {
                            $errors['marketplaces'][$marketplace_code]['seller_id'] = 'Seller ID е задължителен за Amazon';
                        }
                        if (empty($marketplace_data['access_key'])) {
                            $errors['marketplaces'][$marketplace_code]['access_key'] = 'Access Key е задължителен за Amazon';
                        }
                        if (empty($marketplace_data['secret_key'])) {
                            $errors['marketplaces'][$marketplace_code]['secret_key'] = 'Secret Key е задължителен за Amazon';
                        }
                    }
                    
                    // eBay валидация
                    if ($marketplace_code === 'ebay') {
                        if (empty($marketplace_data['app_id'])) {
                            $errors['marketplaces'][$marketplace_code]['app_id'] = 'App ID е задължителен за eBay';
                        }
                        if (empty($marketplace_data['dev_id'])) {
                            $errors['marketplaces'][$marketplace_code]['dev_id'] = 'Dev ID е задължителен за eBay';
                        }
                        if (empty($marketplace_data['cert_id'])) {
                            $errors['marketplaces'][$marketplace_code]['cert_id'] = 'Cert ID е задължителен за eBay';
                        }
                    }
                }
            }
        }

        return $errors;
    }

    /**
     * Проверява за предупреждения относно интеграциите
     *
     * @param array $data Данни за проверка
     * @return array Масив с предупреждения
     */
    private function checkIntegrationWarnings($data) {
        $warnings = [];

        // Проверка за analytics интеграции
        $active_analytics = 0;
        if (isset($data['analytics_integrations']) && is_array($data['analytics_integrations'])) {
            foreach ($data['analytics_integrations'] as $analytics_data) {
                if (!empty($analytics_data['status'])) {
                    $active_analytics++;
                }
            }
        }

        if ($active_analytics === 0) {
            $warnings[] = [
                'type' => 'info',
                'message' => 'Няма активни analytics интеграции. Препоръчваме да активирате Google Analytics за проследяване на посетителите.'
            ];
        }

        // Проверка за API rate limits
        if (isset($data['api_integrations']) && is_array($data['api_integrations'])) {
            foreach ($data['api_integrations'] as $api_code => $api_data) {
                if (!empty($api_data['status']) && isset($api_data['rate_limit'])) {
                    $rate_limit = (int)$api_data['rate_limit'];
                    if ($rate_limit > 5000) {
                        $warnings[] = [
                            'type' => 'warning',
                            'message' => 'Високият rate limit за ' . $api_code . ' може да натовари сървъра'
                        ];
                    }
                }
            }
        }

        // Проверка за webhooks без SSL
        if (isset($data['webhooks']) && is_array($data['webhooks'])) {
            foreach ($data['webhooks'] as $webhook) {
                if (!empty($webhook['status']) && !empty($webhook['url'])) {
                    if (strpos($webhook['url'], 'https://') !== 0) {
                        $warnings[] = [
                            'type' => 'warning',
                            'message' => 'Webhook "' . $webhook['name'] . '" не използва HTTPS. Препоръчваме SSL за сигурност.'
                        ];
                    }
                }
            }
        }

        // Проверка за marketplace интеграции
        $active_marketplaces = 0;
        if (isset($data['marketplaces']) && is_array($data['marketplaces'])) {
            foreach ($data['marketplaces'] as $marketplace_data) {
                if (!empty($marketplace_data['status'])) {
                    $active_marketplaces++;
                }
            }
        }

        if ($active_marketplaces > 0) {
            $warnings[] = [
                'type' => 'info',
                'message' => 'Активирани са ' . $active_marketplaces . ' marketplace интеграции. Не забравяйте да синхронизирате продуктите редовно.'
            ];
        }

        return $warnings;
    }

    /**
     * Получава актуализираните данни за настройките за интеграции
     *
     * @param array $postData POST данни
     * @return array
     */
    private function getUpdatedIntegrationData($postData) {
        $updated_data = [];

        // Analytics интеграции
        if (isset($postData['analytics_integrations']) && is_array($postData['analytics_integrations'])) {
            $updated_data['analytics_integrations'] = [];
            foreach ($postData['analytics_integrations'] as $analytics_code => $analytics_data) {
                $updated_data['analytics_integrations'][$analytics_code] = [
                    'status' => isset($analytics_data['status']) ? 1 : 0
                ];
                
                // Специфични полета за всяка интеграция
                switch ($analytics_code) {
                    case 'google_analytics':
                        $updated_data['analytics_integrations'][$analytics_code]['tracking_id'] = $analytics_data['tracking_id'] ?? '';
                        $updated_data['analytics_integrations'][$analytics_code]['enhanced_ecommerce'] = isset($analytics_data['enhanced_ecommerce']) ? 1 : 0;
                        $updated_data['analytics_integrations'][$analytics_code]['anonymize_ip'] = isset($analytics_data['anonymize_ip']) ? 1 : 0;
                        break;
                    case 'google_tag_manager':
                        $updated_data['analytics_integrations'][$analytics_code]['container_id'] = $analytics_data['container_id'] ?? '';
                        break;
                    case 'facebook_pixel':
                        $updated_data['analytics_integrations'][$analytics_code]['pixel_id'] = $analytics_data['pixel_id'] ?? '';
                        $updated_data['analytics_integrations'][$analytics_code]['advanced_matching'] = isset($analytics_data['advanced_matching']) ? 1 : 0;
                        break;
                    case 'hotjar':
                        $updated_data['analytics_integrations'][$analytics_code]['site_id'] = $analytics_data['site_id'] ?? '';
                        break;
                }
            }
        }

        // Social media интеграции
        if (isset($postData['social_integrations']) && is_array($postData['social_integrations'])) {
            $updated_data['social_integrations'] = [];
            foreach ($postData['social_integrations'] as $social_code => $social_data) {
                $updated_data['social_integrations'][$social_code] = [
                    'status' => isset($social_data['status']) ? 1 : 0
                ];
                
                // Добавяне на специфични полета
                foreach (['app_id', 'app_secret', 'client_id', 'client_secret', 'access_token', 'user_id', 'channel_id', 'api_key'] as $field) {
                    if (isset($social_data[$field])) {
                        $updated_data['social_integrations'][$social_code][$field] = $social_data[$field];
                    }
                }
            }
        }

        // API интеграции
        if (isset($postData['api_integrations']) && is_array($postData['api_integrations'])) {
            $updated_data['api_integrations'] = [];
            foreach ($postData['api_integrations'] as $api_code => $api_data) {
                $updated_data['api_integrations'][$api_code] = [
                    'status' => isset($api_data['status']) ? 1 : 0,
                    'api_key' => $api_data['api_key'] ?? '',
                    'secret_key' => $api_data['secret_key'] ?? '',
                    'rate_limit' => (int)($api_data['rate_limit'] ?? 1000),
                    'timeout' => (int)($api_data['timeout'] ?? 30),
                    'retry_attempts' => (int)($api_data['retry_attempts'] ?? 3),
                    'allowed_ips' => $api_data['allowed_ips'] ?? '',
                    'version' => $api_data['version'] ?? 'v1'
                ];
            }
        }

        // Webhooks
        if (isset($postData['webhooks']) && is_array($postData['webhooks'])) {
            $updated_data['webhooks'] = [];
            foreach ($postData['webhooks'] as $webhook_index => $webhook) {
                $updated_data['webhooks'][$webhook_index] = [
                    'name' => $webhook['name'] ?? '',
                    'event' => $webhook['event'] ?? '',
                    'url' => $webhook['url'] ?? '',
                    'method' => $webhook['method'] ?? 'POST',
                    'status' => isset($webhook['status']) ? 1 : 0,
                    'headers' => $webhook['headers'] ?? '{"Content-Type": "application/json"}'
                ];
            }
        }

        // Marketplace интеграции
        if (isset($postData['marketplaces']) && is_array($postData['marketplaces'])) {
            $updated_data['marketplaces'] = [];
            foreach ($postData['marketplaces'] as $marketplace_code => $marketplace_data) {
                $updated_data['marketplaces'][$marketplace_code] = [
                    'status' => isset($marketplace_data['status']) ? 1 : 0
                ];
                
                // Добавяне на специфични полета
                foreach (['seller_id', 'access_key', 'secret_key', 'marketplace_id', 'app_id', 'dev_id', 'cert_id', 'token', 'shared_secret', 'shop_id'] as $field) {
                    if (isset($marketplace_data[$field])) {
                        $updated_data['marketplaces'][$marketplace_code][$field] = $marketplace_data[$field];
                    }
                }
            }
        }

        return $updated_data;
    }

    /**
     * Изпраща JSON отговор
     *
     * @param array $data
     */
    private function outputJson($data) {
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($data));
    }

    /**
     * Логира действието за запазване на настройки за интеграции
     *
     * @param array $data Запазените данни
     */
    private function logIntegrationSettingsSave($data) {
        try {
            $log_data = [
                'action' => 'integration_settings_save',
                'user_id' => $this->user->getId(),
                'user_name' => $this->user->getUserName(),
                'timestamp' => date('Y-m-d H:i:s'),
                'ip_address' => $this->request->server['REMOTE_ADDR'] ?? 'unknown',
                'changed_fields' => array_keys($data)
            ];
            
            // Логиране в системния лог
            $this->log->write('Integration Settings: Updated by user ' . $this->user->getUserName() . ' (' . $this->user->getId() . ')');
            
        } catch (Exception $e) {
            // Ако логирането се провали, не спираме операцията
            error_log('Failed to log integration settings save: ' . $e->getMessage());
        }
    }
}
