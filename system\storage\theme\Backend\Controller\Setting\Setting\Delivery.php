<?php

namespace Theme25\Backend\Controller\Setting\Setting;

/**
 * Sub-контролер за настройки за доставка
 *
 * Този контролер управлява логиката за показване и обработка на настройките за доставка,
 * включително shipping методи, зони за доставка, цени и други настройки свързани с доставката.
 *
 * @package Theme25\Backend\Controller\Setting\Setting
 * <AUTHOR> Development Team
 * @version 1.0.0
 */
class Delivery extends \Theme25\ControllerSubMethods {

    /**
     * Подготвя данните за настройките за доставка
     */
    public function prepareData() {
        $this->prepareShippingMethodsData()
             ->prepareDeliveryZonesData()
             ->prepareDeliverySettingsData()
             ->prepareShippingRatesData()
             ->prepareUrlsAndActions()
             ->prepareValidationRules();

        // Подготвяне на JavaScript конфигурация
        $this->setData([
            'user_token' => $this->getUserToken()
        ]);
    }

    /**
     * Подготвя данните за shipping методи
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareShippingMethodsData() {
        try {
            $this->loadModelAs('setting/delivery_settings_model', 'deliverySettings');
            $shipping_methods = $this->deliverySettings->getAvailableShippingMethods();
            
            $this->setData('shipping_methods', $shipping_methods);
            
        } catch (Exception $e) {
            $this->setError('Грешка при зареждане на shipping методи: ' . $e->getMessage());
            
            // Задаване на стойности по подразбиране
            $this->setData('shipping_methods', [
                'flat' => [
                    'name' => 'Фиксирана цена',
                    'code' => 'flat',
                    'status' => 1,
                    'sort_order' => 1,
                    'icon' => 'ri-truck-line',
                    'description' => 'Фиксирана цена за доставка',
                    'cost' => 5.00
                ],
                'free' => [
                    'name' => 'Безплатна доставка',
                    'code' => 'free',
                    'status' => 1,
                    'sort_order' => 2,
                    'icon' => 'ri-gift-line',
                    'description' => 'Безплатна доставка при определена сума',
                    'minimum_total' => 50.00
                ],
                'pickup' => [
                    'name' => 'Вземане от офис',
                    'code' => 'pickup',
                    'status' => 1,
                    'sort_order' => 3,
                    'icon' => 'ri-store-line',
                    'description' => 'Вземане от офиса на магазина',
                    'cost' => 0.00
                ],
                'weight' => [
                    'name' => 'По тегло',
                    'code' => 'weight',
                    'status' => 0,
                    'sort_order' => 4,
                    'icon' => 'ri-scales-line',
                    'description' => 'Цена според теглото на поръчката',
                    'rates' => []
                ]
            ]);
        }

        return $this;
    }

    /**
     * Подготвя данните за зони за доставка
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareDeliveryZonesData() {
        try {
            $this->loadModelAs('localisation/geo_zone', 'geoZoneModel');
            $geo_zones = $this->geoZoneModel->getGeoZones();
            
            $this->loadModelAs('localisation/country', 'countryModel');
            $countries = $this->countryModel->getCountries();
            
            $this->setData([
                'geo_zones' => $geo_zones,
                'countries' => $countries
            ]);
            
        } catch (Exception $e) {
            // Fallback данни
            $this->setData([
                'geo_zones' => [
                    ['geo_zone_id' => 1, 'name' => 'България', 'description' => 'Територия на България'],
                    ['geo_zone_id' => 2, 'name' => 'ЕС', 'description' => 'Европейски съюз'],
                    ['geo_zone_id' => 3, 'name' => 'Световно', 'description' => 'Останалия свят']
                ],
                'countries' => [
                    ['country_id' => 1, 'name' => 'България', 'iso_code_2' => 'BG'],
                    ['country_id' => 2, 'name' => 'Германия', 'iso_code_2' => 'DE'],
                    ['country_id' => 3, 'name' => 'Франция', 'iso_code_2' => 'FR']
                ]
            ]);
        }

        return $this;
    }

    /**
     * Подготвя основните настройки за доставка
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareDeliverySettingsData() {
        try {
            $this->loadModelAs('setting/delivery_settings_model', 'deliverySettings');
            $settings = $this->deliverySettings->getDeliverySettings();
            
            // Добавяне на настройките към данните
            foreach ($settings as $key => $value) {
                $this->setData($key, $value);
            }
            
        } catch (Exception $e) {
            $this->setError('Грешка при зареждане на настройки за доставка: ' . $e->getMessage());
            
            // Задаване на стойности по подразбиране
            $this->setData([
                'delivery_terms' => '',
                'delivery_info' => '',
                'delivery_time_min' => 1,
                'delivery_time_max' => 3,
                'delivery_weight_class' => 1,
                'delivery_length_class' => 1,
                'delivery_tax_class' => 0,
                'delivery_auto_calculate' => 1,
                'delivery_display_weight' => 1,
                'delivery_display_time' => 1,
                'delivery_sort_order' => 1,
                'delivery_status' => 1
            ]);
        }

        return $this;
    }

    /**
     * Подготвя данните за цени на доставка
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareShippingRatesData() {
        // Типове изчисляване на цени
        $rate_calculation_types = [
            'fixed' => 'Фиксирана цена',
            'weight' => 'По тегло',
            'total' => 'По стойност на поръчката',
            'item' => 'По брой артикули',
            'distance' => 'По разстояние'
        ];

        $this->setData('rate_calculation_types', $rate_calculation_types);

        // Единици за тегло
        $weight_classes = [
            1 => 'Килограми (kg)',
            2 => 'Грамове (g)',
            3 => 'Паундове (lb)',
            4 => 'Унции (oz)'
        ];

        $this->setData('weight_classes', $weight_classes);

        // Единици за дължина
        $length_classes = [
            1 => 'Сантиметри (cm)',
            2 => 'Милиметри (mm)',
            3 => 'Инчове (in)',
            4 => 'Футове (ft)'
        ];

        $this->setData('length_classes', $length_classes);

        return $this;
    }

    /**
     * Подготвя URL-и за AJAX заявки и действия
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareUrlsAndActions() {
        $urls = [
            'save_url' => $this->getAdminLink('setting/setting/delivery_save'),
            'test_shipping_url' => $this->getAdminLink('setting/setting/test_shipping_method'),
            'calculate_shipping_url' => $this->getAdminLink('setting/setting/calculate_shipping'),
            'toggle_shipping_method_url' => $this->getAdminLink('setting/setting/toggle_shipping_method'),
            'reorder_shipping_methods_url' => $this->getAdminLink('setting/setting/reorder_shipping_methods'),
            'add_shipping_rate_url' => $this->getAdminLink('setting/setting/add_shipping_rate'),
            'remove_shipping_rate_url' => $this->getAdminLink('setting/setting/remove_shipping_rate'),
            'export_delivery_settings_url' => $this->getAdminLink('setting/setting/export_delivery_settings')
        ];

        foreach ($urls as $key => $url) {
            $this->setData($key, $url);
        }

        return $this;
    }

    /**
     * Подготвя правила за валидация
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareValidationRules() {
        $validation_rules = [
            'delivery_time_min' => [
                'required' => true,
                'type' => 'number',
                'min' => 0
            ],
            'delivery_time_max' => [
                'required' => true,
                'type' => 'number',
                'min' => 1
            ],
            'delivery_terms' => [
                'required' => false,
                'max_length' => 1000,
                'type' => 'textarea'
            ],
            'delivery_info' => [
                'required' => false,
                'max_length' => 1000,
                'type' => 'textarea'
            ]
        ];

        $this->setData('validation_rules', $validation_rules);

        return $this;
    }

    /**
     * Получава статистики за доставката
     *
     * @return array
     */
    private function getDeliveryStatistics() {
        try {
            $this->loadModelAs('setting/delivery_settings_model', 'deliveryModel');
            
            return [
                'active_shipping_methods' => $this->deliveryModel->getActiveShippingMethodsCount(),
                'total_shipping_methods' => $this->deliveryModel->getTotalShippingMethodsCount(),
                'configured_zones' => $this->deliveryModel->getConfiguredZonesCount(),
                'average_delivery_time' => $this->deliveryModel->getAverageDeliveryTime()
            ];
            
        } catch (Exception $e) {
            return [
                'active_shipping_methods' => 3,
                'total_shipping_methods' => 4,
                'configured_zones' => 3,
                'average_delivery_time' => '1-3 дни'
            ];
        }
    }

    /**
     * Проверява дали има предупреждения за доставката
     *
     * @return array
     */
    private function getDeliveryWarnings() {
        $warnings = [];
        $settings = $this->getData();
        
        // Проверка за липсващи активни shipping методи
        $shipping_methods = $this->getData('shipping_methods') ?: [];
        $active_methods = array_filter($shipping_methods, function($method) {
            return !empty($method['status']);
        });
        
        if (empty($active_methods)) {
            $warnings[] = [
                'type' => 'error',
                'message' => 'Няма активни методи за доставка! Клиентите няма да могат да завършат поръчки.'
            ];
        }
        
        // Проверка за време за доставка
        if (isset($settings['delivery_time_min']) && isset($settings['delivery_time_max'])) {
            if ($settings['delivery_time_max'] <= $settings['delivery_time_min']) {
                $warnings[] = [
                    'type' => 'warning',
                    'message' => 'Максималното време за доставка трябва да бъде по-голямо от минималното'
                ];
            }
        }
        
        // Проверка за липсващи зони
        $geo_zones = $this->getData('geo_zones') ?: [];
        if (empty($geo_zones)) {
            $warnings[] = [
                'type' => 'info',
                'message' => 'Не са конфигурирани зони за доставка'
            ];
        }
        
        return $warnings;
    }

    /**
     * Получава конфигурацията за JavaScript модула
     *
     * @return array
     */
    public function getJavaScriptConfig() {
        return [
            'userToken' => $this->getUserToken(),
            'urls' => [
                'save' => $this->getData('save_url'),
                'testShipping' => $this->getData('test_shipping_url'),
                'calculateShipping' => $this->getData('calculate_shipping_url'),
                'toggleShippingMethod' => $this->getData('toggle_shipping_method_url'),
                'reorderShippingMethods' => $this->getData('reorder_shipping_methods_url'),
                'addShippingRate' => $this->getData('add_shipping_rate_url'),
                'removeShippingRate' => $this->getData('remove_shipping_rate_url'),
                'exportSettings' => $this->getData('export_delivery_settings_url')
            ],
            'validationRules' => $this->getData('validation_rules'),
            'rateCalculationTypes' => $this->getData('rate_calculation_types'),
            'weightClasses' => $this->getData('weight_classes'),
            'lengthClasses' => $this->getData('length_classes'),
            'statistics' => $this->getDeliveryStatistics()
        ];
    }
}
