<?php

namespace Theme25\Backend\Controller\Setting;

class Setting extends \Theme25\Controller {

    public function __construct($registry) {
        parent::__construct($registry, 'setting/setting');
    }

	public function index() {


        // Инициализиране на данните
		$this->initAdminData();

		$this->setTitle('Настройки на магазина');

		// Обработка на POST заявка за запазване на настройките
		if ($this->isPostRequest()) {
			$this->save();
		}

		// Зареждане на текущите настройки
		$config_security_ip_restriction_status = $this->config->get('config_security_ip_restriction_status');

		$allowed_ips = $this->config->get('config_security_allowed_ips');
		if (!empty($allowed_ips)) {
			$config_security_allowed_ips = $allowed_ips;
		} else {
			$config_security_allowed_ips = [];
		}

        $this->setData([
            'config_security_ip_restriction_status' => $config_security_ip_restriction_status,
            'config_security_allowed_ips' => $config_security_allowed_ips
        ]);

		// Подаване на данните към шаблона
		$this->renderTemplateWithDataAndOutput('setting/setting');
	}

	protected function save() {
		$this->loadModelAs('setting/setting', 'setting');

		// Валидация и обработка на данните от формата

		// Обработка на статуса за IP ограничения
		if ((bool)$this->requestPost('config_security_ip_restriction_status')) {
			$this->setting->editSettingValue('config', 'config_security_ip_restriction_status', 1);
		} else {
			$this->setting->editSettingValue('config', 'config_security_ip_restriction_status', 0);
		}

		// Обработка на разрешените IP адреси
		if ($this->requestPost('config_security_allowed_ips')) {
			// Филтриране на празните стойности и премахване на дубликати
			$allowed_ips = array_unique(array_filter($this->requestPost('config_security_allowed_ips', [])));
			$this->setting->editSettingValue('config', 'config_security_allowed_ips', $allowed_ips);
		} else {
			$this->setting->editSettingValue('config', 'config_security_allowed_ips', []);
		}

		// Добавяне на съобщение за успех
		$this->setSession('success', 'Настройките са запазени успешно!');

		// Пренасочване към страницата с настройки
		$this->redirectResponse($this->getAdminLink('setting/setting'));
	}
}
