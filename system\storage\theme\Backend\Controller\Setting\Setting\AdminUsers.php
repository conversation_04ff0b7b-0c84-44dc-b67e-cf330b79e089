<?php

namespace Theme25\Backend\Controller\Setting\Setting;

/**
 * Sub-контролер за настройки за административни потребители
 *
 * Този контролер управлява логиката за показване и обработка на настройките за
 * административни потребители, включително създаване, редактиране, права на достъп,
 * групи потребители, сигурност и активност.
 *
 * @package Theme25\Backend\Controller\Setting\Setting
 * <AUTHOR> Development Team
 * @version 1.0.0
 */
class AdminUsers extends \Theme25\ControllerSubMethods {

    /**
     * Подготвя данните за настройките за административни потребители
     */
    public function prepareData() {
        $this->prepareAdminUsersData()
             ->prepareUserGroupsData()
             ->preparePermissionsData()
             ->prepareSecuritySettingsData()
             ->prepareActivityLogsData()
             ->prepareUrlsAndActions()
             ->prepareValidationRules();

        // Подготвяне на JavaScript конфигурация
        $this->setData([
            'user_token' => $this->getUserToken()
        ]);
    }

    /**
     * Подготвя данните за административните потребители
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareAdminUsersData() {
        try {
            $this->loadModelAs('setting/AdminUsersSettingsModel', 'adminUsersSettings');
            $admin_users = $this->adminUsersSettings->getAdminUsers();
            
            $this->setData('admin_users', $admin_users);
            
        } catch (Exception $e) {
            $this->setError('Грешка при зареждане на административни потребители: ' . $e->getMessage());
            
            // Задаване на стойности по подразбиране
            $this->setData('admin_users', [
                [
                    'user_id' => 1,
                    'username' => 'admin',
                    'firstname' => 'Администратор',
                    'lastname' => 'Системен',
                    'email' => '<EMAIL>',
                    'user_group' => 'Администратори',
                    'user_group_id' => 1,
                    'status' => 1,
                    'last_login' => date('Y-m-d H:i:s'),
                    'date_added' => date('Y-m-d H:i:s'),
                    'image' => '',
                    'failed_attempts' => 0,
                    'locked_until' => null
                ]
            ]);
        }

        return $this;
    }

    /**
     * Подготвя данните за групите потребители
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareUserGroupsData() {
        try {
            $this->loadModelAs('setting/AdminUsersSettingsModel', 'adminUsersSettings');
            $user_groups = $this->adminUsersSettings->getUserGroups();
            
            $this->setData('user_groups', $user_groups);
            
        } catch (Exception $e) {
            // Задаване на стойности по подразбиране
            $this->setData('user_groups', [
                [
                    'user_group_id' => 1,
                    'name' => 'Администратори',
                    'description' => 'Пълен достъп до всички функции',
                    'users_count' => 1,
                    'permissions_count' => 0,
                    'created_date' => date('Y-m-d H:i:s')
                ],
                [
                    'user_group_id' => 2,
                    'name' => 'Мениджъри',
                    'description' => 'Достъп до продукти, поръчки и клиенти',
                    'users_count' => 0,
                    'permissions_count' => 15,
                    'created_date' => date('Y-m-d H:i:s')
                ],
                [
                    'user_group_id' => 3,
                    'name' => 'Оператори',
                    'description' => 'Ограничен достъп до поръчки',
                    'users_count' => 0,
                    'permissions_count' => 8,
                    'created_date' => date('Y-m-d H:i:s')
                ]
            ]);
        }

        return $this;
    }

    /**
     * Подготвя данните за правата на достъп
     *
     * @return $this За верижно извикване на методи
     */
    private function preparePermissionsData() {
        try {
            $this->loadModelAs('setting/AdminUsersSettingsModel', 'adminUsersSettings');
            $permissions = $this->adminUsersSettings->getPermissions();
            
            $this->setData('permissions', $permissions);
            
        } catch (Exception $e) {
            // Задаване на стойности по подразбиране
            $this->setData('permissions', [
                'catalog' => [
                    'name' => 'Каталог',
                    'description' => 'Управление на продукти, категории, производители',
                    'routes' => [
                        'catalog/product' => 'Продукти',
                        'catalog/category' => 'Категории',
                        'catalog/manufacturer' => 'Производители',
                        'catalog/option' => 'Опции',
                        'catalog/attribute' => 'Атрибути'
                    ]
                ],
                'sale' => [
                    'name' => 'Продажби',
                    'description' => 'Управление на поръчки, клиенти, ваучери',
                    'routes' => [
                        'sale/order' => 'Поръчки',
                        'sale/customer' => 'Клиенти',
                        'sale/voucher' => 'Ваучери',
                        'sale/return' => 'Връщания'
                    ]
                ],
                'marketing' => [
                    'name' => 'Маркетинг',
                    'description' => 'Маркетингови кампании и промоции',
                    'routes' => [
                        'marketing/marketing' => 'Маркетинг',
                        'marketing/coupon' => 'Купони',
                        'marketing/affiliate' => 'Партньори'
                    ]
                ],
                'system' => [
                    'name' => 'Система',
                    'description' => 'Системни настройки и конфигурация',
                    'routes' => [
                        'setting/setting' => 'Настройки',
                        'user/user' => 'Потребители',
                        'user/user_group' => 'Групи потребители',
                        'tool/backup' => 'Backup'
                    ]
                ],
                'report' => [
                    'name' => 'Отчети',
                    'description' => 'Статистики и отчети',
                    'routes' => [
                        'report/sale_order' => 'Отчет продажби',
                        'report/product_viewed' => 'Най-гледани продукти',
                        'report/customer_online' => 'Онлайн клиенти'
                    ]
                ]
            ]);
        }

        return $this;
    }

    /**
     * Подготвя данните за настройки за сигурност
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareSecuritySettingsData() {
        try {
            $this->loadModelAs('setting/AdminUsersSettingsModel', 'adminUsersSettings');
            $security_settings = $this->adminUsersSettings->getSecuritySettings();
            
            $this->setData('security_settings', $security_settings);
            
        } catch (Exception $e) {
            // Задаване на стойности по подразбиране
            $this->setData('security_settings', [
                'password_min_length' => 8,
                'password_require_uppercase' => 1,
                'password_require_lowercase' => 1,
                'password_require_numbers' => 1,
                'password_require_symbols' => 0,
                'password_expiry_days' => 90,
                'max_login_attempts' => 5,
                'lockout_duration' => 30,
                'session_timeout' => 3600,
                'force_password_change' => 0,
                'two_factor_auth' => 0,
                'ip_whitelist_enabled' => 0,
                'ip_whitelist' => '',
                'login_notifications' => 1,
                'activity_logging' => 1,
                'auto_logout_idle' => 1
            ]);
        }

        return $this;
    }

    /**
     * Подготвя данните за логове на активност
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareActivityLogsData() {
        try {
            $this->loadModelAs('setting/AdminUsersSettingsModel', 'adminUsersSettings');
            $activity_logs = $this->adminUsersSettings->getRecentActivityLogs();
            
            $this->setData('activity_logs', $activity_logs);
            
        } catch (Exception $e) {
            // Задаване на стойности по подразбиране
            $this->setData('activity_logs', [
                [
                    'log_id' => 1,
                    'user_id' => 1,
                    'username' => 'admin',
                    'action' => 'login',
                    'description' => 'Успешен вход в системата',
                    'ip_address' => '*************',
                    'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'date_added' => date('Y-m-d H:i:s', strtotime('-5 minutes'))
                ],
                [
                    'log_id' => 2,
                    'user_id' => 1,
                    'username' => 'admin',
                    'action' => 'setting_update',
                    'description' => 'Актуализирани настройки на магазина',
                    'ip_address' => '*************',
                    'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'date_added' => date('Y-m-d H:i:s', strtotime('-15 minutes'))
                ]
            ]);
        }

        return $this;
    }

    /**
     * Подготвя URL-и за AJAX заявки и действия
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareUrlsAndActions() {
        $urls = [
            'save_url' => $this->getAdminLink('setting/setting/admin_users_save'),
            'add_user_url' => $this->getAdminLink('setting/setting/add_admin_user'),
            'edit_user_url' => $this->getAdminLink('setting/setting/edit_admin_user'),
            'delete_user_url' => $this->getAdminLink('setting/setting/delete_admin_user'),
            'toggle_user_status_url' => $this->getAdminLink('setting/setting/toggle_user_status'),
            'reset_password_url' => $this->getAdminLink('setting/setting/reset_user_password'),
            'unlock_user_url' => $this->getAdminLink('setting/setting/unlock_user'),
            'add_user_group_url' => $this->getAdminLink('setting/setting/add_user_group'),
            'edit_user_group_url' => $this->getAdminLink('setting/setting/edit_user_group'),
            'delete_user_group_url' => $this->getAdminLink('setting/setting/delete_user_group'),
            'update_permissions_url' => $this->getAdminLink('setting/setting/update_permissions'),
            'test_security_url' => $this->getAdminLink('setting/setting/test_security'),
            'export_activity_logs_url' => $this->getAdminLink('setting/setting/export_activity_logs'),
            'clear_activity_logs_url' => $this->getAdminLink('setting/setting/clear_activity_logs')
        ];

        foreach ($urls as $key => $url) {
            $this->setData($key, $url);
        }

        return $this;
    }

    /**
     * Подготвя правила за валидация
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareValidationRules() {
        $validation_rules = [
            'username' => [
                'required' => true,
                'min_length' => 3,
                'max_length' => 20,
                'pattern' => '^[a-zA-Z0-9_]+$'
            ],
            'password' => [
                'required' => true,
                'min_length' => 8,
                'require_uppercase' => true,
                'require_lowercase' => true,
                'require_numbers' => true,
                'require_symbols' => false
            ],
            'email' => [
                'required' => true,
                'type' => 'email'
            ],
            'firstname' => [
                'required' => true,
                'min_length' => 2,
                'max_length' => 32
            ],
            'lastname' => [
                'required' => true,
                'min_length' => 2,
                'max_length' => 32
            ],
            'user_group_id' => [
                'required' => true,
                'type' => 'number',
                'min' => 1
            ]
        ];

        $this->setData('validation_rules', $validation_rules);

        return $this;
    }

    /**
     * Получава статистики за административните потребители
     *
     * @return array
     */
    private function getAdminUsersStatistics() {
        try {
            $this->loadModelAs('setting/AdminUsersSettingsModel', 'adminUsersModel');
            
            return [
                'total_users' => $this->adminUsersModel->getTotalUsersCount(),
                'active_users' => $this->adminUsersModel->getActiveUsersCount(),
                'locked_users' => $this->adminUsersModel->getLockedUsersCount(),
                'total_groups' => $this->adminUsersModel->getTotalGroupsCount(),
                'online_users' => $this->adminUsersModel->getOnlineUsersCount(),
                'failed_logins_today' => $this->adminUsersModel->getFailedLoginsToday(),
                'successful_logins_today' => $this->adminUsersModel->getSuccessfulLoginsToday()
            ];
            
        } catch (Exception $e) {
            return [
                'total_users' => 1,
                'active_users' => 1,
                'locked_users' => 0,
                'total_groups' => 3,
                'online_users' => 1,
                'failed_logins_today' => 0,
                'successful_logins_today' => 5
            ];
        }
    }

    /**
     * Проверява дали има предупреждения за потребителите
     *
     * @return array
     */
    private function getAdminUsersWarnings() {
        $warnings = [];
        
        // Проверка за слаби пароли
        $security_settings = $this->getData('security_settings') ?: [];
        if (empty($security_settings['password_require_uppercase']) || 
            empty($security_settings['password_require_numbers'])) {
            $warnings[] = [
                'type' => 'warning',
                'message' => 'Политиката за пароли не е достатъчно строга. Препоръчваме да изисквате главни букви и цифри.'
            ];
        }
        
        // Проверка за двуфакторна автентикация
        if (empty($security_settings['two_factor_auth'])) {
            $warnings[] = [
                'type' => 'info',
                'message' => 'Двуфакторната автентикация не е активирана. Препоръчваме да я включите за по-висока сигурност.'
            ];
        }
        
        // Проверка за заключени потребители
        $statistics = $this->getAdminUsersStatistics();
        if ($statistics['locked_users'] > 0) {
            $warnings[] = [
                'type' => 'warning',
                'message' => 'Има ' . $statistics['locked_users'] . ' заключени потребители. Проверете дали не са необходими действия.'
            ];
        }
        
        // Проверка за неуспешни опити за вход
        if ($statistics['failed_logins_today'] > 10) {
            $warnings[] = [
                'type' => 'error',
                'message' => 'Много неуспешни опити за вход днес (' . $statistics['failed_logins_today'] . '). Възможна атака!'
            ];
        }
        
        return $warnings;
    }

    /**
     * Получава конфигурацията за JavaScript модула
     *
     * @return array
     */
    public function getJavaScriptConfig() {
        return [
            'userToken' => $this->getUserToken(),
            'urls' => [
                'save' => $this->getData('save_url'),
                'addUser' => $this->getData('add_user_url'),
                'editUser' => $this->getData('edit_user_url'),
                'deleteUser' => $this->getData('delete_user_url'),
                'toggleUserStatus' => $this->getData('toggle_user_status_url'),
                'resetPassword' => $this->getData('reset_password_url'),
                'unlockUser' => $this->getData('unlock_user_url'),
                'addUserGroup' => $this->getData('add_user_group_url'),
                'editUserGroup' => $this->getData('edit_user_group_url'),
                'deleteUserGroup' => $this->getData('delete_user_group_url'),
                'updatePermissions' => $this->getData('update_permissions_url'),
                'testSecurity' => $this->getData('test_security_url'),
                'exportActivityLogs' => $this->getData('export_activity_logs_url'),
                'clearActivityLogs' => $this->getData('clear_activity_logs_url')
            ],
            'validationRules' => $this->getData('validation_rules'),
            'statistics' => $this->getAdminUsersStatistics(),
            'warnings' => $this->getAdminUsersWarnings()
        ];
    }
}
