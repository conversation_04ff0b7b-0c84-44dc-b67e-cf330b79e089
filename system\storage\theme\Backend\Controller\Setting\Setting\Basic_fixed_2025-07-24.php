<?php

namespace Theme25\Backend\Controller\Setting\Setting;

/**
 * Sub-контролер за основни настройки на магазина
 *
 * Този контролер управлява логиката за показване и обработка на основните настройки,
 * включително информация за магазина, социални мрежи и SEO настройки.
 *
 * @package Theme25\Backend\Controller\Setting\Setting
 * <AUTHOR> Development Team
 * @version 1.0.0
 */
class Basic extends \Theme25\ControllerSubMethods {

    /**
     * Подготвя данните за основните настройки
     */
    public function prepareData() {
        $this->prepareBasicSettingsData()
             ->prepareImageUrls()
             ->prepareAdditionalData()
             ->prepareUrlsAndActions()
             ->prepareValidationRules();

        // Подготвяне на JavaScript конфигурация
        $this->setData([
            'user_token' => $this->getUserToken()
        ]);
    }

    /**
     * Подготвя основните данни за настройките
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareBasicSettingsData() {
        try {
            $this->loadModelAs('setting/BasicSettingsModel', 'basicSettings');
            $settings = $this->basicSettings->getBasicSettings();
            
            // Добавяне на настройките към данните
            foreach ($settings as $key => $value) {
                $this->setData($key, $value);
            }
            
        } catch (Exception $e) {
            $this->setError('Грешка при зареждане на настройки: ' . $e->getMessage());
            
            // Задаване на стойности по подразбиране
            $this->setData([
                'store_name' => 'Rakla',
                'store_email' => '<EMAIL>',
                'store_phone' => '+359 88 123 4567',
                'store_address' => 'ул. Иван Вазов 12, 1000 София, България',
                'store_logo' => '',
                'facebook_url' => '',
                'instagram_url' => '',
                'youtube_url' => '',
                'twitter_url' => '',
                'maintenance_mode' => 0,
                'support_chat' => 1,
                'reviews_enabled' => 1,
                'stock_display' => 1,
                'currency' => 'BGN',
                'language' => 'bg-bg',
                'meta_title' => 'Rakla - Онлайн магазин',
                'meta_description' => '',
                'meta_keywords' => ''
            ]);
        }

        return $this;
    }

    /**
     * Подготвя URL адресите за изображения
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareImageUrls() {
        $store_logo = $this->getData('store_logo');

        // Генериране на URL към логото
        $store_logo_url = '';
        if (!empty($store_logo)) {
            $store_logo_url = ThemeData()->getImageServerUrl() . $store_logo;
        }

        $this->setData([
            'store_logo_url' => $store_logo_url
        ]);

        return $this;
    }

    /**
     * Подготвя допълнителни данни (валути, езици)
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareAdditionalData() {
        try {
            // Зареждане на налични валути
            $this->loadModelAs('localisation/currency', 'currencyModel');
            $currencies = $this->currencyModel->getCurrencies();
            
            $currency_options = [];
            foreach ($currencies as $currency) {
                $currency_options[] = [
                    'code' => $currency['code'],
                    'title' => $currency['title'],
                    'symbol_left' => $currency['symbol_left'],
                    'symbol_right' => $currency['symbol_right']
                ];
            }
            
            // Зареждане на налични езици
            $this->loadModelAs('localisation/language', 'languageModel');
            $languages = $this->languageModel->getLanguages();
            
            $language_options = [];
            foreach ($languages as $language) {
                $language_options[] = [
                    'code' => $language['code'],
                    'name' => $language['name'],
                    'image' => $language['image']
                ];
            }
            
            $this->setData([
                'currencies' => $currency_options,
                'languages' => $language_options
            ]);
            
        } catch (Exception $e) {
            // Fallback данни
            $this->setData([
                'currencies' => [
                    ['code' => 'BGN', 'title' => 'Български лев', 'symbol_left' => '', 'symbol_right' => ' лв.']
                ],
                'languages' => [
                    ['code' => 'bg-bg', 'name' => 'Български', 'image' => 'bg.png']
                ]
            ]);
        }

        return $this;
    }

    /**
     * Подготвя URL-и за AJAX заявки и действия
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareUrlsAndActions() {
        $urls = [
            'save_url' => $this->getAdminLink('setting/setting/basic_save'),
            'upload_logo_url' => $this->getAdminLink('setting/setting/upload_logo'),
            'remove_logo_url' => $this->getAdminLink('setting/setting/remove_logo'),
            'test_email_url' => $this->getAdminLink('setting/setting/test_email'),
            'test_social_url' => $this->getAdminLink('setting/setting/test_social_url'),
            'validate_contact_url' => $this->getAdminLink('setting/setting/validate_contact'),
            'get_logo_info_url' => $this->getAdminLink('setting/setting/get_logo_info')
        ];

        foreach ($urls as $key => $url) {
            $this->setData($key, $url);
        }

        return $this;
    }

    /**
     * Подготвя правила за валидация
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareValidationRules() {
        $validation_rules = [
            'store_name' => [
                'required' => true,
                'max_length' => 64,
                'type' => 'text'
            ],
            'store_email' => [
                'required' => true,
                'type' => 'email'
            ],
            'store_phone' => [
                'required' => true,
                'type' => 'phone'
            ],
            'store_address' => [
                'required' => true,
                'max_length' => 255,
                'type' => 'textarea'
            ],
            'meta_title' => [
                'required' => true,
                'max_length' => 255,
                'type' => 'text'
            ],
            'meta_description' => [
                'required' => false,
                'max_length' => 500,
                'type' => 'textarea'
            ],
            'facebook_url' => [
                'required' => false,
                'type' => 'url'
            ],
            'instagram_url' => [
                'required' => false,
                'type' => 'url'
            ],
            'youtube_url' => [
                'required' => false,
                'type' => 'url'
            ],
            'twitter_url' => [
                'required' => false,
                'type' => 'url'
            ]
        ];

        $this->setData('validation_rules', $validation_rules);

        return $this;
    }

    /**
     * Подготвя статистики за основните настройки
     *
     * @return array
     */
    private function getBasicSettingsStatistics() {
        $settings = $this->getData();
        
        $required_fields = ['store_name', 'store_email', 'store_phone', 'store_address', 'meta_title'];
        $optional_fields = ['facebook_url', 'instagram_url', 'youtube_url', 'twitter_url', 'meta_description'];
        
        $completed_required = 0;
        $completed_optional = 0;
        
        foreach ($required_fields as $field) {
            if (!empty($settings[$field])) {
                $completed_required++;
            }
        }
        
        foreach ($optional_fields as $field) {
            if (!empty($settings[$field])) {
                $completed_optional++;
            }
        }
        
        return [
            'required_completed' => $completed_required,
            'required_total' => count($required_fields),
            'optional_completed' => $completed_optional,
            'optional_total' => count($optional_fields),
            'overall_completion' => round((($completed_required + $completed_optional) / (count($required_fields) + count($optional_fields))) * 100)
        ];
    }

    /**
     * Проверява дали има предупреждения за основните настройки
     *
     * @return array
     */
    private function getBasicSettingsWarnings() {
        $warnings = [];
        $settings = $this->getData();
        
        // Проверка за липсващо лого
        if (empty($settings['store_logo'])) {
            $warnings[] = [
                'type' => 'info',
                'message' => 'Не е качено лого на магазина'
            ];
        }
        
        // Проверка за липсващи социални мрежи
        $social_fields = ['facebook_url', 'instagram_url', 'youtube_url', 'twitter_url'];
        $empty_social = 0;
        foreach ($social_fields as $field) {
            if (empty($settings[$field])) {
                $empty_social++;
            }
        }
        
        if ($empty_social === count($social_fields)) {
            $warnings[] = [
                'type' => 'warning',
                'message' => 'Не са конфигурирани връзки към социални мрежи'
            ];
        }
        
        // Проверка за липсващо SEO описание
        if (empty($settings['meta_description'])) {
            $warnings[] = [
                'type' => 'info',
                'message' => 'Не е зададено мета описание за SEO'
            ];
        }
        
        return $warnings;
    }

    /**
     * Получава конфигурацията за JavaScript модула
     *
     * @return array
     */
    public function getJavaScriptConfig() {
        return [
            'userToken' => $this->getUserToken(),
            'urls' => [
                'save' => $this->getData('save_url'),
                'uploadLogo' => $this->getData('upload_logo_url'),
                'removeLogo' => $this->getData('remove_logo_url'),
                'testEmail' => $this->getData('test_email_url'),
                'testSocialUrl' => $this->getData('test_social_url'),
                'validateContact' => $this->getData('validate_contact_url'),
                'getLogoInfo' => $this->getData('get_logo_info_url')
            ],
            'validationRules' => $this->getData('validation_rules'),
            'maxFileSize' => 2 * 1024 * 1024, // 2MB
            'allowedFileTypes' => ['image/jpeg', 'image/png', 'image/gif', 'image/svg+xml']
        ];
    }
}
