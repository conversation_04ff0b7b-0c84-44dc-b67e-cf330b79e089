<div class="mb-6">
    <h1 class="text-2xl font-bold text-gray-800">Настройки за известия</h1>
    <p class="text-gray-600 mt-1">Управлявайте email и SMS известията в магазина</p>
</div>

<form id="notification-settings-form" class="space-y-6">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Email Settings -->
        <div class="lg:col-span-2">
            <!-- Email Configuration -->
            <div class="bg-white rounded shadow p-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="ri-mail-settings-line mr-2"></i>
                    Email настройки
                </h2>
                
                <div class="space-y-4">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label for="config_email" class="block text-sm font-medium text-gray-700 mb-1">
                                Email адрес за изпращане <span class="text-red-500">*</span>
                            </label>
                            <input type="email" 
                                   id="config_email" 
                                   name="config_email"
                                   class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" 
                                   value="{{ email_settings.config_email ?? '' }}"
                                   required
                                   placeholder="<EMAIL>">
                        </div>

                        <div>
                            <label for="config_name" class="block text-sm font-medium text-gray-700 mb-1">
                                Име на изпращача <span class="text-red-500">*</span>
                            </label>
                            <input type="text" 
                                   id="config_name" 
                                   name="config_name"
                                   class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" 
                                   value="{{ email_settings.config_name ?? '' }}"
                                   required
                                   placeholder="Rakla.bg">
                        </div>
                    </div>

                    <div>
                        <label for="config_mail_protocol" class="block text-sm font-medium text-gray-700 mb-1">
                            Mail протокол
                        </label>
                        <select id="config_mail_protocol" 
                                name="config_mail_protocol"
                                class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary">
                            {% for protocol_code, protocol_name in mail_protocols %}
                            <option value="{{ protocol_code }}" {{ (email_settings.config_mail_protocol ?? 'mail') == protocol_code ? 'selected' : '' }}>
                                {{ protocol_name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- SMTP Settings -->
                    <div class="smtp-field space-y-4 p-4 bg-blue-50 border border-blue-200 rounded">
                        <h3 class="text-md font-medium text-blue-800">SMTP настройки</h3>
                        
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label for="config_mail_smtp_hostname" class="block text-sm font-medium text-gray-700 mb-1">
                                    SMTP Hostname
                                </label>
                                <input type="text" 
                                       id="config_mail_smtp_hostname" 
                                       name="config_mail_smtp_hostname"
                                       class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" 
                                       value="{{ email_settings.config_mail_smtp_hostname ?? '' }}"
                                       placeholder="smtp.gmail.com">
                            </div>

                            <div>
                                <label for="config_mail_smtp_port" class="block text-sm font-medium text-gray-700 mb-1">
                                    SMTP Port
                                </label>
                                <input type="number" 
                                       id="config_mail_smtp_port" 
                                       name="config_mail_smtp_port"
                                       class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" 
                                       value="{{ email_settings.config_mail_smtp_port ?? 587 }}"
                                       min="1"
                                       max="65535"
                                       placeholder="587">
                            </div>
                        </div>

                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label for="config_mail_smtp_username" class="block text-sm font-medium text-gray-700 mb-1">
                                    SMTP Username
                                </label>
                                <input type="text" 
                                       id="config_mail_smtp_username" 
                                       name="config_mail_smtp_username"
                                       class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" 
                                       value="{{ email_settings.config_mail_smtp_username ?? '' }}"
                                       placeholder="<EMAIL>">
                            </div>

                            <div>
                                <label for="config_mail_smtp_password" class="block text-sm font-medium text-gray-700 mb-1">
                                    SMTP Password
                                </label>
                                <input type="password" 
                                       id="config_mail_smtp_password" 
                                       name="config_mail_smtp_password"
                                       class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" 
                                       value="{{ email_settings.config_mail_smtp_password ?? '' }}"
                                       placeholder="••••••••">
                            </div>
                        </div>

                        <div>
                            <label for="config_mail_smtp_timeout" class="block text-sm font-medium text-gray-700 mb-1">
                                SMTP Timeout (секунди)
                            </label>
                            <input type="number" 
                                   id="config_mail_smtp_timeout" 
                                   name="config_mail_smtp_timeout"
                                   class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" 
                                   value="{{ email_settings.config_mail_smtp_timeout ?? 5 }}"
                                   min="1"
                                   max="300"
                                   placeholder="5">
                        </div>
                    </div>

                    <div>
                        <label for="config_mail_alert_email" class="block text-sm font-medium text-gray-700 mb-1">
                            Admin email адреси (разделени със запетая)
                        </label>
                        <input type="text" 
                               id="config_mail_alert_email" 
                               name="config_mail_alert_email"
                               class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" 
                               value="{{ email_settings.config_mail_alert_email ?? '' }}"
                               placeholder="<EMAIL>, <EMAIL>">
                        <p class="text-xs text-gray-500 mt-1">Email адреси за получаване на административни известия</p>
                    </div>
                </div>
            </div>

            <!-- Email Templates -->
            <div class="bg-white rounded shadow p-6 mt-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="ri-file-text-line mr-2"></i>
                    Email шаблони
                </h2>
                
                <div class="space-y-6">
                    {% for template_code, template in email_templates %}
                    <div class="border border-gray-200 rounded p-4">
                        <div class="flex items-center justify-between mb-3">
                            <h3 class="font-medium text-gray-900">{{ template.name }}</h3>
                            <div class="flex items-center space-x-2">
                                <button type="button" 
                                        data-template="{{ template_code }}"
                                        class="preview-email-template px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600 transition-colors">
                                    <i class="ri-eye-line mr-1"></i>
                                    Preview
                                </button>
                                <button type="button" 
                                        data-template="{{ template_code }}"
                                        class="reset-email-template px-3 py-1 bg-gray-500 text-white text-sm rounded hover:bg-gray-600 transition-colors">
                                    <i class="ri-refresh-line mr-1"></i>
                                    Reset
                                </button>
                                <label class="flex items-center cursor-pointer">
                                    <input type="checkbox" 
                                           name="email_templates[{{ template_code }}][status]"
                                           class="toggle-switch"
                                           {{ template.status ? 'checked' : '' }}>
                                    <span class="ml-2 text-sm text-gray-700">Активен</span>
                                </label>
                            </div>
                        </div>
                        
                        <div class="space-y-3">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    Тема на email
                                </label>
                                <input type="text" 
                                       name="email_templates[{{ template_code }}][subject]"
                                       class="w-full px-3 py-2 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" 
                                       value="{{ template.subject ?? '' }}"
                                       placeholder="Въведете тема на email...">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    Съдържание на email
                                </label>
                                <textarea name="email_templates[{{ template_code }}][content]"
                                          class="email-template-content w-full px-3 py-2 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
                                          rows="4"
                                          placeholder="Въведете съдържание на email...">{{ template.content ?? '' }}</textarea>
                            </div>
                            
                            {% if template.variables is defined and template.variables %}
                            <div class="text-xs text-gray-500">
                                <strong>Налични променливи:</strong> 
                                {% for variable in template.variables %}
                                    <code class="bg-gray-100 px-1 rounded">{{'{'}}{{ variable }}{{'}'}}}</code>{% if not loop.last %}, {% endif %}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <!-- Notification Settings -->
            <div class="bg-white rounded shadow p-6 mt-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="ri-notification-3-line mr-2"></i>
                    Настройки за известия
                </h2>
                
                <div class="space-y-4">
                    <div class="grid grid-cols-2 gap-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <label for="notification_email_enabled" class="text-sm font-medium text-gray-700">
                                    Email известия
                                </label>
                                <p class="text-xs text-gray-500">Активира изпращането на email известия</p>
                            </div>
                            <input type="checkbox" 
                                   id="notification_email_enabled" 
                                   name="notification_email_enabled"
                                   class="toggle-switch"
                                   {{ notification_email_enabled ? 'checked' : '' }}>
                        </div>

                        <div class="flex items-center justify-between">
                            <div>
                                <label for="notification_sms_enabled" class="text-sm font-medium text-gray-700">
                                    SMS известия
                                </label>
                                <p class="text-xs text-gray-500">Активира изпращането на SMS известия</p>
                            </div>
                            <input type="checkbox" 
                                   id="notification_sms_enabled" 
                                   name="notification_sms_enabled"
                                   class="toggle-switch"
                                   {{ notification_sms_enabled ? 'checked' : '' }}>
                        </div>
                    </div>

                    <div class="grid grid-cols-2 gap-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <label for="notification_admin_new_order" class="text-sm font-medium text-gray-700">
                                    Нови поръчки
                                </label>
                                <p class="text-xs text-gray-500">Известие при нова поръчка</p>
                            </div>
                            <input type="checkbox" 
                                   id="notification_admin_new_order" 
                                   name="notification_admin_new_order"
                                   class="toggle-switch"
                                   {{ notification_admin_new_order ? 'checked' : '' }}>
                        </div>

                        <div class="flex items-center justify-between">
                            <div>
                                <label for="notification_admin_new_customer" class="text-sm font-medium text-gray-700">
                                    Нови клиенти
                                </label>
                                <p class="text-xs text-gray-500">Известие при нова регистрация</p>
                            </div>
                            <input type="checkbox" 
                                   id="notification_admin_new_customer" 
                                   name="notification_admin_new_customer"
                                   class="toggle-switch"
                                   {{ notification_admin_new_customer ? 'checked' : '' }}>
                        </div>
                    </div>

                    <div class="grid grid-cols-2 gap-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <label for="notification_customer_order_status" class="text-sm font-medium text-gray-700">
                                    Промяна в статус
                                </label>
                                <p class="text-xs text-gray-500">Известие при промяна в статуса на поръчка</p>
                            </div>
                            <input type="checkbox" 
                                   id="notification_customer_order_status" 
                                   name="notification_customer_order_status"
                                   class="toggle-switch"
                                   {{ notification_customer_order_status ? 'checked' : '' }}>
                        </div>

                        <div class="flex items-center justify-between">
                            <div>
                                <label for="notification_low_stock" class="text-sm font-medium text-gray-700">
                                    Нисък запас
                                </label>
                                <p class="text-xs text-gray-500">Известие при нисък запас на продукти</p>
                            </div>
                            <input type="checkbox" 
                                   id="notification_low_stock" 
                                   name="notification_low_stock"
                                   class="toggle-switch"
                                   {{ notification_low_stock ? 'checked' : '' }}>
                        </div>
                    </div>

                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label for="notification_low_stock_threshold" class="block text-sm font-medium text-gray-700 mb-1">
                                Праг за нисък запас
                            </label>
                            <input type="number" 
                                   id="notification_low_stock_threshold" 
                                   name="notification_low_stock_threshold"
                                   class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" 
                                   value="{{ notification_low_stock_threshold ?? 5 }}"
                                   min="0"
                                   placeholder="5">
                        </div>

                        <div>
                            <label for="notification_frequency" class="block text-sm font-medium text-gray-700 mb-1">
                                Честота на известията
                            </label>
                            <select id="notification_frequency" 
                                    name="notification_frequency"
                                    class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary">
                                {% for frequency_code, frequency_name in notification_frequencies %}
                                <option value="{{ frequency_code }}" {{ notification_frequency == frequency_code ? 'selected' : '' }}>
                                    {{ frequency_name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>

                    <div class="grid grid-cols-2 gap-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <label for="notification_queue_enabled" class="text-sm font-medium text-gray-700">
                                    Опашка за известия
                                </label>
                                <p class="text-xs text-gray-500">Използва опашка за по-бързо изпращане</p>
                            </div>
                            <input type="checkbox" 
                                   id="notification_queue_enabled" 
                                   name="notification_queue_enabled"
                                   class="toggle-switch"
                                   {{ notification_queue_enabled ? 'checked' : '' }}>
                        </div>

                        <div>
                            <label for="notification_retry_attempts" class="block text-sm font-medium text-gray-700 mb-1">
                                Опити за повторно изпращане
                            </label>
                            <input type="number" 
                                   id="notification_retry_attempts" 
                                   name="notification_retry_attempts"
                                   class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" 
                                   value="{{ notification_retry_attempts ?? 3 }}"
                                   min="0"
                                   max="10"
                                   placeholder="3">
                        </div>
                    </div>
                </div>
            </div>

            <!-- SMS Settings -->
            <div class="bg-white rounded shadow p-6 mt-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="ri-message-2-line mr-2"></i>
                    SMS настройки
                </h2>
                
                <div class="sms-field space-y-4">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label for="sms_provider" class="block text-sm font-medium text-gray-700 mb-1">
                                SMS доставчик
                            </label>
                            <select id="sms_provider" 
                                    name="sms_provider"
                                    class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary">
                                {% for provider_code, provider_name in sms_providers %}
                                <option value="{{ provider_code }}" {{ sms_settings.sms_provider == provider_code ? 'selected' : '' }}>
                                    {{ provider_name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>

                        <div>
                            <label for="sms_sender_name" class="block text-sm font-medium text-gray-700 mb-1">
                                Име на изпращача
                            </label>
                            <input type="text" 
                                   id="sms_sender_name" 
                                   name="sms_sender_name"
                                   class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" 
                                   value="{{ sms_settings.sms_sender_name ?? '' }}"
                                   placeholder="Rakla.bg">
                        </div>
                    </div>

                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label for="sms_api_key" class="block text-sm font-medium text-gray-700 mb-1">
                                API Key
                            </label>
                            <input type="text" 
                                   id="sms_api_key" 
                                   name="sms_api_key"
                                   class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" 
                                   value="{{ sms_settings.sms_api_key ?? '' }}"
                                   placeholder="Въведете API ключ...">
                        </div>

                        <div>
                            <label for="sms_api_secret" class="block text-sm font-medium text-gray-700 mb-1">
                                API Secret
                            </label>
                            <input type="password" 
                                   id="sms_api_secret" 
                                   name="sms_api_secret"
                                   class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary" 
                                   value="{{ sms_settings.sms_api_secret ?? '' }}"
                                   placeholder="••••••••">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Notifications Panel -->
        <div class="lg:col-span-1">
            <!-- Notification Statistics -->
            <div class="bg-white rounded shadow p-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="ri-bar-chart-line mr-2"></i>
                    Статистики за известия
                </h2>
                
                <div class="space-y-3">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Активни шаблони:</span>
                        <span id="active-email-templates-count" class="text-sm font-medium text-green-600">
                            {{ active_email_templates ?? 0 }}
                        </span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Общо шаблони:</span>
                        <span class="text-sm font-medium text-blue-600">
                            {{ total_email_templates ?? 0 }}
                        </span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Изпратени днес:</span>
                        <span id="emails-sent-today-count" class="text-sm font-medium text-purple-600">
                            {{ emails_sent_today ?? 0 }}
                        </span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Изпратени този месец:</span>
                        <span class="text-sm font-medium text-gray-600">
                            {{ emails_sent_month ?? 0 }}
                        </span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Неуспешни днес:</span>
                        <span class="text-sm font-medium text-red-600">
                            {{ failed_emails_today ?? 0 }}
                        </span>
                    </div>
                </div>
            </div>

            <!-- Test Notifications -->
            <div class="bg-white rounded shadow p-6 mt-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="ri-test-tube-line mr-2"></i>
                    Тестване на известия
                </h2>
                
                <div class="space-y-3">
                    <button type="button" 
                            id="test-email-settings"
                            class="w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors">
                        <i class="ri-mail-send-line mr-2"></i>
                        Тест Email
                    </button>

                    <button type="button" 
                            id="test-sms-settings"
                            class="w-full px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors">
                        <i class="ri-message-2-line mr-2"></i>
                        Тест SMS
                    </button>
                    
                    <div class="border-t pt-3">
                        <div class="space-y-2">
                            <input type="email" 
                                   id="test-notification-email"
                                   class="w-full px-3 py-2 border border-gray-300 rounded text-sm"
                                   placeholder="<EMAIL>">
                            
                            <select id="test-notification-type" class="w-full px-3 py-2 border border-gray-300 rounded text-sm">
                                <option value="">Изберете тип известие</option>
                                <option value="order_confirmation">Потвърждение на поръчка</option>
                                <option value="order_shipped">Поръчката е изпратена</option>
                                <option value="customer_registration">Регистрация на клиент</option>
                                <option value="password_reset">Възстановяване на парола</option>
                            </select>
                            
                            <button type="button" 
                                    id="send-test-notification"
                                    class="w-full px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600 transition-colors">
                                <i class="ri-send-plane-line mr-2"></i>
                                Изпрати тест
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded shadow p-6 mt-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="ri-tools-line mr-2"></i>
                    Бързи действия
                </h2>
                
                <div class="space-y-3">
                    <button type="submit" 
                            id="save-notification-settings"
                            class="w-full px-4 py-2 bg-primary text-white rounded hover:bg-primary/90 transition-colors">
                        <i class="ri-save-line mr-2"></i>
                        Запази настройки
                    </button>

                    <button type="button" 
                            onclick="location.reload()"
                            class="w-full px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors">
                        <i class="ri-refresh-line mr-2"></i>
                        Презареди
                    </button>
                </div>
            </div>

            <!-- Notification Tips -->
            <div class="bg-yellow-50 border border-yellow-200 rounded p-6 mt-6">
                <h2 class="text-lg font-semibold text-yellow-800 mb-4">
                    <i class="ri-lightbulb-line mr-2"></i>
                    Съвети за известия
                </h2>
                
                <div class="space-y-3 text-sm text-yellow-700">
                    <div class="flex items-start">
                        <i class="ri-check-line text-yellow-500 mr-2 mt-0.5"></i>
                        <span>Тествайте email настройките преди да активирате известията</span>
                    </div>
                    
                    <div class="flex items-start">
                        <i class="ri-check-line text-yellow-500 mr-2 mt-0.5"></i>
                        <span>Персонализирайте email шаблоните с променливи</span>
                    </div>
                    
                    <div class="flex items-start">
                        <i class="ri-check-line text-yellow-500 mr-2 mt-0.5"></i>
                        <span>Използвайте SMTP за по-надеждно изпращане</span>
                    </div>
                    
                    <div class="flex items-start">
                        <i class="ri-check-line text-yellow-500 mr-2 mt-0.5"></i>
                        <span>Активирайте опашката при голям обем известия</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
