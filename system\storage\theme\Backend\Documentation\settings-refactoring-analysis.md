# Анализ и план за преработка на административния панел за настройки

## 1. АНАЛИЗ НА СЪЩЕСТВУВАЩИЯ ШАБЛОН

### 1.1 Общ преглед на setting.twig
- **Файл:** `system/storage/theme/Backend/View/Template/setting/setting.twig`
- **Размер:** 1039 реда код
- **Структура:** 7 табове с различни функционалности
- **JavaScript:** Вграден в шаблона (985-1038 реда)

### 1.2 Структура на табовете

#### Таб 1: Основни настройки (tab-basic / content-basic)
**Функционалности:**
- Информация за магазина (име, лого, имейл, телефон, адрес)
- Социални мрежи (Facebook, Instagram, YouTube, Twitter)
- Общи настройки (поддръжка, отзиви, наличност, режим за поддръжка)
- Валута и език (основна валута, основен език)

**Полета за обработка:**
- `store-name` - текстово поле
- Лого - файл upload с preview
- `store-email` - имейл поле
- `store-phone` - телефонно поле
- `store-address` - textarea
- Социални мрежи - 4 URL полета
- Toggle switches за настройки
- Select полета за валута и език

#### Таб 2: Доставка (tab-delivery / content-delivery)
**Функционалности:**
- Методи за доставка (Спиди интеграция)
- Безплатна доставка настройки

**Полета за обработка:**
- Спиди потребителско име и парола
- Toggle за активиране на Спиди
- Toggle за безплатна доставка
- Минимална сума за безплатна доставка

#### Таб 3: Плащания (tab-payment / content-payment)
**Функционалности:**
- Наложен платеж
- Банков превод (банка, IBAN, BIC, получател)
- Кредитна/дебитна карта (Merchant ID, API ключ)
- ePay.bg интеграция (пълни настройки)

**Полета за обработка:**
- Toggle switches за всеки метод
- Банкови данни
- API ключове и настройки
- ePay конфигурация (6 полета)

#### Таб 4: Известия (tab-notifications / content-notifications)
**Функционалности:**
- Имейл шаблони (4 шаблона с редактиране)
- Настройки за известия (toggle switches)
- Тестване на известия

**Полета за обработка:**
- Редактиране на имейл шаблони
- Toggle switches за типове известия
- Тестов имейл и шаблон за тестване

#### Таб 5: Интеграции (tab-integrations / content-integrations)
**Функционалности:**
- Google Analytics
- Facebook Pixel
- Google reCAPTCHA
- Mailchimp
- Google Search Console

**Полета за обработка:**
- API ключове за всяка интеграция
- Toggle switches за активиране
- Специфични настройки за всяка услуга

#### Таб 6: Сигурност (tab-security / content-security)
**Функционалности:**
- Настройки за сигурност
- IP ограничения с динамично добавяне
- Логове за достъп
- Смяна на парола

**Полета за обработка:**
- Toggle за заключване след неуспешни опити
- Динамично управление на IP адреси
- Форма за смяна на парола

#### Таб 7: Админ потребители (tab-admin-users / content-admin-users)
**Функционалности:**
- Таблица с админ потребители
- Модал за добавяне/редактиране на потребители

**Полета за обработка:**
- Форма в модал с всички потребителски данни
- CRUD операции за админ потребители

### 1.3 JavaScript функционалности в шаблона
**Текущ JavaScript код (реда 985-1038):**
- Tab switching функционалност
- Модал управление за админ потребители
- IP адрес управление (динамично добавяне/премахване)
- Event listeners за форми

## 2. ИДЕНТИФИЦИРАНИ ПРОБЛЕМИ

### 2.1 Архитектурни проблеми
- Всички функционалности в един контролер
- JavaScript код вграден в Twig шаблона
- Липса на разделяне на отговорностите
- Няма AJAX заявки за асинхронни операции
- Всички данни се обработват в един метод

### 2.2 Поддръжка и разширяемост
- Трудно добавяне на нови табове
- Сложно тестване на отделни функционалности
- Дублиране на код между табовете
- Липса на валидация на клиентската страна

## 3. ПРЕПОРЪКИ ЗА АРХИТЕКТУРА

### 3.1 Контролерна структура
```
Setting.php (главен контролер)
Setting/ (суб-контролери)
├── Basic.php (основни настройки)
├── Delivery.php (доставка)
├── Payment.php (плащания)
├── Notifications.php (известия)
├── Integrations.php (интеграции)
├── Security.php (сигурност)
└── AdminUsers.php (админ потребители)
```

### 3.2 AJAX суб-контролери
```
Setting/Ajax/
├── BasicSave.php
├── DeliverySave.php
├── PaymentSave.php
├── NotificationTest.php
├── IntegrationTest.php
├── SecuritySave.php
├── IpManagement.php
├── AdminUserCrud.php
└── PasswordChange.php
```

### 3.3 JavaScript структура
```
Javascript/
├── settings/
│   ├── settings-main.js (основен файл)
│   ├── settings-basic.js
│   ├── settings-delivery.js
│   ├── settings-payment.js
│   ├── settings-notifications.js
│   ├── settings-integrations.js
│   ├── settings-security.js
│   └── settings-admin-users.js
```

## 4. AJAX ЗАЯВКИ И ИНТЕРАКТИВНИ ЕЛЕМЕНТИ

### 4.1 Необходими AJAX операции

#### Основни настройки
- **Запазване на основни данни** - `/setting/ajax/basic_save`
- **Upload на лого** - `/setting/ajax/logo_upload`
- **Валидация на имейл/телефон** - `/setting/ajax/validate_contact`

#### Доставка
- **Тест на Спиди връзка** - `/setting/ajax/speedy_test`
- **Запазване на доставни настройки** - `/setting/ajax/delivery_save`

#### Плащания
- **Тест на плащане API** - `/setting/ajax/payment_test`
- **Валидация на банкови данни** - `/setting/ajax/bank_validate`
- **ePay тест връзка** - `/setting/ajax/epay_test`

#### Известия
- **Изпращане на тестово известие** - `/setting/ajax/notification_test`
- **Редактиране на имейл шаблон** - `/setting/ajax/template_edit`
- **Preview на шаблон** - `/setting/ajax/template_preview`

#### Интеграции
- **Тест на Google Analytics** - `/setting/ajax/ga_test`
- **Тест на Facebook Pixel** - `/setting/ajax/fb_test`
- **Тест на reCAPTCHA** - `/setting/ajax/recaptcha_test`
- **Валидация на API ключове** - `/setting/ajax/api_validate`

#### Сигурност
- **Добавяне на IP адрес** - `/setting/ajax/ip_add`
- **Премахване на IP адрес** - `/setting/ajax/ip_remove`
- **Смяна на парола** - `/setting/ajax/password_change`
- **Зареждане на логове** - `/setting/ajax/logs_load`

#### Админ потребители
- **CRUD операции** - `/setting/ajax/admin_user_crud`
- **Валидация на потребител** - `/setting/ajax/user_validate`
- **Проверка на имейл** - `/setting/ajax/email_check`

### 4.2 Интерактивни елементи

#### Toggle switches
- Всички toggle switches трябва да запазват състоянието чрез AJAX
- Визуална обратна връзка при промяна
- Error handling при неуспешно запазване

#### Динамични форми
- IP адреси - добавяне/премахване на полета
- Социални мрежи - динамично управление
- Интеграции - показване/скриване на настройки

#### Валидация в реално време
- Имейл адреси
- URL адреси
- IP адреси
- API ключове

#### File uploads
- Лого upload с preview
- Drag & drop функционалност
- Progress bar
- Error handling

### 4.3 User Experience подобрения
- Loading states за всички AJAX операции
- Toast notifications за успех/грешка
- Автоматично запазване на промени
- Undo/Redo функционалност
- Keyboard shortcuts

## 5. ДЕТАЙЛНО АРХИТЕКТУРНО ПЛАНИРАНЕ

### 5.1 Главен контролер Setting.php

**Отговорности:**
- Координация между табовете
- Общи данни и инициализация
- Routing към специализираните контролери
- Рендиране на главния layout

**Методи:**
```php
public function index()           // Главна страница
public function getTabData()     // Данни за всички табове
public function validateAccess() // Проверка на права
```

### 5.2 Специализирани контролери

#### Setting/Basic.php
**Отговорности:**
- Основни настройки на магазина
- Социални мрежи
- Валута и език

**Методи:**
```php
public function index()                    // Зареждане на данни
public function save()                    // Запазване на настройки
public function uploadLogo()              // Upload на лого
public function validateContact()         // Валидация на контакти
```

#### Setting/Delivery.php
**Отговорности:**
- Методи за доставка
- Спиди интеграция
- Безплатна доставка

**Методи:**
```php
public function index()
public function save()
public function testSpeedy()              // Тест на Спиди API
public function getSpeedyOffices()        // Зареждане на офиси
```

#### Setting/Payment.php
**Отговорности:**
- Методи за плащане
- API интеграции за плащания

**Методи:**
```php
public function index()
public function save()
public function testPaymentGateway()      // Тест на плащане
public function validateBankData()        // Валидация на банкови данни
public function testEpay()               // Тест на ePay
```

#### Setting/Notifications.php
**Отговорности:**
- Имейл шаблони
- Настройки за известия
- Тестване на известия

**Методи:**
```php
public function index()
public function save()
public function editTemplate()           // Редактиране на шаблон
public function previewTemplate()        // Preview на шаблон
public function sendTestNotification()   // Тестово известие
```

#### Setting/Integrations.php
**Отговорности:**
- Външни интеграции
- API тестове

**Методи:**
```php
public function index()
public function save()
public function testGoogleAnalytics()
public function testFacebookPixel()
public function testRecaptcha()
public function validateApiKey()
```

#### Setting/Security.php
**Отговорности:**
- Настройки за сигурност
- IP ограничения
- Логове

**Методи:**
```php
public function index()
public function save()
public function addIpAddress()
public function removeIpAddress()
public function getAccessLogs()
public function changePassword()
```

#### Setting/AdminUsers.php
**Отговорности:**
- CRUD операции за админ потребители
- Управление на роли

**Методи:**
```php
public function index()
public function add()
public function edit()
public function delete()
public function validateUser()
public function checkEmail()
```

### 5.3 AJAX суб-контролери структура

#### Setting/Ajax/ директория
```
Ajax/
├── BasicOperations.php
├── DeliveryOperations.php
├── PaymentOperations.php
├── NotificationOperations.php
├── IntegrationOperations.php
├── SecurityOperations.php
└── AdminUserOperations.php
```

**Всеки AJAX контролер наследява базов AjaxController:**
```php
abstract class AjaxController extends \Theme25\Controller {
    protected function jsonResponse($data, $success = true)
    protected function validateAjaxRequest()
    protected function handleException($e)
}
```

### 5.4 Модели структура

#### Setting/Models/
```
Models/
├── BasicSettings.php
├── DeliverySettings.php
├── PaymentSettings.php
├── NotificationSettings.php
├── IntegrationSettings.php
├── SecuritySettings.php
└── AdminUser.php
```

**Всеки модел наследява базов SettingModel:**
```php
abstract class SettingModel extends \Theme25\ModelBackend {
    protected function getSetting($key, $default = null)
    protected function setSetting($key, $value)
    protected function validateSetting($key, $value)
}
```

### 5.5 Twig шаблони структура

#### Главен шаблон
- `setting/setting.twig` - основен layout с табове

#### Специализирани шаблони
```
setting/
├── tabs/
│   ├── basic.twig
│   ├── delivery.twig
│   ├── payment.twig
│   ├── notifications.twig
│   ├── integrations.twig
│   ├── security.twig
│   └── admin-users.twig
├── modals/
│   ├── admin-user-modal.twig
│   ├── template-editor-modal.twig
│   └── confirmation-modal.twig
└── components/
    ├── toggle-switch.twig
    ├── api-test-button.twig
    └── file-upload.twig
```

## 6. JAVASCRIPT СТРУКТУРА И ОРГАНИЗАЦИЯ

### 6.1 Основна архитектура

#### Главен модул - settings-main.js
```javascript
const SettingsManager = {
    config: {
        userToken: '',
        baseUrl: '',
        currentTab: 'basic'
    },

    modules: {},

    init() {
        this.loadConfig();
        this.initTabs();
        this.loadModules();
        this.bindGlobalEvents();
    },

    loadModule(name) {
        // Динамично зареждане на модули
    },

    showNotification(message, type) {
        // Глобални известия
    }
};
```

### 6.2 Специализирани модули

#### settings-basic.js
**Отговорности:**
- Основни настройки форма
- Logo upload функционалност
- Социални мрежи управление
- Валидация в реално време

**Ключови функции:**
```javascript
const BasicSettings = {
    init() {},
    handleLogoUpload() {},
    validateContactInfo() {},
    saveSocialMedia() {},
    toggleMaintenanceMode() {}
};
```

#### settings-delivery.js
**Отговорности:**
- Доставни методи
- Спиди интеграция
- Безплатна доставка калкулатор

**Ключови функции:**
```javascript
const DeliverySettings = {
    init() {},
    testSpeedyConnection() {},
    calculateFreeShipping() {},
    updateDeliveryMethods() {}
};
```

#### settings-payment.js
**Отговорности:**
- Плащания методи
- API тестове
- Валидация на данни

**Ключови функции:**
```javascript
const PaymentSettings = {
    init() {},
    testPaymentGateway() {},
    validateBankData() {},
    togglePaymentMethod() {}
};
```

#### settings-notifications.js
**Отговорности:**
- Имейл шаблони редактор
- Тестване на известия
- Preview функционалност

**Ключови функции:**
```javascript
const NotificationSettings = {
    init() {},
    openTemplateEditor() {},
    sendTestNotification() {},
    previewTemplate() {}
};
```

#### settings-integrations.js
**Отговорности:**
- API интеграции
- Тестване на връзки
- Конфигурация валидация

**Ключови функции:**
```javascript
const IntegrationSettings = {
    init() {},
    testGoogleAnalytics() {},
    testFacebookPixel() {},
    validateApiKeys() {}
};
```

#### settings-security.js
**Отговорности:**
- IP адреси управление
- Парола смяна
- Логове зареждане

**Ключови функции:**
```javascript
const SecuritySettings = {
    init() {},
    addIpAddress() {},
    removeIpAddress() {},
    changePassword() {},
    loadAccessLogs() {}
};
```

#### settings-admin-users.js
**Отговорности:**
- CRUD операции за потребители
- Модал управление
- Валидация на форми

**Ключови функции:**
```javascript
const AdminUserSettings = {
    init() {},
    openUserModal() {},
    saveUser() {},
    deleteUser() {},
    validateUserForm() {}
};
```

### 6.3 Споделени утилити

#### settings-utils.js
```javascript
const SettingsUtils = {
    // AJAX wrapper
    ajax(url, data, method = 'POST') {},

    // Валидация функции
    validateEmail(email) {},
    validateUrl(url) {},
    validateIp(ip) {},

    // UI утилити
    showLoader(element) {},
    hideLoader(element) {},
    showToast(message, type) {},

    // Форма утилити
    serializeForm(form) {},
    resetForm(form) {},
    validateForm(form) {}
};
```

### 6.4 Зареждане на модулите

#### В контролерите
```php
// В Setting/Basic.php
public function __construct($registry) {
    parent::__construct($registry, 'setting/basic');
    $this->loadScripts(['settings/settings-main.js', 'settings/settings-basic.js']);
}
```

#### Динамично зареждане
```javascript
// В settings-main.js
loadModule(tabName) {
    if (!this.modules[tabName]) {
        const script = document.createElement('script');
        script.src = `/backend_js/settings-${tabName}.js`;
        script.onload = () => {
            this.modules[tabName] = window[`${tabName.charAt(0).toUpperCase() + tabName.slice(1)}Settings`];
            this.modules[tabName].init();
        };
        document.head.appendChild(script);
    }
}
```

### 6.5 Event система

#### Глобални събития
```javascript
const SettingsEvents = {
    TAB_CHANGED: 'settings:tab:changed',
    DATA_SAVED: 'settings:data:saved',
    ERROR_OCCURRED: 'settings:error:occurred',

    emit(event, data) {
        document.dispatchEvent(new CustomEvent(event, { detail: data }));
    },

    on(event, callback) {
        document.addEventListener(event, callback);
    }
};
```

## 7. ДЕТАЙЛЕН ПЛАН ЗА ИЗПЪЛНЕНИЕ

### 7.1 Фаза 1: Подготовка и основа (Приоритет: Висок)

#### Задача 1.1: Създаване на базови класове (2 часа)
- Създаване на `AjaxController` базов клас
- Създаване на `SettingModel` базов клас
- Създаване на `SettingValidator` клас
- Тестване на базовата функционалност

#### Задача 1.2: Backup и подготовка (30 мин)
- Backup на съществуващия `Setting.php`
- Backup на `setting.twig` шаблона
- Създаване на директории за новата структура

#### Задача 1.3: Главен контролер рефакториране (1.5 часа)
- Преработка на `Setting.php` за координация
- Добавяне на routing логика
- Запазване на съвместимост с текущия URL

### 7.2 Фаза 2: Основни настройки (Приоритет: Висок)

#### Задача 2.1: Basic контролер (2 часа)
- Създаване на `Setting/Basic.php`
- Пренасяне на логиката за основни настройки
- Създаване на `BasicSettings` модел

#### Задача 2.2: Basic JavaScript (1.5 часа)
- Създаване на `settings-basic.js`
- Logo upload функционалност
- Валидация в реално време
- AJAX запазване

#### Задача 2.3: Basic шаблон (1 час)
- Създаване на `setting/tabs/basic.twig`
- Премахване на JavaScript от шаблона
- Тестване на функционалността

### 7.3 Фаза 3: Сигурност (Приоритет: Висок)

#### Задача 3.1: Security контролер (2 часа)
- Създаване на `Setting/Security.php`
- IP адреси управление
- Парола смяна логика
- Логове функционалност

#### Задача 3.2: Security JavaScript (1.5 часа)
- Създаване на `settings-security.js`
- Динамично IP управление
- AJAX операции за сигурност

#### Задача 3.3: Security шаблон (1 час)
- Създаване на `setting/tabs/security.twig`
- Интеграция с JavaScript модула

### 7.4 Фаза 4: Плащания и доставка (Приоритет: Среден)

#### Задача 4.1: Payment контролер (2.5 часа)
- Създаване на `Setting/Payment.php`
- API интеграции за плащания
- Валидация на плащания данни
- Тестване на gateway-ове

#### Задача 4.2: Delivery контролер (2 часа)
- Създаване на `Setting/Delivery.php`
- Спиди интеграция
- Безплатна доставка логика

#### Задача 4.3: Payment & Delivery JavaScript (2 часа)
- Създаване на `settings-payment.js`
- Създаване на `settings-delivery.js`
- API тестове и валидация

#### Задача 4.4: Payment & Delivery шаблони (1 час)
- Създаване на съответните Twig шаблони
- Интеграция с JavaScript

### 7.5 Фаза 5: Известия и интеграции (Приоритет: Среден)

#### Задача 5.1: Notifications контролер (2.5 часа)
- Създаване на `Setting/Notifications.php`
- Имейл шаблони редактор
- Тестване на известия
- Preview функционалност

#### Задача 5.2: Integrations контролер (2 часа)
- Създаване на `Setting/Integrations.php`
- API интеграции тестове
- Валидация на ключове

#### Задача 5.3: Notifications & Integrations JavaScript (2 часа)
- Създаване на `settings-notifications.js`
- Създаване на `settings-integrations.js`
- Rich text editor интеграция

#### Задача 5.4: Notifications & Integrations шаблони (1 час)
- Създаване на съответните Twig шаблони

### 7.6 Фаза 6: Админ потребители (Приоритет: Нисък)

#### Задача 6.1: AdminUsers контролер (2 часа)
- Създаване на `Setting/AdminUsers.php`
- CRUD операции
- Роли управление
- Валидация

#### Задача 6.2: AdminUsers JavaScript (1.5 часа)
- Създаване на `settings-admin-users.js`
- Модал управление
- AJAX CRUD операции

#### Задача 6.3: AdminUsers шаблони (1 час)
- Създаване на шаблони за таблица и модал

### 7.7 Фаза 7: Интеграция и тестване (Приоритет: Висок)

#### Задача 7.1: Главен JavaScript модул (2 часа)
- Създаване на `settings-main.js`
- Координация между модулите
- Глобални утилити
- Event система

#### Задача 7.2: Главен шаблон преработка (1.5 часа)
- Преработка на `setting.twig`
- Интеграция на всички табове
- Премахване на вградения JavaScript

#### Задача 7.3: Тестване и отстраняване на грешки (3 часа)
- Функционално тестване на всички табове
- AJAX операции тестване
- Cross-browser тестване
- Performance оптимизация

### 7.8 Фаза 8: Документация и финализиране (Приоритет: Среден)

#### Задача 8.1: Код документация (1 час)
- PHPDoc коментари
- JavaScript документация
- README файлове

#### Задача 8.2: Потребителска документация (1 час)
- Ръководство за използване
- Troubleshooting секция

## 8. AI ПОДКАНА ЗА ИЗПЪЛНЕНИЕ

### 8.1 Общи инструкции за AI агента

```
Ти си експертен PHP разработчик, който трябва да преработи административния панел за настройки на OpenCart проект. Следвай точно тези инструкции:

ВАЖНО: Винаги създавай backup файлове преди промени!

АРХИТЕКТУРНИ ПРИНЦИПИ:
1. Следвай Single Responsibility Principle - всеки контролер отговаря за един таб
2. Използвай съществуващите конвенции от проекта (Controller.php базов клас)
3. Всички AJAX операции в отделни методи/контролери
4. JavaScript код НИКОГА в Twig шаблони - само в .js файлове
5. Използвай $this->loadScripts() за зареждане на JS файлове в контролерите

СТРУКТУРА НА ФАЙЛОВЕТЕ:
- Контролери: system/storage/theme/Backend/Controller/Setting/
- Модели: system/storage/theme/Backend/Model/Setting/
- Шаблони: system/storage/theme/Backend/View/Template/setting/
- JavaScript: system/storage/theme/Backend/View/Javascript/settings/

КОНВЕНЦИИ ЗА КОД:
- Използвай $this->setData() за данни към шаблоните
- Използвай $this->renderTemplateWithDataAndOutput() за рендиране
- Всички AJAX отговори в JSON формат
- Валидация на всички входни данни
- Error handling с try-catch блокове
- Използвай $this->getAdminLink() за URL генериране
```

### 8.2 Специфични инструкции по фази

#### Фаза 1: Базови класове
```
Създай следните базови класове:

1. AjaxController (system/storage/theme/Backend/Controller/Ajax/AjaxController.php):
   - Методи: jsonResponse(), validateAjaxRequest(), handleException()
   - Наследява Theme25\Controller
   - Автоматична проверка на user_token

2. SettingModel (system/storage/theme/Backend/Model/Setting/SettingModel.php):
   - Методи: getSetting(), setSetting(), validateSetting()
   - Наследява Theme25\ModelBackend
   - Работа с oc_setting таблица

3. SettingValidator (system/storage/theme/Backend/Helper/SettingValidator.php):
   - Валидация на имейли, URL-и, IP адреси
   - API ключове валидация
   - Статични методи за лесно използване
```

#### Фаза 2: Basic контролер
```
Създай Setting/Basic.php:
- Наследява Theme25\Controller
- Конструктор: parent::__construct($registry, 'setting/basic')
- Зарежда scripts: ['settings/settings-main.js', 'settings/settings-basic.js']
- Методи: index(), save(), uploadLogo(), validateContact()
- Използва BasicSettings модел
- Всички данни чрез $this->setData()

Създай settings-basic.js:
- Модул BasicSettings с init(), handleLogoUpload(), validateContactInfo()
- Интеграция с SettingsManager главния модул
- AJAX заявки към /setting/basic/save
- Валидация в реално време
- File upload с preview
```

#### Фаза 3-6: Останали контролери
```
За всеки контролер (Security, Payment, Delivery, Notifications, Integrations, AdminUsers):

1. Създай контролер файл със същата структура като Basic
2. Създай съответния JavaScript модул
3. Създай Twig шаблон в setting/tabs/
4. Всички AJAX операции в отделни методи
5. Пълна валидация на данните
6. Error handling и logging

СПЕЦИАЛНИ ИЗИСКВАНИЯ:
- Security: IP адреси с regex валидация
- Payment: API тестове с timeout
- Notifications: Rich text editor за шаблони
- AdminUsers: Модал с CRUD операции
```

### 8.3 Примерен код за референция

#### Контролер структура:
```php
<?php
namespace Theme25\Backend\Controller\Setting;

class Basic extends \Theme25\Controller {
    public function __construct($registry) {
        parent::__construct($registry, 'setting/basic');
        $this->loadScripts(['settings/settings-main.js', 'settings/settings-basic.js']);
    }

    public function index() {
        $this->initAdminData();
        $this->setTitle('Основни настройки');

        // Зареждане на данни
        $this->loadModelAs('setting/basic_settings', 'basicSettings');
        $data = $this->basicSettings->getBasicSettings();
        $this->setData($data);

        $this->renderTemplateWithDataAndOutput('setting/tabs/basic');
    }

    public function save() {
        if (!$this->isPostRequest()) {
            $this->jsonResponse(['error' => 'Invalid request method'], false);
            return;
        }

        try {
            $this->loadModelAs('setting/basic_settings', 'basicSettings');
            $result = $this->basicSettings->saveSettings($this->requestPost());
            $this->jsonResponse(['message' => 'Настройките са запазени успешно']);
        } catch (Exception $e) {
            $this->jsonResponse(['error' => $e->getMessage()], false);
        }
    }
}
```

#### JavaScript структура:
```javascript
const BasicSettings = {
    init() {
        this.bindEvents();
        this.initValidation();
    },

    bindEvents() {
        document.getElementById('save-basic').addEventListener('click', () => {
            this.saveSettings();
        });
    },

    async saveSettings() {
        const formData = new FormData(document.getElementById('basic-form'));

        try {
            const response = await SettingsUtils.ajax('/setting/basic/save', formData);
            SettingsUtils.showToast(response.message, 'success');
        } catch (error) {
            SettingsUtils.showToast(error.message, 'error');
        }
    }
};
```

### 8.4 Контрол на качеството

```
ПРЕДИ ВСЯКА ЗАДАЧА:
1. Прочети съществуващия код за разбиране на конвенциите
2. Създай backup на файловете, които ще променяш
3. Тествай всяка функционалност след имплементация

СЛЕД ВСЯКА ЗАДАЧА:
1. Провери за PHP syntax грешки
2. Тествай AJAX заявките в браузъра
3. Провери console за JavaScript грешки
4. Валидирай HTML markup

ФИНАЛНО ТЕСТВАНЕ:
1. Всички табове да се зареждат правилно
2. Всички AJAX операции да работят
3. Валидацията да функционира
4. Няма JavaScript грешки в console
5. Responsive design да е запазен
```

## 9. ЗАКЛЮЧЕНИЕ

Този comprehensive план предоставя пълна roadmap за преработка на административния панел за настройки. Архитектурата е проектирана да бъде:

- **Модулна** - всеки таб е отделен модул
- **Поддържаема** - ясно разделение на отговорностите
- **Разширяема** - лесно добавяне на нови функционалности
- **Тестваема** - всеки компонент може да се тества отделно
- **Съвместима** - запазва съществуващите URL-и и конвенции

Общо време за изпълнение: **~35-40 часа** разпределени в 8 фази с ясни приоритети.

**Следващи действия:**
1. Започни с Фаза 1 (базови класове)
2. Продължи последователно през фазите
3. Тествай всяка фаза преди преминаване към следващата
4. Документирай всички промени

Този план осигурява structured approach за успешна преработка на системата, следвайки best practices и съществуващите конвенции на проекта.

