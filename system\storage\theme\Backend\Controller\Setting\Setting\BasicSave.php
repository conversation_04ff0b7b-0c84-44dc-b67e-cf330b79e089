<?php

namespace Theme25\Backend\Controller\Setting\Setting;

/**
 * Sub-контролер за AJAX запазване на основни настройки
 */
class BasicSave extends \Theme25\ControllerSubMethods {

    /**
     * Изпълнява AJAX запазване на основни настройки
     */
    public function execute() {
        $json = [];

        // Проверка за права на достъп
        if (!$this->hasPermission('modify', 'setting/setting')) {
            $json['error'] = 'Нямате права за редактиране на настройки';
            $this->outputJson($json);
            return;
        }

        // Проверка за POST заявка
        if (!$this->isPostRequest()) {
            $json['error'] = 'Невалидна заявка';
            $this->outputJson($json);
            return;
        }

        try {
            // Получаване на данните от POST
            $postData = $this->requestPost();
            
            // Валидация на данните
            $validation_errors = $this->validateBasicSettings($postData);
            if (!empty($validation_errors)) {
                $json['error'] = 'Грешки при валидацията';
                $json['validation_errors'] = $validation_errors;
                $this->outputJson($json);
                return;
            }

            $this->loadModelAs('setting/basic_settings_model', 'basicSettings');
            
            // Запазване на настройките
            $result = $this->basicSettings->saveBasicSettings($postData);
            
            if ($result === true) {
                $json['success'] = 'Основните настройки са запазени успешно';
                $json['timestamp'] = date('d.m.Y H:i:s');
                
                // Добавяне на актуализирани данни
                $json['updated_data'] = $this->getUpdatedSettingsData($postData);
            } else {
                // Има грешки при валидацията от модела
                $json['error'] = 'Грешки при валидацията';
                $json['validation_errors'] = $result;
            }

        } catch (Exception $e) {
            $json['error'] = 'Грешка при запазване: ' . $e->getMessage();
        }

        $this->outputJson($json);
    }

    /**
     * Валидира основните настройки
     *
     * @param array $data Данни за валидация
     * @return array Масив с грешки
     */
    private function validateBasicSettings($data) {
        $errors = [];

        // Валидация на име на магазина
        if (empty($data['store_name'])) {
            $errors['store_name'] = 'Името на магазина е задължително';
        } elseif (mb_strlen($data['store_name']) > 64) {
            $errors['store_name'] = 'Името на магазина не може да бъде повече от 64 символа';
        }

        // Валидация на имейл
        if (empty($data['store_email'])) {
            $errors['store_email'] = 'Имейл адресът е задължителен';
        } elseif (!filter_var($data['store_email'], FILTER_VALIDATE_EMAIL)) {
            $errors['store_email'] = 'Невалиден имейл адрес';
        }

        // Валидация на телефон
        if (empty($data['store_phone'])) {
            $errors['store_phone'] = 'Телефонният номер е задължителен';
        } elseif (!$this->isValidPhone($data['store_phone'])) {
            $errors['store_phone'] = 'Невалиден телефонен номер';
        }

        // Валидация на адрес
        if (empty($data['store_address'])) {
            $errors['store_address'] = 'Адресът е задължителен';
        } elseif (mb_strlen($data['store_address']) > 255) {
            $errors['store_address'] = 'Адресът не може да бъде повече от 255 символа';
        }

        // Валидация на социални мрежи URL-и
        $socialUrls = ['facebook_url', 'instagram_url', 'youtube_url', 'twitter_url'];
        foreach ($socialUrls as $urlField) {
            if (!empty($data[$urlField]) && !filter_var($data[$urlField], FILTER_VALIDATE_URL)) {
                $errors[$urlField] = 'Невалиден URL адрес';
            }
        }

        // Валидация на мета заглавие
        if (empty($data['meta_title'])) {
            $errors['meta_title'] = 'Мета заглавието е задължително';
        } elseif (mb_strlen($data['meta_title']) > 255) {
            $errors['meta_title'] = 'Мета заглавието не може да бъде повече от 255 символа';
        }

        // Валидация на мета описание
        if (!empty($data['meta_description']) && mb_strlen($data['meta_description']) > 500) {
            $errors['meta_description'] = 'Мета описанието не може да бъде повече от 500 символа';
        }

        return $errors;
    }

    /**
     * Валидира телефонен номер
     *
     * @param string $phone Телефонен номер
     * @return bool
     */
    private function isValidPhone($phone) {
        // Премахваме всички символи освен цифри, +, -, (, ), пространства
        $cleanPhone = preg_replace('/[^\d\+\-\(\)\s]/', '', $phone);
        
        // Проверяваме дали има поне 7 цифри
        $digits = preg_replace('/[^\d]/', '', $cleanPhone);
        return strlen($digits) >= 7;
    }

    /**
     * Получава актуализираните данни за настройките
     *
     * @param array $postData POST данни
     * @return array
     */
    private function getUpdatedSettingsData($postData) {
        return [
            'store_name' => $postData['store_name'] ?? '',
            'store_email' => $postData['store_email'] ?? '',
            'store_phone' => $postData['store_phone'] ?? '',
            'store_address' => $postData['store_address'] ?? '',
            'facebook_url' => $postData['facebook_url'] ?? '',
            'instagram_url' => $postData['instagram_url'] ?? '',
            'youtube_url' => $postData['youtube_url'] ?? '',
            'twitter_url' => $postData['twitter_url'] ?? '',
            'maintenance_mode' => isset($postData['maintenance_mode']) ? 1 : 0,
            'support_chat' => isset($postData['support_chat']) ? 1 : 0,
            'reviews_enabled' => isset($postData['reviews_enabled']) ? 1 : 0,
            'stock_display' => isset($postData['stock_display']) ? 1 : 0,
            'currency' => $postData['currency'] ?? 'BGN',
            'language' => $postData['language'] ?? 'bg-bg',
            'meta_title' => $postData['meta_title'] ?? '',
            'meta_description' => $postData['meta_description'] ?? '',
            'meta_keywords' => $postData['meta_keywords'] ?? ''
        ];
    }

    /**
     * Изпраща JSON отговор
     *
     * @param array $data
     */
    private function outputJson($data) {
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($data));
    }

    /**
     * Логира действието за запазване на настройки
     *
     * @param array $data Запазените данни
     */
    private function logSettingsSave($data) {
        try {
            $log_data = [
                'action' => 'basic_settings_save',
                'user_id' => $this->user->getId(),
                'user_name' => $this->user->getUserName(),
                'timestamp' => date('Y-m-d H:i:s'),
                'ip_address' => $this->request->server['REMOTE_ADDR'] ?? 'unknown',
                'changed_fields' => array_keys($data)
            ];
            
            // Логиране в системния лог
            $this->log->write('Settings: Basic settings updated by user ' . $this->user->getUserName() . ' (' . $this->user->getId() . ')');
            
        } catch (Exception $e) {
            // Ако логирането се провали, не спираме операцията
            error_log('Failed to log settings save: ' . $e->getMessage());
        }
    }

    /**
     * Валидира специфични полета според типа им
     *
     * @param string $field Име на полето
     * @param mixed $value Стойност
     * @return string|null Съобщение за грешка или null ако е валидно
     */
    private function validateField($field, $value) {
        switch ($field) {
            case 'store_name':
                if (empty($value)) return 'Името на магазина е задължително';
                if (mb_strlen($value) > 64) return 'Името не може да бъде повече от 64 символа';
                break;
                
            case 'store_email':
                if (empty($value)) return 'Имейл адресът е задължителен';
                if (!filter_var($value, FILTER_VALIDATE_EMAIL)) return 'Невалиден имейл адрес';
                break;
                
            case 'store_phone':
                if (empty($value)) return 'Телефонният номер е задължителен';
                if (!$this->isValidPhone($value)) return 'Невалиден телефонен номер';
                break;
                
            case 'store_address':
                if (empty($value)) return 'Адресът е задължителен';
                if (mb_strlen($value) > 255) return 'Адресът не може да бъде повече от 255 символа';
                break;
                
            case 'meta_title':
                if (empty($value)) return 'Мета заглавието е задължително';
                if (mb_strlen($value) > 255) return 'Мета заглавието не може да бъде повече от 255 символа';
                break;
                
            case 'meta_description':
                if (!empty($value) && mb_strlen($value) > 500) return 'Мета описанието не може да бъде повече от 500 символа';
                break;
                
            case 'facebook_url':
            case 'instagram_url':
            case 'youtube_url':
            case 'twitter_url':
                if (!empty($value) && !filter_var($value, FILTER_VALIDATE_URL)) return 'Невалиден URL адрес';
                break;
        }
        
        return null;
    }
}
