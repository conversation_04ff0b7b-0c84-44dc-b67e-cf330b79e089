<?php

namespace Theme25\Backend\Helper;

/**
 * Клас за валидация на настройки
 * Предоставя методи за валидация на различни типове данни
 */
class SettingValidator extends BaseHelper {

    public function __construct($registry) {
        parent::__construct($registry);
    }

    /**
     * Получава версията на Helper класа
     * @return string Версия на класа
     */
    public function getVersion() {
        return '1.0.0';
    }

    /**
     * Получава името на Helper класа
     * @return string Име на класа
     */
    public function getName() {
        return 'SettingValidator';
    }

    /**
     * Валидира имейл адрес
     *
     * @param string $email Имейл адрес
     * @return bool|string True ако е валиден, иначе съобщение за грешка
     */
    public function validateEmail($email) {
        if (empty($email)) {
            return 'Имейл адресът е задължителен';
        }

        if (!$this->isValidEmail($email)) {
            return 'Невалиден имейл адрес';
        }

        return true;
    }

    /**
     * Валидира URL адрес
     *
     * @param string $url URL адрес
     * @param bool $required Дали е задължителен
     * @return bool|string True ако е валиден, иначе съобщение за грешка
     */
    public function validateUrl($url, $required = false) {
        if (empty($url)) {
            return $required ? 'URL адресът е задължителен' : true;
        }

        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            return 'Невалиден URL адрес';
        }

        return true;
    }

    /**
     * Валидира IP адрес
     *
     * @param string $ip IP адрес
     * @return bool|string True ако е валиден, иначе съобщение за грешка
     */
    public function validateIpAddress($ip) {
        if (empty($ip)) {
            return 'IP адресът е задължителен';
        }

        // Използваме метода от BaseHelper
        if ($this->isValidIp($ip)) {
            return true;
        }

        // Проверка за CIDR нотация
        if (preg_match('/^(\d{1,3}\.){3}\d{1,3}\/\d{1,2}$/', $ip)) {
            list($ipPart, $mask) = explode('/', $ip);
            if ($this->isValidIp($ipPart) && $mask >= 0 && $mask <= 32) {
                return true;
            }
        }

        return 'Невалиден IP адрес';
    }

    /**
     * Валидира телефонен номер
     *
     * @param string $phone Телефонен номер
     * @param bool $required Дали е задължителен
     * @return bool|string True ако е валиден, иначе съобщение за грешка
     */
    public function validatePhone($phone, $required = false) {
        if (empty($phone)) {
            return $required ? 'Телефонният номер е задължителен' : true;
        }

        // Премахваме всички символи освен цифри, +, -, (, ), пространства
        $cleanPhone = preg_replace('/[^\d\+\-\(\)\s]/', '', $phone);

        // Проверяваме дали има поне 7 цифри
        $digits = preg_replace('/[^\d]/', '', $cleanPhone);
        if (strlen($digits) < 7) {
            return 'Телефонният номер трябва да съдържа поне 7 цифри';
        }

        return true;
    }

    /**
     * Валидира API ключ
     *
     * @param string $apiKey API ключ
     * @param int $minLength Минимална дължина
     * @param int $maxLength Максимална дължина
     * @return bool|string True ако е валиден, иначе съобщение за грешка
     */
    public function validateApiKey($apiKey, $minLength = 10, $maxLength = 255) {
        if (empty($apiKey)) {
            return 'API ключът е задължителен';
        }

        if (strlen($apiKey) < $minLength) {
            return "API ключът трябва да бъде поне {$minLength} символа";
        }

        if (strlen($apiKey) > $maxLength) {
            return "API ключът не може да бъде повече от {$maxLength} символа";
        }

        // Проверка за валидни символи (букви, цифри, тире, долна черта)
        if (!preg_match('/^[a-zA-Z0-9\-_]+$/', $apiKey)) {
            return 'API ключът може да съдържа само букви, цифри, тире и долна черта';
        }

        return true;
    }

    /**
     * Валидира парола
     *
     * @param string $password Парола
     * @param int $minLength Минимална дължина
     * @return bool|string True ако е валидна, иначе съобщение за грешка
     */
    public function validatePassword($password, $minLength = 8) {
        if (empty($password)) {
            return 'Паролата е задължителна';
        }

        if (strlen($password) < $minLength) {
            return "Паролата трябва да бъде поне {$minLength} символа";
        }

        // Проверка за поне една главна буква
        if (!preg_match('/[A-Z]/', $password)) {
            return 'Паролата трябва да съдържа поне една главна буква';
        }

        // Проверка за поне една малка буква
        if (!preg_match('/[a-z]/', $password)) {
            return 'Паролата трябва да съдържа поне една малка буква';
        }

        // Проверка за поне една цифра
        if (!preg_match('/[0-9]/', $password)) {
            return 'Паролата трябва да съдържа поне една цифра';
        }

        return true;
    }

    /**
     * Валидира числова стойност
     *
     * @param mixed $value Стойност
     * @param float $min Минимална стойност
     * @param float $max Максимална стойност
     * @return bool|string True ако е валидна, иначе съобщение за грешка
     */
    public function validateNumeric($value, $min = null, $max = null) {
        if (!is_numeric($value)) {
            return 'Стойността трябва да бъде число';
        }

        $numValue = (float) $value;

        if ($min !== null && $numValue < $min) {
            return "Стойността трябва да бъде поне {$min}";
        }

        if ($max !== null && $numValue > $max) {
            return "Стойността не може да бъде повече от {$max}";
        }

        return true;
    }

    /**
     * Валидира цяло число
     *
     * @param mixed $value Стойност
     * @param int $min Минимална стойност
     * @param int $max Максимална стойност
     * @return bool|string True ако е валидно, иначе съобщение за грешка
     */
    public function validateInteger($value, $min = null, $max = null) {
        if (!is_numeric($value) || (int)$value != $value) {
            return 'Стойността трябва да бъде цяло число';
        }

        $intValue = (int) $value;

        if ($min !== null && $intValue < $min) {
            return "Стойността трябва да бъде поне {$min}";
        }

        if ($max !== null && $intValue > $max) {
            return "Стойността не може да бъде повече от {$max}";
        }

        return true;
    }

    /**
     * Валидира текстова стойност
     *
     * @param string $value Стойност
     * @param int $minLength Минимална дължина
     * @param int $maxLength Максимална дължина
     * @param bool $required Дали е задължителна
     * @return bool|string True ако е валидна, иначе съобщение за грешка
     */
    public function validateText($value, $minLength = 0, $maxLength = 255, $required = false) {
        if (empty($value)) {
            return $required ? 'Полето е задължително' : true;
        }

        $length = mb_strlen($value, 'UTF-8');

        if ($minLength > 0 && $length < $minLength) {
            return "Текстът трябва да бъде поне {$minLength} символа";
        }

        if ($maxLength > 0 && $length > $maxLength) {
            return "Текстът не може да бъде повече от {$maxLength} символа";
        }

        return true;
    }

    /**
     * Валидира масив от IP адреси
     *
     * @param array $ips Масив с IP адреси
     * @return bool|array True ако всички са валидни, иначе масив с грешки
     */
    public function validateIpArray($ips) {
        if (!is_array($ips)) {
            return 'IP адресите трябва да бъдат масив';
        }

        $errors = [];

        foreach ($ips as $index => $ip) {
            $validation = $this->validateIpAddress($ip);
            if ($validation !== true) {
                $errors[$index] = $validation;
            }
        }

        return empty($errors) ? true : $errors;
    }

    /**
     * Валидира JSON стринг
     *
     * @param string $json JSON стринг
     * @param bool $required Дали е задължителен
     * @return bool|string True ако е валиден, иначе съобщение за грешка
     */
    public function validateJson($json, $required = false) {
        if (empty($json)) {
            return $required ? 'JSON данните са задължителни' : true;
        }

        // Използваме метода от BaseHelper
        if (!$this->isValidJson($json)) {
            return 'Невалиден JSON формат: ' . json_last_error_msg();
        }

        return true;
    }

    /**
     * Валидира дата
     *
     * @param string $date Дата
     * @param string $format Формат на датата
     * @return bool|string True ако е валидна, иначе съобщение за грешка
     */
    public function validateDate($date, $format = 'Y-m-d') {
        if (empty($date)) {
            return 'Датата е задължителна';
        }

        $dateTime = \DateTime::createFromFormat($format, $date);

        if (!$dateTime || $dateTime->format($format) !== $date) {
            return "Невалидна дата. Очакван формат: {$format}";
        }

        return true;
    }
}
