<!-- CSS Styles for Tabs -->
<style>
.tab-button {
    border: none;
    background: none;
    cursor: pointer;
    transition: color 0.2s ease;
    border-bottom: 2px solid transparent;
}

.tab-button:hover {
    color: var(--primary-color, #6366f1) !important;
}

.tab-button.active {
    color: var(--primary-color, #6366f1) !important;
    border-bottom-color: var(--primary-color, #6366f1);
}

.tab-content {
    display: block;
}

.tab-content.hidden {
    display: none !important;
}

.animate-spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}
</style>

<!-- sub Header -->
<header class="bg-white border-b border-gray-200" style="margin-top: -2px; position: relative;">
    <div class="flex border-b border-gray-200">
        <button id="tab-basic" class="tab-button {{ current_tab == 'basic' ? 'active text-primary' : 'text-gray-600 hover:text-primary' }} px-6 py-3 whitespace-nowrap" data-tab="basic">Основни настройки</button>
        <button id="tab-security" class="tab-button {{ current_tab == 'security' ? 'active text-primary' : 'text-gray-600 hover:text-primary' }} px-6 py-3 whitespace-nowrap" data-tab="security">Сигурност</button>
        <button id="tab-payment" class="tab-button {{ current_tab == 'payment' ? 'active text-primary' : 'text-gray-600 hover:text-primary' }} px-6 py-3 whitespace-nowrap" data-tab="payment">Плащания</button>
        <button id="tab-delivery" class="tab-button {{ current_tab == 'delivery' ? 'active text-primary' : 'text-gray-600 hover:text-primary' }} px-6 py-3 whitespace-nowrap" data-tab="delivery">Доставка</button>
        <button id="tab-notifications" class="tab-button {{ current_tab == 'notifications' ? 'active text-primary' : 'text-gray-600 hover:text-primary' }} px-6 py-3 whitespace-nowrap" data-tab="notifications">Известия</button>
        <button id="tab-integrations" class="tab-button {{ current_tab == 'integrations' ? 'active text-primary' : 'text-gray-600 hover:text-primary' }} px-6 py-3 whitespace-nowrap" data-tab="integrations">Интеграции</button>
        <button id="tab-admin_users" class="tab-button {{ current_tab == 'admin_users' ? 'active text-primary' : 'text-gray-600 hover:text-primary' }} px-6 py-3 whitespace-nowrap" data-tab="admin_users">Админ потребители</button>
    </div>
</header>

<!-- Main Content Area -->
<main class="flex-1 overflow-y-auto p-6 bg-gray-50">
    <!-- Tab Contents -->
    <div class="tab-contents">
        <!-- Basic Settings Tab -->
        <div id="content-basic" class="tab-content {{ current_tab == 'basic' ? '' : 'hidden' }}">
            {% if current_tab == 'basic' %}
                {% include 'setting/tabs/basic.twig' %}
            {% else %}
                <div class="bg-white rounded shadow p-6">
                    <div class="text-center py-8">
                        <i class="ri-store-line text-4xl text-gray-400 mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Основни настройки</h3>
                        <p class="text-gray-500">Кликнете върху таба за да заредите настройките</p>
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- Security Settings Tab -->
        <div id="content-security" class="tab-content {{ current_tab == 'security' ? '' : 'hidden' }}">
            {% if current_tab == 'security' %}
                {% include 'setting/tabs/security.twig' %}
            {% else %}
                <div class="bg-white rounded shadow p-6">
                    <div class="text-center py-8">
                        <i class="ri-shield-line text-4xl text-gray-400 mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Настройки за сигурност</h3>
                        <p class="text-gray-500">Кликнете върху таба за да заредите настройките</p>
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- Payment Settings Tab -->
        <div id="content-payment" class="tab-content {{ current_tab == 'payment' ? '' : 'hidden' }}">
            {% if current_tab == 'payment' %}
                {% include 'setting/tabs/payment.twig' %}
            {% else %}
                <div class="bg-white rounded shadow p-6">
                    <div class="text-center py-8">
                        <i class="ri-bank-card-line text-4xl text-gray-400 mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Настройки за плащания</h3>
                        <p class="text-gray-500">Кликнете върху таба за да заредите настройките</p>
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- Delivery Settings Tab -->
        <div id="content-delivery" class="tab-content {{ current_tab == 'delivery' ? '' : 'hidden' }}">
            {% if current_tab == 'delivery' %}
                {% include 'setting/tabs/delivery.twig' %}
            {% else %}
                <div class="bg-white rounded shadow p-6">
                    <div class="text-center py-8">
                        <i class="ri-truck-line text-4xl text-gray-400 mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Настройки за доставка</h3>
                        <p class="text-gray-500">Кликнете върху таба за да заредите настройките</p>
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- Notifications Settings Tab -->
        <div id="content-notifications" class="tab-content {{ current_tab == 'notifications' ? '' : 'hidden' }}">
            {% if current_tab == 'notifications' %}
                {% include 'setting/tabs/notifications.twig' %}
            {% else %}
                <div class="bg-white rounded shadow p-6">
                    <div class="text-center py-8">
                        <i class="ri-notification-line text-4xl text-gray-400 mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Настройки за известия</h3>
                        <p class="text-gray-500">Кликнете върху таба за да заредите настройките</p>
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- Integrations Settings Tab -->
        <div id="content-integrations" class="tab-content {{ current_tab == 'integrations' ? '' : 'hidden' }}">
            {% if current_tab == 'integrations' %}
                {% include 'setting/tabs/integrations.twig' %}
            {% else %}
                <div class="bg-white rounded shadow p-6">
                    <div class="text-center py-8">
                        <i class="ri-links-line text-4xl text-gray-400 mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Интеграции</h3>
                        <p class="text-gray-500">Кликнете върху таба за да заредите настройките</p>
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- Admin Users Settings Tab -->
        <div id="content-admin_users" class="tab-content {{ current_tab == 'admin_users' ? '' : 'hidden' }}">
            {% if current_tab == 'admin_users' %}
                {% include 'setting/tabs/admin-users.twig' %}
            {% else %}
                <div class="bg-white rounded shadow p-6">
                    <div class="text-center py-8">
                        <i class="ri-admin-line text-4xl text-gray-400 mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Админ потребители</h3>
                        <p class="text-gray-500">Кликнете върху таба за да заредите настройките</p>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</main>

<!-- JavaScript Configuration -->
<script>
window.settingsConfig = {
    userToken: '{{ user_token }}',
    currentTab: '{{ current_tab }}',
    baseUrl: '{{ base_url }}',
    ajaxUrls: {
        basic_save: '{{ ajax_urls.basic_save ?? "" }}',
        upload_logo: '{{ ajax_urls.upload_logo ?? "" }}',
        test_email: '{{ ajax_urls.test_email ?? "" }}',
        security_update: '{{ ajax_urls.security_update ?? "" }}',
        payment_save: '{{ ajax_urls.payment_save ?? "" }}',
        test_payment: '{{ ajax_urls.test_payment ?? "" }}',
        update_currencies: '{{ ajax_urls.update_currencies ?? "" }}',
        toggle_payment_method: '{{ ajax_urls.toggle_payment_method ?? "" }}',
        delivery_save: '{{ ajax_urls.delivery_save ?? "" }}',
        test_shipping: '{{ ajax_urls.test_shipping ?? "" }}',
        calculate_shipping: '{{ ajax_urls.calculate_shipping ?? "" }}',
        toggle_shipping_method: '{{ ajax_urls.toggle_shipping_method ?? "" }}',
        notifications_save: '{{ ajax_urls.notifications_save ?? "" }}',
        test_email_settings: '{{ ajax_urls.test_email_settings ?? "" }}',
        test_sms: '{{ ajax_urls.test_sms ?? "" }}',
        preview_template: '{{ ajax_urls.preview_template ?? "" }}',
        send_test_notification: '{{ ajax_urls.send_test_notification ?? "" }}',
        reset_template: '{{ ajax_urls.reset_template ?? "" }}',
        integrations_save: '{{ ajax_urls.integrations_save ?? "" }}',
        test_integration: '{{ ajax_urls.test_integration ?? "" }}',
        toggle_integration: '{{ ajax_urls.toggle_integration ?? "" }}',
        generate_api_key: '{{ ajax_urls.generate_api_key ?? "" }}',
        sync_marketplace: '{{ ajax_urls.sync_marketplace ?? "" }}',
        add_webhook: '{{ ajax_urls.add_webhook ?? "" }}',
        remove_webhook: '{{ ajax_urls.remove_webhook ?? "" }}',
        test_webhook: '{{ ajax_urls.test_webhook ?? "" }}',
        admin_users_save: '{{ ajax_urls.admin_users_save ?? "" }}',
        add_user: '{{ ajax_urls.add_user ?? "" }}',
        edit_user: '{{ ajax_urls.edit_user ?? "" }}',
        delete_user: '{{ ajax_urls.delete_user ?? "" }}',
        toggle_user_status: '{{ ajax_urls.toggle_user_status ?? "" }}',
        reset_password: '{{ ajax_urls.reset_password ?? "" }}',
        unlock_user: '{{ ajax_urls.unlock_user ?? "" }}',
        add_user_group: '{{ ajax_urls.add_user_group ?? "" }}',
        edit_user_group: '{{ ajax_urls.edit_user_group ?? "" }}',
        delete_user_group: '{{ ajax_urls.delete_user_group ?? "" }}',
        update_permissions: '{{ ajax_urls.update_permissions ?? "" }}',
        test_security: '{{ ajax_urls.test_security ?? "" }}',
        export_activity_logs: '{{ ajax_urls.export_activity_logs ?? "" }}',
        clear_activity_logs: '{{ ajax_urls.clear_activity_logs ?? "" }}'
    }
};

</script>
