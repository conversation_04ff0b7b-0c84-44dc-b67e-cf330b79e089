<?php

namespace Theme25\Backend\Controller\Setting;

/**
 * Главен контролер за настройки на магазина
 *
 * Този контролер управлява всички операции свързани с настройки в административната част.
 * Следва sub-controller архитектурата на темата и предоставя методи за различни типове настройки.
 *
 * @package Theme25\Backend\Controller\Setting
 * <AUTHOR> Development Team
 * @version 1.0.0
 */
class Setting extends \Theme25\Controller {

    public function __construct($registry) {
        parent::__construct($registry, 'setting/setting');

        // Зареждане на JavaScript файлове специфични за този контролер
        $this->loadScripts();
    }

    /**
     * Зарежда необходимите JavaScript файлове
     */
    protected function loadScripts() {
        $this->addBackendScriptWithVersion([
            'settings-main.js',
            'image-manager.js'
        ], 'footer');
    }

    /**
     * Основен метод - показва настройките с табове
     */
    public function index() {
        $this->setTitle('Настройки на магазина');

        // Инициализиране на данните
        $this->initAdminData();

        try {
            $subController = $this->setBackendSubController('Setting/Setting/Index', $this);

            // Подготовка на данните
            $subController->prepareData();

            // Добавяне на основни данни ако липсват
            if (!isset($this->data['current_tab'])) {
                $this->setData('current_tab', $this->requestGet('tab', 'basic'));
            }

        } catch (Exception $e) {
            // Ако има грешка, задаваме минимални данни
            $this->setData([
                'current_tab' => 'basic',
                'tabs' => [
                    'basic' => 'Основни',
                    'security' => 'Сигурност',
                    'payment' => 'Плащания',
                    'delivery' => 'Доставка',
                    'notifications' => 'Известия',
                    'integrations' => 'Интеграции',
                    'admin_users' => 'Админ потребители'
                ]
            ]);
        }

        // Подготовка на всички AJAX URL-и за JavaScript
        $this->prepareAllAjaxUrls();

        // Рендиране на шаблона с данните от $this->data
        $this->renderTemplateWithDataAndOutput('setting/setting');
    }

    /**
     * Основни настройки
     */
    public function basic() {
        $this->setTitle('Основни настройки');

        // Инициализиране на данните
        $this->initAdminData();

        $subController = $this->setBackendSubController('Setting/Setting/Basic', $this);

        // Подготовка на данните
        $subController->prepareData();

        $this->renderTemplateWithDataAndOutput('setting/tabs/basic');
    }

    /**
     * Настройки за сигурност
     */
    public function security() {
        $this->setTitle('Настройки за сигурност');

        // Инициализиране на данните
        $this->initAdminData();

        $subController = $this->setBackendSubController('Setting/Setting/Security', $this);

        // Подготовка на данните
        $subController->prepareData();

        $this->renderTemplateWithDataAndOutput('setting/tabs/security');
    }

    /**
     * Настройки за плащания
     */
    public function payment() {
        $this->setTitle('Настройки за плащания');

        // Инициализиране на данните
        $this->initAdminData();

        $subController = $this->setBackendSubController('Setting/Setting/Payment', $this);

        // Подготовка на данните
        $subController->prepareData();

        $this->renderTemplateWithDataAndOutput('setting/tabs/payment');
    }

    /**
     * Настройки за доставка
     */
    public function delivery() {
        $this->setTitle('Настройки за доставка');

        // Инициализиране на данните
        $this->initAdminData();

        $subController = $this->setBackendSubController('Setting/Setting/Delivery', $this);

        // Подготовка на данните
        $subController->prepareData();

        $this->renderTemplateWithDataAndOutput('setting/tabs/delivery');
    }

    /**
     * Настройки за известия
     */
    public function notifications() {
        $this->setTitle('Настройки за известия');

        // Инициализиране на данните
        $this->initAdminData();

        $subController = $this->setBackendSubController('Setting/Setting/Notifications', $this);

        // Подготовка на данните
        $subController->prepareData();

        $this->renderTemplateWithDataAndOutput('setting/tabs/notifications');
    }

    /**
     * Настройки за интеграции
     */
    public function integrations() {
        $this->setTitle('Настройки за интеграции');

        // Инициализиране на данните
        $this->initAdminData();

        $subController = $this->setBackendSubController('Setting/Setting/Integrations', $this);

        // Подготовка на данните
        $subController->prepareData();

        $this->renderTemplateWithDataAndOutput('setting/tabs/integrations');
    }

    /**
     * Настройки за административни потребители
     */
    public function admin_users() {
        $this->setTitle('Административни потребители');

        // Инициализиране на данните
        $this->initAdminData();

        $subController = $this->setBackendSubController('Setting/Setting/AdminUsers', $this);

        // Подготовка на данните
        $subController->prepareData();

        $this->renderTemplateWithDataAndOutput('setting/tabs/admin-users');
    }

    /**
     * AJAX метод за запазване на основни настройки
     */
    public function basic_save() {
        $subController = $this->setBackendSubController('Setting/Setting/BasicSave', $this);
        $subController->execute();
    }

    /**
     * AJAX метод за качване на лого
     */
    public function upload_logo() {
        $subController = $this->setBackendSubController('Setting/Setting/UploadLogo', $this);
        $subController->execute();
    }

    /**
     * AJAX метод за тестване на имейл
     */
    public function test_email() {
        $subController = $this->setBackendSubController('Setting/Setting/TestEmail', $this);
        $subController->execute();
    }

    /**
     * AJAX метод за актуализиране на настройки за сигурност
     */
    public function security_update() {
        $subController = $this->setBackendSubController('Setting/Setting/SecurityUpdate', $this);
        $subController->execute();
    }

    /**
     * AJAX метод за тестване на IP адрес
     */
    public function test_ip() {
        $subController = $this->setBackendSubController('Setting/Setting/TestIP', $this);
        $subController->execute();
    }

    /**
     * AJAX метод за изчистване на неуспешни опити за вход
     */
    public function clear_failed_logins() {
        $subController = $this->setBackendSubController('Setting/Setting/ClearFailedLogins', $this);
        $subController->execute();
    }

    /**
     * AJAX метод за запазване на настройки за плащания
     */
    public function payment_save() {
        $subController = $this->setBackendSubController('Setting/Setting/PaymentSave', $this);
        $subController->execute();
    }

    /**
     * AJAX метод за тестване на payment метод
     */
    public function test_payment_method() {
        $subController = $this->setBackendSubController('Setting/Setting/TestPaymentMethod', $this);
        $subController->execute();
    }

    /**
     * AJAX метод за актуализиране на валути
     */
    public function update_currencies() {
        $subController = $this->setBackendSubController('Setting/Setting/UpdateCurrencies', $this);
        $subController->execute();
    }

    /**
     * AJAX метод за toggle на payment метод
     */
    public function toggle_payment_method() {
        $subController = $this->setBackendSubController('Setting/Setting/TogglePaymentMethod', $this);
        $subController->execute();
    }

    /**
     * AJAX метод за пренареждане на payment методи
     */
    public function reorder_payment_methods() {
        $subController = $this->setBackendSubController('Setting/Setting/ReorderPaymentMethods', $this);
        $subController->execute();
    }

    /**
     * AJAX метод за запазване на настройки за доставка
     */
    public function delivery_save() {
        $subController = $this->setBackendSubController('Setting/Setting/DeliverySave', $this);
        $subController->execute();
    }

    /**
     * AJAX метод за тестване на shipping метод
     */
    public function test_shipping_method() {
        $subController = $this->setBackendSubController('Setting/Setting/TestShippingMethod', $this);
        $subController->execute();
    }

    /**
     * AJAX метод за калкулиране на доставка
     */
    public function calculate_shipping() {
        $subController = $this->setBackendSubController('Setting/Setting/CalculateShipping', $this);
        $subController->execute();
    }

    /**
     * AJAX метод за toggle на shipping метод
     */
    public function toggle_shipping_method() {
        $subController = $this->setBackendSubController('Setting/Setting/ToggleShippingMethod', $this);
        $subController->execute();
    }

    /**
     * AJAX метод за пренареждане на shipping методи
     */
    public function reorder_shipping_methods() {
        $subController = $this->setBackendSubController('Setting/Setting/ReorderShippingMethods', $this);
        $subController->execute();
    }

    /**
     * AJAX метод за запазване на настройки за известия
     */
    public function notifications_save() {
        $subController = $this->setBackendSubController('Setting/Setting/NotificationsSave', $this);
        $subController->execute();
    }

    /**
     * AJAX метод за тестване на email настройки
     */
    public function test_email_settings() {
        $subController = $this->setBackendSubController('Setting/Setting/TestEmailSettings', $this);
        $subController->execute();
    }

    /**
     * AJAX метод за тестване на SMS настройки
     */
    public function test_sms_settings() {
        $subController = $this->setBackendSubController('Setting/Setting/TestSMSSettings', $this);
        $subController->execute();
    }

    /**
     * AJAX метод за preview на email шаблон
     */
    public function preview_email_template() {
        $subController = $this->setBackendSubController('Setting/Setting/PreviewEmailTemplate', $this);
        $subController->execute();
    }

    /**
     * AJAX метод за изпращане на тестово известие
     */
    public function send_test_notification() {
        $subController = $this->setBackendSubController('Setting/Setting/SendTestNotification', $this);
        $subController->execute();
    }

    /**
     * AJAX метод за възстановяване на email шаблон
     */
    public function reset_email_template() {
        $subController = $this->setBackendSubController('Setting/Setting/ResetEmailTemplate', $this);
        $subController->execute();
    }

    /**
     * AJAX метод за запазване на настройки за интеграции
     */
    public function integrations_save() {
        $subController = $this->setBackendSubController('Setting/Setting/IntegrationsSave', $this);
        $subController->execute();
    }

    /**
     * AJAX метод за тестване на интеграция
     */
    public function test_integration() {
        $subController = $this->setBackendSubController('Setting/Setting/TestIntegration', $this);
        $subController->execute();
    }

    /**
     * AJAX метод за toggle на интеграция
     */
    public function toggle_integration() {
        $subController = $this->setBackendSubController('Setting/Setting/ToggleIntegration', $this);
        $subController->execute();
    }

    /**
     * AJAX метод за генериране на API ключ
     */
    public function generate_api_key() {
        $subController = $this->setBackendSubController('Setting/Setting/GenerateApiKey', $this);
        $subController->execute();
    }

    /**
     * AJAX метод за синхронизиране на marketplace
     */
    public function sync_marketplace() {
        $subController = $this->setBackendSubController('Setting/Setting/SyncMarketplace', $this);
        $subController->execute();
    }

    /**
     * AJAX метод за добавяне на webhook
     */
    public function add_webhook() {
        $subController = $this->setBackendSubController('Setting/Setting/AddWebhook', $this);
        $subController->execute();
    }

    /**
     * AJAX метод за премахване на webhook
     */
    public function remove_webhook() {
        $subController = $this->setBackendSubController('Setting/Setting/RemoveWebhook', $this);
        $subController->execute();
    }

    /**
     * AJAX метод за тестване на webhook
     */
    public function test_webhook() {
        $subController = $this->setBackendSubController('Setting/Setting/TestWebhook', $this);
        $subController->execute();
    }

    /**
     * AJAX метод за запазване на настройки за админ потребители
     */
    public function admin_users_save() {
        $subController = $this->setBackendSubController('Setting/Setting/AdminUsersSave', $this);
        $subController->execute();
    }

    /**
     * AJAX метод за добавяне на админ потребител
     */
    public function add_admin_user() {
        $subController = $this->setBackendSubController('Setting/Setting/AddAdminUser', $this);
        $subController->execute();
    }

    /**
     * AJAX метод за редактиране на админ потребител
     */
    public function edit_admin_user() {
        $subController = $this->setBackendSubController('Setting/Setting/EditAdminUser', $this);
        $subController->execute();
    }

    /**
     * AJAX метод за изтриване на админ потребител
     */
    public function delete_admin_user() {
        $subController = $this->setBackendSubController('Setting/Setting/DeleteAdminUser', $this);
        $subController->execute();
    }

    /**
     * AJAX метод за toggle на статуса на потребител
     */
    public function toggle_user_status() {
        $subController = $this->setBackendSubController('Setting/Setting/ToggleUserStatus', $this);
        $subController->execute();
    }

    /**
     * AJAX метод за възстановяване на парола на потребител
     */
    public function reset_user_password() {
        $subController = $this->setBackendSubController('Setting/Setting/ResetUserPassword', $this);
        $subController->execute();
    }

    /**
     * AJAX метод за отключване на потребител
     */
    public function unlock_user() {
        $subController = $this->setBackendSubController('Setting/Setting/UnlockUser', $this);
        $subController->execute();
    }

    /**
     * AJAX метод за добавяне на група потребители
     */
    public function add_user_group() {
        $subController = $this->setBackendSubController('Setting/Setting/AddUserGroup', $this);
        $subController->execute();
    }

    /**
     * AJAX метод за редактиране на група потребители
     */
    public function edit_user_group() {
        $subController = $this->setBackendSubController('Setting/Setting/EditUserGroup', $this);
        $subController->execute();
    }

    /**
     * AJAX метод за изтриване на група потребители
     */
    public function delete_user_group() {
        $subController = $this->setBackendSubController('Setting/Setting/DeleteUserGroup', $this);
        $subController->execute();
    }

    /**
     * AJAX метод за актуализиране на права на достъп
     */
    public function update_permissions() {
        $subController = $this->setBackendSubController('Setting/Setting/UpdatePermissions', $this);
        $subController->execute();
    }

    /**
     * AJAX метод за тестване на настройки за сигурност
     */
    public function test_security() {
        $subController = $this->setBackendSubController('Setting/Setting/TestSecurity', $this);
        $subController->execute();
    }

    /**
     * AJAX метод за експорт на логове за активност
     */
    public function export_activity_logs() {
        $subController = $this->setBackendSubController('Setting/Setting/ExportActivityLogs', $this);
        $subController->execute();
    }

    /**
     * AJAX метод за изчистване на логове за активност
     */
    public function clear_activity_logs() {
        $subController = $this->setBackendSubController('Setting/Setting/ClearActivityLogs', $this);
        $subController->execute();
    }

    /**
     * Подготвя всички AJAX URL-и за JavaScript конфигурацията
     */
    private function prepareAllAjaxUrls() {
        $ajax_urls = [
            // Basic settings URLs
            'basic_save' => $this->getAdminLink('setting/setting/basic_save'),
            'upload_logo' => $this->getAdminLink('setting/setting/upload_logo'),
            'test_email' => $this->getAdminLink('setting/setting/test_email'),

            // Security settings URLs
            'security_update' => $this->getAdminLink('setting/setting/security_update'),
            'test_security' => $this->getAdminLink('setting/setting/test_security'),
            'test_ip' => $this->getAdminLink('setting/setting/test_ip'),
            'clear_failed_logins' => $this->getAdminLink('setting/setting/clear_failed_logins'),

            // Payment settings URLs
            'payment_save' => $this->getAdminLink('setting/setting/payment_save'),
            'test_payment' => $this->getAdminLink('setting/setting/test_payment_method'),
            'update_currencies' => $this->getAdminLink('setting/setting/update_currencies'),
            'toggle_payment_method' => $this->getAdminLink('setting/setting/toggle_payment_method'),
            'reorder_payment_methods' => $this->getAdminLink('setting/setting/reorder_payment_methods'),

            // Delivery settings URLs
            'delivery_save' => $this->getAdminLink('setting/setting/delivery_save'),
            'test_shipping' => $this->getAdminLink('setting/setting/test_shipping_method'),
            'calculate_shipping' => $this->getAdminLink('setting/setting/calculate_shipping'),
            'toggle_shipping_method' => $this->getAdminLink('setting/setting/toggle_shipping_method'),
            'reorder_shipping_methods' => $this->getAdminLink('setting/setting/reorder_shipping_methods'),

            // Notifications settings URLs
            'notifications_save' => $this->getAdminLink('setting/setting/notifications_save'),
            'test_email_settings' => $this->getAdminLink('setting/setting/test_email_settings'),
            'test_sms' => $this->getAdminLink('setting/setting/test_sms_settings'),
            'preview_template' => $this->getAdminLink('setting/setting/preview_email_template'),
            'send_test_notification' => $this->getAdminLink('setting/setting/send_test_notification'),
            'reset_template' => $this->getAdminLink('setting/setting/reset_email_template'),

            // Integrations settings URLs
            'integrations_save' => $this->getAdminLink('setting/setting/integrations_save'),
            'test_integration' => $this->getAdminLink('setting/setting/test_integration'),
            'toggle_integration' => $this->getAdminLink('setting/setting/toggle_integration'),
            'generate_api_key' => $this->getAdminLink('setting/setting/generate_api_key'),
            'sync_marketplace' => $this->getAdminLink('setting/setting/sync_marketplace'),
            'add_webhook' => $this->getAdminLink('setting/setting/add_webhook'),
            'remove_webhook' => $this->getAdminLink('setting/setting/remove_webhook'),
            'test_webhook' => $this->getAdminLink('setting/setting/test_webhook'),

            // Admin users settings URLs
            'admin_users_save' => $this->getAdminLink('setting/setting/admin_users_save'),
            'add_user' => $this->getAdminLink('setting/setting/add_admin_user'),
            'edit_user' => $this->getAdminLink('setting/setting/edit_admin_user'),
            'delete_user' => $this->getAdminLink('setting/setting/delete_admin_user'),
            'toggle_user_status' => $this->getAdminLink('setting/setting/toggle_user_status'),
            'reset_password' => $this->getAdminLink('setting/setting/reset_user_password'),
            'unlock_user' => $this->getAdminLink('setting/setting/unlock_user'),
            'add_user_group' => $this->getAdminLink('setting/setting/add_user_group'),
            'edit_user_group' => $this->getAdminLink('setting/setting/edit_user_group'),
            'delete_user_group' => $this->getAdminLink('setting/setting/delete_user_group'),
            'update_permissions' => $this->getAdminLink('setting/setting/update_permissions'),
            'export_activity_logs' => $this->getAdminLink('setting/setting/export_activity_logs'),
            'clear_activity_logs' => $this->getAdminLink('setting/setting/clear_activity_logs')
        ];

        $this->setData('ajax_urls', $ajax_urls);
        $this->setData('base_url', $this->getAdminLink('setting/setting'));
    }
}
